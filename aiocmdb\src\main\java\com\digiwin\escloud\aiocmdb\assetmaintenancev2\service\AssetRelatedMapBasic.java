package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoProduceType;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetNoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;

public abstract class AssetRelatedMapBasic {

    private final static String PREFIX_ASSET_CODE_CACHE_KEY = "asset:code:cache:%s:%s:%s";
    private final static String PREFIX_ASSET_CODE_MAX_CACHE_KEY = PREFIX_ASSET_CODE_CACHE_KEY + ":max";

    private enum AssetCategoryCodeRuleType {
        BASIC, ASSET, NONE;
    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String getAssetCodeGroupKey(String eid, String modeCode, String sinkName) {
        return String.format(PREFIX_ASSET_CODE_CACHE_KEY, modeCode, sinkName, eid);
    }
    private String getAssetCodeMaxGroupKey(String eid, String modeCode, String sinkName) {
        return String.format(PREFIX_ASSET_CODE_MAX_CACHE_KEY, modeCode, sinkName, eid);
    }

    /**
     * 刪除緩存中, 相關資產代碼
     *
     * @param modeCode  代表操作或配置模式的模式代碼。
     * @param sinkName  水槽名稱為資產代碼指定目標或接收器實體。
     * @param isRemoveAll 是否移除所有的資產編碼緩存
     */
    public void removeRangeAssetCodeCache(String modeCode, String sinkName, boolean isRemoveAll) {
        String assetCodeGroupRangeKeys = String.format("asset:code:cache:%s:%s*", modeCode, sinkName);
        if (isRemoveAll) {
            assetCodeGroupRangeKeys = "asset:code:cache:*";
        }
        Set<String> keys = stringRedisTemplate.keys(assetCodeGroupRangeKeys);
        if (!keys.isEmpty()) {
            stringRedisTemplate.delete(keys);
        }
    }

    public void removeRangeAssetCodeCache(String modeCode, String sinkName) {
        this.removeRangeAssetCodeCache(modeCode, sinkName, false);
    }

    /**
     * 刪除緩存中, 相關資產代碼
     *
     * @param modeCode  代表操作或配置模式的模式代碼。
     * @param sinkName  水槽名稱為資產代碼指定目標或接收器實體。
     */
    public void removeAssetCodeCache(String eid, String modeCode, String sinkName) {
        String assetCodeGroupRangeKey = getAssetCodeGroupKey(eid, modeCode, sinkName);
        stringRedisTemplate.delete(assetCodeGroupRangeKey);
    }

    private AssetCategoryCodeRuleType buildInventoryAssetCode(
            String eid, String modeCode, String sinkName,
            Supplier<Pair<String, List<AssetCategoryCodingRuleSimple>>> assetCodeRuleFunc,
            Function<Pair<String, List<AssetCategoryCodingRuleSimple>>, String> curFlowNumFunc
    ) {
        //避開拼發, 同時間產生編號, 如果己經有在建立, 則就是等
        String lockKey = "lock:" + getAssetCodeGroupKey(eid, modeCode, sinkName);
        Boolean lockAcquired = stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, "1", Duration.ofSeconds(3 * 60));

        if (Boolean.FALSE.equals(lockAcquired)) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待鎖時被中斷", e);
            }
            return AssetCategoryCodeRuleType.NONE;
        }

        try {
            return this.startBuildInventoryAssetCode(eid, modeCode, sinkName, assetCodeRuleFunc, curFlowNumFunc);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        } finally {
            stringRedisTemplate.delete(lockKey); // 釋放鎖
        }
    }

    private AssetCategoryCodeRuleType startBuildInventoryAssetCode(
            String eid, String modeCode, String sinkName,
            Supplier<Pair<String, List<AssetCategoryCodingRuleSimple>>> assetCodeRuleFunc,
            Function<Pair<String, List<AssetCategoryCodingRuleSimple>>, String> curFlowNumFunc
    ) {
        // 取出編碼規則, 跟編碼大類別
        Pair<String, List<AssetCategoryCodingRuleSimple>> codeRule = assetCodeRuleFunc.get();
        String mainCode = codeRule.getKey();
        List<AssetCategoryCodingRuleSimple> codingRuleSimples = codeRule.getValue();

        if (codingRuleSimples.isEmpty()) {
//            throw new RuntimeException("Not Found Asset Category Coding Rule Setting, modelCode:" + modelCode);
//            return Optional.empty(); // 有些資產是不用 assetCode, 所以如果查不到編碼規則, 就表示不需要
            return AssetCategoryCodeRuleType.BASIC;
        }

        if (this.hasInventoryAssetCodAssetCodes(eid, modeCode, sinkName)) {
            return AssetCategoryCodeRuleType.ASSET;
        }

        // 取得最大的資產編號
        if (curFlowNumFunc == null) {
            throw  new IllegalArgumentException("curFlowNumFunc must not be null");
        }
        String curFlowNum = this.getCacheMaxAssetCode(eid, modeCode, sinkName);
        //檢查緩存是否有最大編號, 如果沒有,再從DB取出
        if (curFlowNum.isEmpty()) {
            curFlowNum = curFlowNumFunc.apply(codeRule);
        }

        // 產生編號預備動作
        AssetNoUtil.getInstance().setAssetNoRuleInfo(codingRuleSimples, mainCode, curFlowNum);

        // 如果有設定流水號, 則依流水號動態計算, 若無則固定產生 1000 組的資產編號
        int produceCount = 1000;
        if (!codingRuleSimples.isEmpty()) {
            int[] snEncodingCounts = codingRuleSimples.stream()
                    .filter(row -> row.getRuleNumber() == AssetNoProduceType.SERIAL_NUMBER)
                    .map(AssetCategoryCodingRuleSimple::getRuleSettingValue)
                    .mapToInt(Integer::parseInt)
                    .toArray();
            if (snEncodingCounts.length > 0) {
                int snEncodingCount = snEncodingCounts[0];
                switch (snEncodingCount) {
                    case 1:
                        produceCount = 9;
                        break;
                    case 2:
                        produceCount = 99;
                        break;
                    case 3:
                        produceCount = 999;
                        break;
                }
            }
        }
        List<String> multiAssetNoList = AssetNoUtil.getInstance().getMultiAssetNo(
                codingRuleSimples.toArray(new AssetCategoryCodingRuleSimple[0]), produceCount
        );

        // save to redis
        this.cacheAssetCodes(eid, modeCode, sinkName, multiAssetNoList);

        return AssetCategoryCodeRuleType.ASSET;
    }

    private void cacheMaxAssetCode(String eid, String modeCode, String sinkName, String assetCode) {
        String assetCodeMaxGroupKey = this.getAssetCodeMaxGroupKey(eid, modeCode, sinkName);
        Long size = Optional.ofNullable(stringRedisTemplate.opsForList().size(assetCodeMaxGroupKey)).orElse(0L);
        if (size > 0) {
            stringRedisTemplate.opsForList().leftPop(assetCodeMaxGroupKey);
        }
        stringRedisTemplate.opsForList().rightPush(assetCodeMaxGroupKey, assetCode);
    }

    private String getCacheMaxAssetCode(String eid, String modeCode, String sinkName) {
        String assetCodeMaxGroupKey = this.getAssetCodeMaxGroupKey(eid, modeCode, sinkName);
        return Optional.ofNullable(stringRedisTemplate.opsForList().leftPop(assetCodeMaxGroupKey)).orElse("");
    }
    
    private void cacheAssetCodes(String eid, String modeCode, String sinkName, List<String> multiAssetNoList) {
        String assetCodeGroupKey = this.getAssetCodeGroupKey(eid, modeCode, sinkName);
        stringRedisTemplate.opsForList().rightPushAll(assetCodeGroupKey, multiAssetNoList);
    }

    private boolean hasInventoryAssetCodAssetCodes(String eid, String modeCode, String sinkName) {
        String assetCodeGroupKey = this.getAssetCodeGroupKey(eid, modeCode, sinkName);

        // Lua 腳本確保原子性
        String script = "return redis.call('LLEN', KEYS[1]) > 0 and 1 or 0";

        Object result = stringRedisTemplate.execute(
                RedisScript.of(script, Long.class),
                Collections.singletonList(assetCodeGroupKey)
        );

        if (result instanceof Long) {
            return (Long) result > 0;
        } else if (result instanceof Number) {
            return ((Number) result).longValue() > 0;
        }
        return false;
    }

    protected Optional<String> getCacheAssetCode(
            String eid, String modeCode, String sinkName,
            Supplier<Pair<String, List<AssetCategoryCodingRuleSimple>>> assetCodeRuleFunc,
            Function<Pair<String, List<AssetCategoryCodingRuleSimple>>, String> curFlowNumFunc,
            int retryCount
    ) {
        String assetCodeGroupKey = this.getAssetCodeGroupKey(eid, modeCode, sinkName);

        //step1. 從庫存取 assetCode
        String assetCode = stringRedisTemplate.opsForList().leftPop(assetCodeGroupKey);
        assetCode = Optional.ofNullable(assetCode).orElse("");
        if (!assetCode.isEmpty()) {
            this.cacheMaxAssetCode(eid, modeCode, sinkName, assetCode); //緩存當前最大資產編號
            return Optional.of(assetCode);
        }

        //step2. 檢查庫存, 如果沒有庫存, 則重建 assetCode 的庫存
        AssetCategoryCodeRuleType assetCategoryCodeRuleType = this.buildInventoryAssetCode(eid, modeCode, sinkName, assetCodeRuleFunc, curFlowNumFunc);
        if (AssetCategoryCodeRuleType.BASIC.equals(assetCategoryCodeRuleType)) {
            //檢查是不是categoryType = BASIC , 如果是, 則是不用產生任何的資產編號
            return Optional.empty();
        }

        if (retryCount <= 5) {
            retryCount++;
            return this.getCacheAssetCode(eid, modeCode, sinkName, assetCodeRuleFunc, curFlowNumFunc, retryCount);
        } else {
            String errMsg = String.format("get AssetCode error, eid:%s, modeCode:%s, sinkName:%s", eid, modeCode, sinkName);
            throw new RuntimeException(errMsg);
        }
    }
}
