package com.digiwin.escloud.aiocmdb.field.service;

import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSetMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;

import java.util.List;

public interface IFieldService {
    PageInfo<Field> getFieldList(String modelCode, String fieldSetCode, String search, String searchFieldCode,
                                 String fieldType, Boolean system, boolean existModel, boolean existFieldSet,
                                 int pageNum, int pageSize);

    List<Field> getSystemFieldList(String modelCode, Boolean system, Boolean hide);

    Field getFieldDetail(String fieldCode);

    BaseResponse addField(Field field);

    BaseResponse modifyField(Field field);

    BaseResponse checkField(String fieldCode);

    BaseResponse deleteField(long id);

    boolean sortField(List<ModelFieldMapping> modelFieldMappings);

    boolean sortFieldSetField(List<FieldSetMapping> fieldSetMappings);

    boolean saveFieldFormula(List<ModelFieldFomula> modelFieldFomulas);

    BaseResponse fieldTypeEnumSort(List<FieldTypeEnum> FieldTypeEnumList);

    BaseResponse formatField(List<ModelFieldMapping> modelFieldMappingList);

    List<Field> findFieldCodeList(List<String> fieldCodeList);

    List<FieldTypeEnum> getFieldTypeEnumByFieldCodes(List<String> fieldCodes);
}
