package com.digiwin.escloud.aioitms.instance.model;

import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.instance.AiopsBaseInstance;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel("合并统计明细信息")
@Data
public class MergeStatisticsDetailInfo extends AiopsBaseInstance {

    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("放置点")
    private String placementPoint;
    @ApiModelProperty("Ip地址")
    private String ipAddress;
    @ApiModelProperty("运维项目Id")
    private String aiopsItemId;

    @ApiModelProperty("运维项目群组")
    private String aiopsItemGroup;

    @ApiModelProperty("代理运维设备列表")
    private List<AiopsKitDevice> aiopsKitDeviceList;

    /**
     * 以下字段为了网安稽查使用
     */
    @ApiModelProperty("设备型号")
    //设备型号 DeviceAdvancedInfoAttributeModel.bios_family
    private String deviceModel;
    //数据库大小
    private String databaseSize;
    // 数据库版本
    private String databaseVersion;

    @ApiModelProperty("關聯資產")
    private int isRelated;
    private String assetCode;
    private String assetName;

    private AssetAiopsInstance assetAiopsInstance;


}
