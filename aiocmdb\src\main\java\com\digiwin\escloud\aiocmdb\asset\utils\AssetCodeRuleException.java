package com.digiwin.escloud.aiocmdb.asset.utils;

import lombok.Getter;

public class AssetCodeRuleException extends RuntimeException {
    @Getter
    private final AssetNoProduceType errRuleType;

    @Getter
    private final String errRuleMsg;

    @Getter
    private String errCode;

    public AssetCodeRuleException(AssetNoProduceType errRuleType, String errRuleMsg, int errCode) {
        super(errRuleMsg);
        this.errRuleType = errRuleType;
        this.errRuleMsg = errRuleMsg;
        this.errCode = errCode + "";
    }

}
