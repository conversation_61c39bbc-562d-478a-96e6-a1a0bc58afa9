package com.digiwin.escloud.issueservice.v3.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.issueservice.cache.KmoCache;
import com.digiwin.escloud.issueservice.common.IndepthAICommon;
import com.digiwin.escloud.issueservice.t.model.issuekb.ChatMessage;
import com.digiwin.escloud.issueservice.t.model.issuekb.ChatMessageV2;
import com.digiwin.escloud.issueservice.utils.BigDataUtil;
import com.digiwin.escloud.issueservice.v3.dao.IIssueSchedulingDaoV3;
import com.digiwin.escloud.issueservice.v3.service.IAIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class AIServiceImpl extends IndepthAICommon implements IAIService {
    @Value("${digiwin.issueservice.defaultSid:241199971893824}")
    Long defaultSid;
    @Value("${digiwin.issue.connectarea}")
    private String connectArea;
    @Value("${search.indepthai.code}")
    private String searchIndepthAICode;

    @Value("${skc.apptoken}")
    private String skcAppToken;//用于接口调用的身份验证，可通过【我的订单-查看访问牌】获取访问令牌

    @Value("${skc.appcode}")
    private String skcAppCode;//用于应用的身份验证，即应用AppToken

    @Resource(name = "indepthAIWebClient")
    private WebClient webClient;
    @Value("${indepth.ai.url:https://kai-skc-test.apps.digiwincloud.com.cn/assistant/api}")
    private String indepthAiUrl;
    @Autowired
    KmoCache kmoCache;
    @Autowired
    IIssueSchedulingDaoV3 iIssueSchedulingDaoV3;
    @Autowired
    private BigDataUtil bigDataUtil;

    public Flux<ServerSentEvent<Object>> generateTextStream(String askMessage, String askSessionId) {

        // 要傳的參數.
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setQuestion(askMessage);
        chatMessage.setSessionId(askSessionId);
        Map<String,Object> mapB = new HashMap<>();
        mapB.put("sessionId",askSessionId);
        mapB.put("question",askMessage);

        Map<String, Object> mapP = new HashMap<>();
        mapP.put("id", SnowFlake.getInstance().newId());
        mapP.put("startSearchTime", DateUtil.getSomeDateFormatString(DateUtil.getLocalNow(), DateUtil.DATE_TIME_FORMATTER));
        mapP.put("sid", defaultSid);
        Map map = JSONObject.parseObject(askMessage,Map.class);
        mapP.put("source", map.getOrDefault("source","").toString());
        mapP.put("aiSource", map.getOrDefault("aiSource","").toString());
        mapP.put("productCode", map.getOrDefault("productCode","").toString());
        mapP.put("serviceRegion", map.getOrDefault("serviceRegion","").toString());
        mapP.put("searchContent", map.getOrDefault("searchContent","").toString());
        mapP.put("callUrl", indepthAiUrl+"/"+getIndepthAICode());
        mapP.put("callType", HttpMethod.POST);
        //日志里面传body
        mapP.put("APIbody", mapB);

        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Accel-Buffering", "no");
        headers.set("token", kmoCache.getChatFileAuth());
        headers.set("digi-middleware-auth-app", skcAppCode);//用于应用的身份验证，即应用AppToken
        headers.set("digi-skc-app-token", skcAppToken);//用于接口调用的身份验证，可通过【我的订单-查看访问牌】获取访问令牌
        headers.set("Content-Type", "application/json");
        headers.set("Accept-Language", "CN".equals(connectArea) ? "zh-CN" : "zh-TW");

        //日志里面传请求头
        HashMap<String,Object> srParam = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        headers.forEach((key, values) ->
                headerMap.put(key, String.join(",", values)));
        srParam.put("head",headerMap);
        mapP.put("APIparam", srParam);

        chatMessage.setAiCallLog(mapP);

        Flux<ServerSentEvent<Object>> flux = webClient.post()
                .uri("/" + getIndepthAICode())
                .headers(httpHeaders -> httpHeaders.addAll(headers))
                .bodyValue(chatMessage)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<Object>>() {});
        return this.packageFlowProcess(chatMessage, flux);
    }


    private String getIndepthAICode(){
        return searchIndepthAICode;
    }

    @Override
    protected void callApiError(Throwable error, String askMessage) {
        //TODO call api error
        if (error instanceof WebClientResponseException) {
            WebClientResponseException wcrErr = (WebClientResponseException) error;
            System.out.println(wcrErr.getResponseBodyAsString());
        } else {
            System.out.println(error.getMessage());
        }
    }

    @Override
    protected void saveMsg(Object procDomainObject, String fullFlowMsg, String msgSourceList, Throwable error, ChatMessage chatMessage) {
        //TODO process msg has error
        this.saveMsg(procDomainObject,null , fullFlowMsg, msgSourceList,chatMessage );
    }

    @Override
    protected void saveMsg(Object procDomainObject, Object procDomainDoneObject, String fullFlowMsg, String msgSourceList, ChatMessage chatMessage) {
//        System.out.println(fullFlowMsg);
        List<Map<String, Object>> edrOperateLogList = new ArrayList<>();
        Map<String,Object> chatfileCallLog = chatMessage.getAiCallLog();
        chatfileCallLog.put("resultContent",fullFlowMsg);
        chatfileCallLog.put("respondTime",DateUtil.getSomeDateFormatString(DateUtil.getLocalNow(), DateUtil.DATE_TIME_FORMATTER));

        UploadBigDataContext uploadBigDataContext = new UploadBigDataContext(null, "", null, null, "chatfileCallLog", false, chatfileCallLog);
        edrOperateLogList.addAll(bigDataUtil.createParentUploadStruct(uploadBigDataContext));

        bigDataUtil.simulateLocalUploadData(edrOperateLogList);

    }

    @Override
    protected Object getProcessDomainObject(ChatMessage chatMessage, Object recMsg, String eventId) {
        Map<String, Object> flowRow = (Map<String, Object>) recMsg;

//        String returnSampleJson = "{\n" +
//                "    \"action\": \"answer\",\n" +
//                "    \"blockId\": \"4e8fd224-1c77-402a-8501-33fd6603e57f\",\n" +
//                "    \"message\": \"昨天上午 10:30 提的案件:111199 的问题处理好了吗？什么时候给我打电话?\\n{<br>  \\\"eid\\\": 99990000,<br>  \\\"deviceId\\\": 8759487583,<br>  \\\"userId\\\": \\\"asam_user_id\\\",<br>  \\\"case_no\\\": [\\\"111199\\\"],<br>  \\\"query_start_date\\\": \\\"2025-04-10 10:30:00\\\",<br>  \\\"query_end_date\\\": \\\"2025-04-10 10:30:00\\\"<br>}\",\n" +
//                "    \"messageSource\": \"text_reply\",\n" +
//                "    \"nodeId\": \"7b26b6c3-6bde-4b93-b6ba-a4d72c8b5cc5\",\n" +
//                "    \"sessionId\": \"91539a51-e6cb-4886-b2be-070fe3eaffc1\",\n" +
//                "    \"status\": \"done\"\n" +
//                "}";

        String action = this.getReturnFlowValue("action", flowRow);
        String blockId = this.getReturnFlowValue("blockId", flowRow);
        String message = this.getReturnFlowValue("message", flowRow);
        String messageSource = this.getReturnFlowValue("messageSource", flowRow);
        String nodeId = this.getReturnFlowValue("nodeId", flowRow);
        String sessionId = this.getReturnFlowValue("sessionId", flowRow);
        String status = this.getReturnFlowValue("status", flowRow);

        ChatMessageV2 chatMsgRow = new ChatMessageV2();

        // basic info
//        chatMsgRow.setId(); // 目前不確定用哪一個值
//        chatMsgRow.setSid();
//        chatMsgRow.setUserSid();
//        chatMsgRow.setUserId();

        // user ask
        chatMsgRow.setQuestion(chatMessage.getQuestion());

        // from ai reply
        chatMsgRow.setSessionId(sessionId);
        chatMsgRow.setBlockId(blockId);
        chatMsgRow.setNodeId(nodeId);
        chatMsgRow.setStatus(status);

        // system info
//        chatMsgRow.setSender();
//        chatMsgRow.setReceiver();
//        chatMsgRow.setCreateTime();
//        chatMsgRow.setUpdateTime();
        return chatMsgRow;
    }
}