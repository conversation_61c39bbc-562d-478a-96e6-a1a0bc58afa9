package com.digiwin.escloud.aioitms.report.model.serviceReport;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.digiwin.escloud.common.model.UnitType;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Date 2023/7/7 15:05
 * @Created yanggld
 * @Description
 */
@Data
public class ProductServiceReportGenerateReqDTO extends ProductServiceReportModel {
    private String db = "escloud-db";
    private String dataStartTime;
    private String dataEndTime;
    private String dataStartDateStr;
    private String dataStartYMStr;
    private String dataEndDateStr;
    private String dataEndYMStr;
    private String language = "zh_CN";
    private String[] analysisProjectCodes;
    private String productLine;

    //E10 sqlserver体检报告需要
    private int groupInterval;
    private UnitType unitType;
    private String dbType;
    private String deviceId;
    private String dbInstanceName;
    private String dbServerName;
    private String dbId;
    private List<String> deviceIdList;
    private String deviceNames;
    private String tempProductCode;
    // 异步调用aioai得api需要传入得参数
    private String token;
    private String acceptLanguage;
    private Long headerEid ;
    private Long headerSid ;


    public String getDataStartDateStr() {
        if (StringUtils.isEmpty(dataStartDateStr)) {
            LocalDate dataStartDate = getDataStartDate();
            String dateStr = LocalDateTimeUtil.format(dataStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            this.dataStartDateStr = dateStr;
        }
        return dataStartDateStr;
    }

    public String getDataEndDateStr() {
        if (StringUtils.isEmpty(dataEndDateStr)) {
            LocalDate dataEndDate = getDataEndDate();
            String dateStr = LocalDateTimeUtil.format(dataEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            this.dataEndDateStr = dateStr;
        }
        return dataEndDateStr;
    }

    public String getDataStartTime() {
        if (StringUtils.isEmpty(dataStartTime)) {
            LocalDate dataStartDate = getDataStartDate();
            String dateStr =  LocalDateTimeUtil.format(dataStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
            this.dataStartTime = dateStr;
        }
        return dataStartTime;
    }

    public String getDataEndTime() {
        if (StringUtils.isEmpty(dataEndTime)) {
            LocalDate dataEndDate = getDataEndDate();
            String dateStr = LocalDateTimeUtil.format(dataEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))+ " 23:59:59";
            this.dataEndTime = dateStr;
        }
        return dataEndTime;
    }

    public String getDataStartYMStr() {
        if (StringUtils.isEmpty(dataStartYMStr)) {
            LocalDate dataStartDate = getDataStartDate();
            String dateStr = LocalDateTimeUtil.format(dataStartDate, DateTimeFormatter.ofPattern("yyyy-MM"));
            this.dataStartYMStr = dateStr;
        }
        return dataStartYMStr;
    }

    public String getDataEndYMStr() {
        if (StringUtils.isEmpty(dataEndYMStr)) {
            LocalDate dataEndDate = getDataEndDate();
            String dateStr = LocalDateTimeUtil.format(dataEndDate, DateTimeFormatter.ofPattern("yyyy-MM"));
            this.dataEndYMStr = dateStr;
        }
        return dataEndYMStr;
    }
}
