package com.digiwin.escloud.aioitms.report.service.serviceReprot;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiochat.model.IssueDescRequest;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.escloud.dao.EscloudMapper;
import com.digiwin.escloud.aioitms.escloud.model.InformationQuestionPO;
import com.digiwin.escloud.aioitms.escloud.model.QuestionSummaryPO;
import com.digiwin.escloud.aioitms.escloud.model.TQuestionClassificationPO;
import com.digiwin.escloud.aioitms.properties.AioItmsProperties;
import com.digiwin.escloud.aioitms.report.dao.ProductServiceReportMapper;
import com.digiwin.escloud.aioitms.report.model.serviceReport.*;
import com.digiwin.escloud.aioitms.report.model.serviceReport.po.RatePO;
import com.digiwin.escloud.common.feign.AioAiFeignClient;
import com.digiwin.escloud.common.feign.AioChatFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.integration.api.gpt.RetryCallback;
import com.digiwin.escloud.integration.api.gpt.resp.ChatGPTResponse;
import com.digiwin.escloud.integration.service.gpt.IChatGPTService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Date 2023/7/19 11:26
 * @Created yanggld
 * @Description
 */
@Slf4j
@Service
public class TProductServiceReportService {

    /**
     * T系列产品报告版本，为了兼容旧报告使用，版本大于等于设定得版本 就展示新字段，小于就展示旧字段 目前设定跟随代码分支走
     */
    private static final Double T_REPORT_VERSION = 1.51;

    @Autowired
    private ProductServiceReportMapper productServiceReportMapper;
    @Autowired
    private EscloudMapper escloudMapper;
    @Autowired
    private TProductServiceReportEsService tProductServiceReportEsService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private AioItmsProperties aioItmsProperties;
    @Autowired
    @Qualifier("IChatGPTService")
    private IChatGPTService chatGPTService;
    @Autowired
    private ProductServiceReportBigDataService reportBigDataService;
    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private AioAiFeignClient aioAiFeignClient;


    private Integer retryCount = 3;
    private Integer retryIntervalSecond = 10;
    private String token = "7bb472a3-b51c-42ea-81c5-c08ca2d0248b";


    public void saveTProductServiceReport(ProductServiceReportGenerateReqDTO model) throws Exception {
        model.setHeaderSid(RequestUtil.getHeaderSid());
        model.setHeaderEid(RequestUtil.getHeaderEid());
        model.setToken(RequestUtil.getHeaderToken());
        model.setAcceptLanguage(RequestUtil.getAcceptLanguage());
        TProductServiceReport report = new TProductServiceReport();
        report.setId(Long.toString(model.getId()));
        report.setReportName(model.getCustomerFullName() +model.getProductName()+ "产品服务报告");
        tProductServiceReportEsService.save(report, true);
        new Thread(() -> {
            try {
                saveTProductServiceReport2Es(report, model);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    private int resultSize(Class clazz) {
        int resultSize = 0;
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            String name = method.getName();
            if (name.endsWith("Result")) {
                resultSize++;
            }
        }
        return resultSize;
    }

    private void saveTProductServiceReport2Es(TProductServiceReport report, ProductServiceReportGenerateReqDTO model) throws InterruptedException {
        String eid = String.valueOf(model.getHeaderEid());
        String sid = String.valueOf(model.getHeaderSid());
        String requestToken = model.getToken();
        String acceptLanguage = model.getAcceptLanguage();
        int tCount = resultSize(TProductServiceReport.class) / 2;
        List<ProductServiceReportAccountModel> accountList = model.getAccountList();
        int tAccountCount = resultSize(TProductServiceReportAccount.class) / 2;
        CountDownLatch countDownLatch = new CountDownLatch(tCount+tAccountCount*accountList.size());
        report.setCustomerName(model.getCustomerName());
        LocalDate dataEndDate = model.getDataEndDate();
        report.setYear(LocalDateTimeUtil.format(dataEndDate, DateTimeFormatter.ofPattern("yyyy")));
        report.setDataStartDate(model.getDataStartDate());
        report.setDataEndDate(model.getDataEndDate());
        report.setGenerateTime(model.getGenerateTime());
        report.setEntName(model.getEntName());
        report.setEntCode(model.getEntCode());
        /**
         * 问题量趋势分析
         */
        taskExecutor.submit(() -> {
            List<QuestionSummaryPO> questionSummaryData = escloudMapper.getTQuestionSummary(model);
            if (!CollectionUtils.isEmpty(questionSummaryData)) {
                report.setQuestionTrendAnalysisData(questionSummaryData);
                if (aioItmsProperties.isProductServiceReportSend()) {
                    StringBuilder sb = new StringBuilder();
                    String question = QuestionSummaryPO.getQuestion(questionSummaryData);
                    sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                    Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                    responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                        @Override
                        public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                            log.info("==========questionSummaryData===========");
                            if (log.isDebugEnabled()) {
                                log.debug(sb.toString());
                            }
                            log.info("-->{}", response.body());
                            report.setQuestionTrendAnalysisResult(response.body().getData());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                            log.error("questionCloseDataFail:{}", t.getMessage());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleRetry(Call<ChatGPTResponse> call) {
                            log.warn("questionCloseDataRetry:{}", LocalDateTime.now());
                        }
                    });
                } else {
                    countDownLatch.countDown();
                }
            } else {
                countDownLatch.countDown();
            }
        });

        /**
         * 年度培训课程安排及参与总结,同时兼容旧报告保持不变
         */
        taskExecutor.submit(() -> {
            List<Map<String, Object>> trainCourseList = getTrainCourse(model);
            if (!CollectionUtils.isEmpty(trainCourseList)) {
                report.setYearTrainCourseArrangementData(trainCourseList);
                report.setYearTrainCourseArrangementResult("");
            }
            countDownLatch.countDown();
        });

        /**
         * 问题归类分析
         */
        taskExecutor.submit(() -> {
            List<TQuestionClassificationPO> tQuestionModuleData = escloudMapper.getTQuestionModule(model);
            if (!CollectionUtils.isEmpty(tQuestionModuleData)) {
                report.setQuestionModuleAnalysisData(tQuestionModuleData);
                if (aioItmsProperties.isProductServiceReportSend()) {
                    StringBuilder sb = new StringBuilder();
                    String question = TQuestionClassificationPO.getQuestion(tQuestionModuleData);
                    sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                    Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                    responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                        @Override
                        public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                            log.info("==========tQuestionModuleData===========");
                            if (log.isDebugEnabled()) {
                                log.debug(sb.toString());
                            }
                            log.info("-->{}", response.body().getData());
                            report.setQuestionModuleAnalysisResult(response.body().getData());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                            log.error("tQuestionModuleDataFail:{}", t.getMessage());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleRetry(Call<ChatGPTResponse> call) {
                            log.warn("tQuestionModuleDataRetry:{}", LocalDateTime.now());
                        }
                    });
                } else {
                    countDownLatch.countDown();
                }
            } else {
                countDownLatch.countDown();
            }
        });


        /**
         * 问题类型分析数据
         */
        taskExecutor.submit(() -> {
            List<TQuestionClassificationPO> tQuestionClassificationData = escloudMapper.getTQuestionClassification(model);
            if (!CollectionUtils.isEmpty(tQuestionClassificationData)) {
                report.setQuestionClassificationData(tQuestionClassificationData);
                if (aioItmsProperties.isProductServiceReportSend()) {
                    StringBuilder sb = new StringBuilder();
                    String question = TQuestionClassificationPO.getQuestion(tQuestionClassificationData);
                    sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                    Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                    responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                        @Override
                        public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                            log.info("==========tQuestionClassificationData===========");
                            if (log.isDebugEnabled()) {
                                log.debug(sb.toString());
                            }
                            log.info("-->{}", response.body().getData());
                            report.setQuestionClassificationAnalysisResult(response.body().getData());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                            log.error("tQuestionClassificationDataFail:{}", t.getMessage());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleRetry(Call<ChatGPTResponse> call) {
                            log.warn("tQuestionClassificationDataRetry:{}", LocalDateTime.now());
                        }
                    });
                } else {
                    countDownLatch.countDown();
                }
            } else {
                countDownLatch.countDown();
            }
        });

        List<TProductServiceReportAccount> tProductServiceReportAccountList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (ProductServiceReportAccountModel accountModel : accountList) {
                TProductServiceReportAccount reportAccount = new TProductServiceReportAccount();
                reportAccount.setAccountName(accountModel.getAccountName());
                reportAccount.setAccountCode(accountModel.getAccountCode());
                tProductServiceReportAccountList.add(reportAccount);
                /**
                 * 制单及时性分析
                 */
                taskExecutor.submit(() -> {
                    List<DocTimelyPO> tDocTimelyData = reportBigDataService.getTDocTimely(model, reportAccount);
                    Map<String, List<DocTimelyPO>> moduleNameMap = tDocTimelyData.stream().collect(Collectors.groupingBy(DocTimelyPO::getModuleName));
                    StringBuilder docTimelySb = new StringBuilder();
                    List<TProductServiceReportAccount.DocTimelyGroupData> docTimelyGroupDataList = new ArrayList<>();
                    for (Map.Entry<String, List<DocTimelyPO>> entry : moduleNameMap.entrySet()) {
                        String moduleNameKey = entry.getKey();
                        List<DocTimelyPO> moduleNameValList = entry.getValue();
                        if (!CollectionUtils.isEmpty(moduleNameValList)) {
                            String question = DocTimelyPO.getQuestionV2(moduleNameKey,moduleNameValList);
                            docTimelySb.append(question);
                        }
                        TProductServiceReportAccount.DocTimelyGroupData docTimelyGroupData = new TProductServiceReportAccount.DocTimelyGroupData(moduleNameKey,moduleNameValList);
                        docTimelyGroupDataList.add(docTimelyGroupData);
                    }
                    reportAccount.setDocTimelyGroupData(docTimelyGroupDataList);
                    if (aioItmsProperties.isProductServiceReportSend() && !StringUtils.isEmpty(docTimelySb.toString())) {
                        docTimelySb.append(ChatGPTQuestion.getDefaultRemark());
                        Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, docTimelySb.toString());
                        responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                            @Override
                            public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                                log.info("==========tDocTimelyData===========");
                                if (log.isDebugEnabled()) {
                                    log.debug(docTimelySb.toString());
                                }
                                log.info("-->{}", response.body().getData());
                                reportAccount.setDocTimelyAnalysisResult(response.body().getData());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                                log.error("tDocTimelyDataFail:{}", t.getMessage());
                                countDownLatch.countDown();
                            }

                            @Override
                            public void handleRetry(Call<ChatGPTResponse> call) {
                                log.warn("tDocTimelyDataRetry:{}", LocalDateTime.now());
                            }
                        });
                    } else {
                        countDownLatch.countDown();
                    }
                });

                /**
                 * 库存扣账及时率
                 */
                taskExecutor.submit(() -> {
                    List<RatePO> tInventoryDeductionTimelyData = reportBigDataService.getTInventoryDeductionTimely(model, reportAccount);
                    if (!CollectionUtils.isEmpty(tInventoryDeductionTimelyData)) {
                        reportAccount.setInventoryDeductionTimelyData(tInventoryDeductionTimelyData);
                        if (aioItmsProperties.isProductServiceReportSend()) {
                            StringBuilder sb = new StringBuilder();
                            String question = RatePO.getQuestion(tInventoryDeductionTimelyData);
                            sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                            Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                            responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                                @Override
                                public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                                    log.info("==========tInventoryDeductionTimelyData===========");
                                    if (log.isDebugEnabled()) {
                                        log.debug(sb.toString());
                                    }
                                    log.info("-->{}", response.body().getData());
                                    reportAccount.setInventoryDeductionTimelyAnalysisResult(response.body().getData());
                                    countDownLatch.countDown();
                                }

                                @Override
                                public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                                    log.error("tInventoryDeductionTimelyDataFail:{}", t.getMessage());
                                    countDownLatch.countDown();
                                }

                                @Override
                                public void handleRetry(Call<ChatGPTResponse> call) {
                                    log.warn("tInventoryDeductionTimelyDataRetry:{}", LocalDateTime.now());
                                }
                            });
                        } else {
                            countDownLatch.countDown();
                        }
                    } else {
                        countDownLatch.countDown();
                    }
                });

                /**
                 * 月结周期分析数据
                 */
                taskExecutor.submit(() -> {
                    List<MonthlySettlementPeriodPO> monthlySettlementPeriodData = reportBigDataService.getMonthlySettlementPeriod(model, reportAccount);
                    if (!CollectionUtils.isEmpty(monthlySettlementPeriodData)) {
                        reportAccount.setMonthlySettlementPeriodData(monthlySettlementPeriodData);
                        if (aioItmsProperties.isProductServiceReportSend()) {
                            StringBuilder sb = new StringBuilder();
                            String question = MonthlySettlementPeriodPO.getQuestion(monthlySettlementPeriodData);
                            sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                            Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                            responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                                @Override
                                public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                                    log.info("==========monthlySettlementPeriodData===========");
                                    if (log.isDebugEnabled()) {
                                        log.debug(sb.toString());
                                    }
                                    log.info("-->{}", response.body().getData());
                                    reportAccount.setMonthlySettlementPeriodAnalysisResult(response.body().getData());
                                    countDownLatch.countDown();
                                }

                                @Override
                                public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                                    log.error("monthlySettlementPeriodDataFail:{}", t.getMessage());
                                    countDownLatch.countDown();
                                }

                                @Override
                                public void handleRetry(Call<ChatGPTResponse> call) {
                                    log.warn("monthlySettlementPeriodDataRetry:{}", LocalDateTime.now());
                                }
                            });
                        } else {
                            countDownLatch.countDown();
                        }
                    } else {
                        countDownLatch.countDown();
                    }
                });
            }
        }
        report.setAccountList(tProductServiceReportAccountList);

        /**
         * 资讯类分析数据
         */
        taskExecutor.submit(() -> {
            List<InformationQuestionPO> informationQuestionData = escloudMapper.getInformationQuestionPO(model);
            if (!CollectionUtils.isEmpty(informationQuestionData)) {
                report.setInformationQuestionData(informationQuestionData);
                if (aioItmsProperties.isProductServiceReportSend()) {
                    StringBuilder sb = new StringBuilder();
                    String question = InformationQuestionPO.getQuestion(informationQuestionData);
                    sb.append(question).append(ChatGPTQuestion.getDefaultRemark());
                    Call<ChatGPTResponse> responseCall = chatGPTService.getChatGPTContent(token, sb.toString());
                    responseCall.enqueue(new RetryCallback<ChatGPTResponse>(responseCall, retryCount, retryIntervalSecond) {
                        @Override
                        public void handleResponse(Call<ChatGPTResponse> call, Response<ChatGPTResponse> response) {
                            log.info("==========informationQuestionData===========");
                            if (log.isDebugEnabled()) {
                                log.debug(sb.toString());
                            }
                            log.info("-->{}", response.body().getData());
                            report.setInformationQuestionAnalysisResult(response.body().getData());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleFail(Call<ChatGPTResponse> call, Throwable t) {
                            log.error("informationQuestionDataFail:{}", t.getMessage());
                            countDownLatch.countDown();
                        }

                        @Override
                        public void handleRetry(Call<ChatGPTResponse> call) {
                            log.warn("informationQuestionDataRetry:{}", LocalDateTime.now());
                        }
                    });
                } else {
                    countDownLatch.countDown();
                }
            } else {
                countDownLatch.countDown();
            }
        });
        // T新增案件总结 有deepseek智能体返回结论
        taskExecutor.submit(() -> {
            try {
                IssueDescRequest request = new IssueDescRequest();
                request.setStartTime(model.getDataStartTime());
                request.setEndTime(model.getDataEndTime());
                request.setProductCode(model.getProductCode());
                request.setServiceCode(model.getServiceCode());
                log.info("getIssueDescForReport:eid={},sid={},requestToken={},acceptLanguage={},request={}", eid, sid, requestToken, acceptLanguage, JSONObject.toJSONString(request));
                ResponseBase issueDescRb = aioAiFeignClient.getIssueDescForReport(eid, sid, requestToken, acceptLanguage, request);
                if (issueDescRb != null && issueDescRb.checkIsSuccess() && issueDescRb.getData() != null) {
                    report.setIssueDescriptionSummaryResult(issueDescRb.getData().toString());
                } else {
                    log.warn("获取问题描述摘要失败，返回结果: {}", issueDescRb != null ? issueDescRb.getErrMsg() : "null");
                    report.setIssueDescriptionSummaryResult("");
                }
            } catch (Exception e) {
                log.error("获取问题描述摘要异常: {}", e.getMessage(), e);
                report.setIssueDescriptionSummaryResult("");
            } finally {
                countDownLatch.countDown();
                // 新增AI总结结论，如果有AI总结的结论 以下的三个能力不要返回
                report.setManagementApplicationAbilitySummary(null);
                report.setBehaviorApplicationSummary(null);
                report.setOrganizationalImprovementSummary(null);
            }
        });
        boolean awaitResult = countDownLatch.await(5, TimeUnit.MINUTES); //修复bug 需要判断计数是否到零结束
        if (!awaitResult) {
            // 如果等待被中断或超时，抛出异常或执行其他操作
            throw new RuntimeException("Failed to get GPT response within the specified timeout.");
        }

        report.setReportVersion(T_REPORT_VERSION);
        log.info("report-->{}", JSONUtil.toJsonStr(report));
        tProductServiceReportEsService.save(report, true);
        ProductServiceReportModel poModel = new ProductServiceReportModel();
        poModel.setReportStatus(ProductServiceReportStatus.EVALUATING);
        poModel.setId(model.getId());
        productServiceReportMapper.update(poModel);
    }

    public void save(Map<String,Object> map) {
        ProductServiceReport productServiceReport = JSONObject.parseObject(JSONObject.toJSONString(map), ProductServiceReport.class);
        tProductServiceReportEsService.bulkUpdateByScript(productServiceReport.getId(),map,TProductServiceReport.class);
    }

    public TProductServiceReport findById(String id) {
        return tProductServiceReportEsService.findById(id, TProductServiceReport.class);
    }

    private List<Map<String, Object>> getTrainCourse(ProductServiceReportGenerateReqDTO model) {
        return ProductServiceReportService.buildCourseSql(model, bigDataUtil);
    }

}
