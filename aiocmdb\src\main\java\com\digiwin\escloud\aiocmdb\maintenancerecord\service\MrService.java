package com.digiwin.escloud.aiocmdb.maintenancerecord.service;

import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.common.constant.MrConstant.MR_ROW_KEY;

@Slf4j
@Service
public class MrService implements IMrService, ParamCheckHelp {

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private BigDataUtil bigDataUtil;

    @Override
    public Object getMrDetail(String modelCode, String id, Long eid) {
        return commonUtils.getMrDetail(modelCode, id, eid);
    }

    @Override
    public BaseResponse gerMrDetailByMapList(List<Map<String, Object>> mapList) {
        //region 參數檢查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(mapList, "mapList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long headerEid = RequestUtil.getHeaderEid();

        return BaseResponse.ok(mapList.stream().filter(x -> !CollectionUtils.isEmpty(x)).map(x -> {
            String modelCode = Objects.toString(x.get("modelCode"), "");
            String id = Objects.toString(x.get("id"), "");
            Long eid = LongUtil.checkCurrentOrDefault(LongUtil.objectToLong(x.get("eid")), headerEid);
            Object obj = getMrDetail(modelCode, id, eid);
            Map<String, Object> resultMap = new HashMap<>(4);
            resultMap.put("modelCode", modelCode);
            resultMap.put("id", id);
            resultMap.put("eid", eid);
            resultMap.put("content", obj);
            return resultMap;
        }).collect(Collectors.toList()));
    }

    @Override
    public boolean saveMrDetail(String modelCode, String id, Object targetValue) {
        HashMap hashMap = commonUtils.saveMrDetail(modelCode, id, targetValue);
        return true;
    }

    @Override
    public boolean saveMrDetail(long eid, String modelCode, String id, Object targetValue) {
        HashMap hashMap = commonUtils.saveMrDetail(eid, modelCode, id, targetValue);
        return true;
    }

    @Override
    public BaseResponse getMrDetailByKeyList(String modelCode, Boolean containSplitSign, List<String> keyList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCode, "modelCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(keyList, "keyList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        containSplitSign = containSplitSign == null ? false : containSplitSign;

        //endregion
        String mrRowKey = "";
        if (containSplitSign) {
            mrRowKey = MR_ROW_KEY;
        }
        String key = String.join(mrRowKey, keyList);
        if (!CollectionUtils.isEmpty(keyList) && keyList.size() > 1) {
            String sql = "select * from DeviceAdvancedInfoCollected where deviceId='" + keyList.get(1) +
                    "' order by collectedTime desc limit 1";
            List<Map<String, Object>> dataList = bigDataUtil.query(sql);
            if (!CollectionUtils.isEmpty(dataList)) {
                Object model = dataList.get(0).getOrDefault("model", null);
                return BaseResponse.ok(model);
            } else {
                Object mrDetailByKey = commonUtils.getMrDetailByKey(modelCode, keyList.get(1));
                if (!ObjectUtils.isEmpty(mrDetailByKey)) {
                    BaseResponse.ok(mrDetailByKey);
                }
            }
        }
        return BaseResponse.ok(commonUtils.getMrDetailByKey(modelCode, key));
    }

    @Override
    public boolean removeMrDetailByIdList(String modelCode, List<String> idList) {
        return commonUtils.removeMrDetailByIdList(modelCode, idList, null);
    }

    @Override
    public boolean removeMrDetailByIdListAndEid(String modelCode, List<String> idList,Long eid) {
        return commonUtils.removeMrDetailByIdList(modelCode, idList, eid);
    }
//
//    @Override
//    public BaseResponse getMrDetailByMap(String modelCode, Map<String, Object> conditionMap) {
//        //region 参数检查
//
//        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCode, "modelCode");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        optResponse = checkParamIsEmpty(conditionMap, "conditionMap");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        //endregion
//
//        return BaseResponse.ok(commonUtils.getMrDetailByMap(modelCode, conditionMap));
//    }
//
//    @Override
//    public BaseResponse getMrDetailByGroupMap(String modelCode, Map<String, Object> groupConditionMap) {
//        //region 参数检查
//
//        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCode, "modelCode");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        optResponse = checkParamIsEmpty(groupConditionMap, "groupConditionMap");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        //endregion
//
//        return BaseResponse.ok(commonUtils.getMrDetailByGroupMap(modelCode, groupConditionMap));
//    }
//
//    @Override
//    public BaseResponse removeMrDetailByGroupMap(String modelCode, Map<String, Object> groupConditionMap) {
//        //region 参数检查
//
//        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCode, "modelCode");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        optResponse = checkParamIsEmpty(groupConditionMap, "groupConditionMap");
//        if (optResponse.isPresent()) {
//            return optResponse.get();
//        }
//
//        //endregion
//
//        return commonUtils.removeMrDetailByGroupMap(modelCode, groupConditionMap);
//    }

}
