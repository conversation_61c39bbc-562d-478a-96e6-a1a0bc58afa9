package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetExistenceStatus;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping;
import com.digiwin.escloud.aiocmdb.asset.model.enums.AssetMapField;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.asset.utils.AssetCodeRuleException;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.maintenancerecord.service.MrService;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.model.ResponseCode.INTERNAL_ERROR;
import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Slf4j
@Service
public class AssetServiceImpl implements AssetService, ParamCheckHelp {
    private static final String ASSET_STATUS = "status";
    private static final String ASSET_STATUS_INVALID = "INVALID";
    private static final String ASSET_AI_ID = AssetMapField.ASSET_AI_ID.getKey();
    protected static final String ASSET_ID = "assetId";
    protected static final String ASSET_NAME = "assetName";
    protected static final List<String> INSTANCE_STATUS_LIST = Lists.newArrayList(
            AiopsAuthStatus.AUTHED.getCode(),
            AiopsAuthStatus.UNAUTH.getCode(),
            AiopsAuthStatus.NONE.getCode());

    private static final String STATUS_ONLINE = "Online";
    private static final String SERVICECLOUD_MODEL_DB = "servicecloud";
    private static final String STATUS_OFFLINE = "Offline";
    private static final String STATUS_NOT_INSTALLED = "NotInstalled";
    private static final String USE_STATUS_IN_USE = "InUse";
    private static final String MANAGER_STATUS_MAINTENANCE_REQUIRED = "MaintenanceRequired";

    private static final Set<String> FIELDS_TO_KEEP;

    static {
        Set<String> tempSet = new HashSet<>();
        tempSet.add(ASSET_ID);
        tempSet.add("eid");
        tempSet.add("collectedTime");
        tempSet.add("flumeTimestamp");
        tempSet.add("assetName");
        tempSet.add("useStatus");
        tempSet.add("managerStatus");
        FIELDS_TO_KEEP = Collections.unmodifiableSet(tempSet);
    }


    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private EtlMapper etlMapper;

    @Autowired
    private MrService mrService;

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;


    @Autowired
    private IAssetCategoryService assetCategoryService;

    @Autowired
    private AssetAutomaticallyEstablishedService assetAutomaticallyEstablishedService;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Override
    public BaseResponse deleteAsset(String modelCode, Long id, Long eid) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(id, "id");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("modelCode", modelCode);
        map.put("sinkType", "starrocks");
        EtlEngine mainEtlEngine = etlMapper.getMainEtlEngine(map);
        String sinkPk = mainEtlEngine.getSinkPk();
        if (StringUtils.isEmpty(sinkPk) || (StringUtils.isNotEmpty(sinkPk) && sinkPk.contains(","))) {
            return BaseResponse.error(INTERNAL_ERROR, "not support pk type");
        }
        String deleteSql = "delete from " + SERVICECLOUD_MODEL_DB + "." + modelCode + " where " + sinkPk + " = " + id;
        bigDataUtil.srSave(deleteSql);
        mrService.removeMrDetailByIdListAndEid(modelCode, Stream.of(id).map(Object::toString).collect(Collectors.toList()), eid);

        // 删除AssetsRelated表中的相关数据
        String deleteAssetsRelatedSql = String.format(
                "delete from %s.AssetsRelated where primaryAssetId = %d and primarySinkName = '%s'",
                SERVICECLOUD_MODEL_DB, id, modelCode);
        bigDataUtil.srSave(deleteAssetsRelatedSql);
        // 删除被关联
        String deleteAssociatedSql = String.format(
                "delete from %s.AssetsRelated where associatedAssetId = %d and associatedSinkName = '%s'",
                SERVICECLOUD_MODEL_DB, id, modelCode);
        bigDataUtil.srSave(deleteAssociatedSql);

        return BaseResponse.ok();

    }


    @Override
    public BaseResponse<List<AssetExistenceStatus>> checkAssetsExistInAssetsRelated(List<Long> assetIdList) {
        long startTime = System.currentTimeMillis();

        // 1. 检查并准备输入ID
        if (assetIdList == null || assetIdList.isEmpty()) {
            log.debug("Input assetIdList is null or empty, returning empty result");
            return BaseResponse.ok(Collections.emptyList());
        }

        List<Long> distinctAssetIdList = assetIdList.stream().distinct().collect(Collectors.toList());
        log.info("Processing {} distinct asset IDs for existence check", distinctAssetIdList.size());
        String idListString = distinctAssetIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

        try {
            // === 步骤 2: 第一次查询，获取关联关系 ===
            String relatedQuerySql = String.format(
                    "SELECT DISTINCT primarySinkName, primaryAssetId, associatedAssetId FROM %s.AssetsRelated WHERE associatedAssetId IN (%s)",
                    SERVICECLOUD_MODEL_DB, idListString);

            List<Map<String, Object>> relatedResults = bigDataUtil.starrocksQuery(relatedQuerySql);

            if (relatedResults == null || relatedResults.isEmpty()) {
                List<AssetExistenceStatus> notFoundList = distinctAssetIdList.stream()
                        .map(id -> new AssetExistenceStatus(id, null, null))
                        .collect(Collectors.toList());
                return BaseResponse.ok(notFoundList);
            }

            // === 步骤 3: 数据处理和分组 ===
            Map<String, Set<Long>> primaryIdsByTable = new HashMap<>();

            // 建立 associatedAssetId -> List<primaryAssetId> 的映射，支持一对多关系
            Map<Long, List<Long>> associatedToPrimaryIdsMap = new HashMap<>();

            for (Map<String, Object> row : relatedResults) {
                Object sinkNameObj = row.get("primarySinkName");
                Object primaryIdObj = row.get("primaryAssetId");
                Object associatedIdObj = row.get("associatedAssetId");

                if (sinkNameObj instanceof String && primaryIdObj instanceof Number && associatedIdObj instanceof Number) {
                    String tableName = (String) sinkNameObj;
                    Long primaryId = ((Number) primaryIdObj).longValue();
                    Long associatedId = ((Number) associatedIdObj).longValue();

                    primaryIdsByTable.computeIfAbsent(tableName, k -> new HashSet<>()).add(primaryId);

                    // 如果一个 associatedId 尚未被映射，则添加映射。这实现了“取第一个”的逻辑。
                    associatedToPrimaryIdsMap.computeIfAbsent(associatedId, k -> new ArrayList<>()).add(primaryId);
                }
            }

            // === 步骤 4: 第二次批量查询，获取主资产的 assetCode 和 assetName ===
            Map<Long, Map<String, String>> primaryAssetDetailsMap = new HashMap<>();

            for (Map.Entry<String, Set<Long>> entry : primaryIdsByTable.entrySet()) {
                String tableName = entry.getKey();
                Set<Long> primaryIds = entry.getValue();

                if (primaryIds.isEmpty()) {
                    continue;
                }

                String primaryIdListString = primaryIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                String detailsQuerySql = String.format(
                        "SELECT assetId, assetCode, assetName FROM %s.%s WHERE assetId IN (%s)",
                        SERVICECLOUD_MODEL_DB, tableName, primaryIdListString);

                List<Map<String, Object>> detailsResults = bigDataUtil.starrocksQuery(detailsQuerySql);

                for (Map<String, Object> detailRow : detailsResults) {
                    Object idObj = detailRow.get(ASSET_ID);
                    Object codeObj = detailRow.get("assetCode");
                    Object nameObj = detailRow.get("assetName");

                    if (idObj instanceof Number && codeObj instanceof String && nameObj instanceof String) {
                        Long assetId = ((Number) idObj).longValue();
                        Map<String, String> details = new HashMap<>();
                        details.put("assetCode", (String) codeObj);
                        details.put("assetName", (String) nameObj);
                        primaryAssetDetailsMap.put(assetId, details);
                    }
                }
            }

            // === 步骤 5: 结果聚合，构建最终返回列表 ===
            List<AssetExistenceStatus> finalResultList = new ArrayList<>();
            for (Long associatedId : distinctAssetIdList) {
                List<Long> primaryIds = associatedToPrimaryIdsMap.get(associatedId);

                if (primaryIds != null && !primaryIds.isEmpty()) {
                    // 查找第一个有效的主资产详情（优先选择有完整信息的）
                    AssetExistenceStatus result = findBestAssetDetails(associatedId, primaryIds, primaryAssetDetailsMap);
                    finalResultList.add(result);
                } else {
                    // 在AssetsRelated中就没找到这个associatedId
                    finalResultList.add(new AssetExistenceStatus(associatedId, null, null));
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("Asset existence check completed in {}ms, processed {} assets, found {} with associations",
                    endTime - startTime, distinctAssetIdList.size(),
                    finalResultList.stream().mapToInt(status -> status.isExists() ? 1 : 0).sum());

            return BaseResponse.okT(finalResultList);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            String assetIdsForLog = assetIdList.stream().map(String::valueOf).collect(Collectors.joining(", "));
            log.error("Error checking asset association details for assetIds: [{}] after {}ms",
                    assetIdsForLog, endTime - startTime, e);
            return BaseResponse.error(INTERNAL_ERROR, "批量查询资产关联详情失败: " + e.getMessage());
        }
    }

    /**
     * 从多个主资产ID中选择最佳的资产详情
     * 优先选择有完整assetCode和assetName信息的资产
     *
     * @param associatedId           关联资产ID
     * @param primaryIds             主资产ID列表
     * @param primaryAssetDetailsMap 主资产详情映射
     * @return 最佳的资产存在状态
     */
    private AssetExistenceStatus findBestAssetDetails(Long associatedId, List<Long> primaryIds,
                                                      Map<Long, Map<String, String>> primaryAssetDetailsMap) {
        if (primaryIds == null || primaryIds.isEmpty()) {
            return new AssetExistenceStatus(associatedId, null, null);
        }

        // 优先查找有完整信息的资产（assetCode和assetName都不为空）
        StringBuilder assetCode = new StringBuilder();
        StringBuilder assetName = new StringBuilder();
        for (Long primaryId : primaryIds) {
            Map<String, String> details = primaryAssetDetailsMap.get(primaryId);
            if (details != null) {
                assetCode.append(details.get("assetCode")).append(",");
                assetName.append(details.get("assetName")).append(",");
            }
        }
        String code = assetCode.length() > 0 ? assetCode.substring(0, assetCode.length() - 1) : "";
        String name = assetName.length() > 0 ? assetName.substring(0, assetName.length() - 1) : "";
        return new AssetExistenceStatus(associatedId, code, name);
    }

    /**
     * 暂时用不到
     *
     * @param aicList
     * @return
     */
    @Override
    public BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList) {
        if (CollectionUtils.isEmpty(aicList)) {
            log.info("[invalidCmdbAsset] Input list is empty, nothing to process.");
            return BaseResponse.ok();
        }

        Map<String, List<AiopsItemContextDTO>> aiopsItemMap = aicList.stream()
                .collect(Collectors.groupingBy(AiopsItemContextDTO::getAiopsItem));

        List<AssetCategory> assetCategoryList = assetCategoryMapper.selectAssetCategorySinkNameByAiopsItemList(aiopsItemMap.keySet());

        if (CollectionUtils.isEmpty(assetCategoryList)) {
            log.warn("[invalidCmdbAsset] No matching asset categories found for the given aiopsItems: {}", aiopsItemMap.keySet());
            return BaseResponse.ok();
        }

        for (AssetCategory assetCategory : assetCategoryList) {
            try {
                String modelCode = assetCategory.getModelCode();
                List<AiopsItemContextDTO> dtoList = aiopsItemMap.get(assetCategory.getAiopsItem());

                if (CollectionUtils.isEmpty(dtoList)) {
                    continue;
                }
                List<Long> aiIdList = dtoList.stream().map(AiopsItemContextDTO::getAiId).collect(Collectors.toList());
                if (aiIdList.isEmpty()) {
                    continue;
                }

                String idsAsString = aiIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
                String selectSql = String.format("SELECT assetId, aiId FROM %s.%s WHERE %s IN (%s)",
                        SERVICECLOUD_MODEL_DB,
                        modelCode,
                        ASSET_AI_ID,
                        idsAsString);

                log.debug("[invalidCmdbAsset] Executing StarRocks query for modelCode [{}]: {}", modelCode, selectSql);
                List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(selectSql);

                if (CollectionUtils.isEmpty(maps)) {
                    log.warn("[invalidCmdbAsset] No assets found in StarRocks for modelCode [{}] with aiIds: {}", modelCode, idsAsString);
                    continue;
                }

                List<LinkedHashMap<String, Object>> finalDataList = maps.stream().map(map -> {
                    LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(map);
                    map1.put(ASSET_STATUS, ASSET_STATUS_INVALID);
                    return map1;
                }).collect(Collectors.toList());

                log.info("[invalidCmdbAsset] Updating {} assets to invalid status for modelCode [{}].", finalDataList.size(), modelCode);

            } catch (Exception e) {
                log.error("[invalidCmdbAsset] An unexpected error occurred while processing category with aiopsItem: {}", assetCategory.getAiopsItem(), e);
            }
        }

        return BaseResponse.ok();
    }


    @Override
    public BaseResponse batchSaveInstanceToStarRocksAndHBase(AssetSaveBigDataParam param) {
        try {
            // 参数校验
            if (param == null || StringUtils.isEmpty(param.getModelCode()) ||
                    CollectionUtils.isEmpty(param.getAiIdList()) || param.getUpsert() == null ||
                    LongUtil.isEmpty(param.getEid()) || param.getAiopsItem() == null) {
                return BaseResponse.error("500", "参数不能为空");
            }

            String modelCode = param.getModelCode();
            List<Long> aiIdList = param.getAiIdList();
            Boolean upsert = param.getUpsert();
            List<Long> clearAiIdList = param.getClearAiIdList();

            log.info("Start batch saving instance data, modelCode: {}, aiIdList size: {}, upsert: {}, clearAiIdList size: {}",
                    modelCode, aiIdList.size(), upsert, clearAiIdList != null ? clearAiIdList.size() : 0);

            // 处理clearAiIdList逻辑
            if (!CollectionUtils.isEmpty(clearAiIdList)) {
                BaseResponse clearResult = processClearAiIdList(clearAiIdList, modelCode, param.getEid());
                if (!clearResult.checkIsSuccess()) {
                    log.error("Failed to process clearAiIdList: {}", clearResult.getErrMsg());
                    return clearResult;
                }
            }

            ResponseBase<List<CmdbModelDataFieldRelationMapping>> mappingResponse = assetCategoryService.getCmdbModelDataFieldRelationMappingList(modelCode);
            List<CmdbModelDataFieldRelationMapping> mappingList = new ArrayList<>();

            // 优化逻辑：即使mappingResponse没有数据也继续执行，因为queryAdditionalAssetData可能有数据
            if (mappingResponse.checkIsSuccess() && mappingResponse.getData() != null && !CollectionUtils.isEmpty(mappingResponse.getData())) {
                mappingList = mappingResponse.getData();
            } else {
                log.warn("Field relation mapping configuration not found or empty, but continuing with queryAdditionalAssetData");
            }

            // 处理源数据查询（只有在mappingList不为空时才执行）
            List<Map<String, Object>> sourceDataList = new ArrayList<>();


            if (!CollectionUtils.isEmpty(mappingList)) {
                Map<String, List<CmdbModelDataFieldRelationMapping>> sourceGroupMap = mappingList.stream()
                        .collect(Collectors.groupingBy(CmdbModelDataFieldRelationMapping::getSourceModelCode));

                for (Map.Entry<String, List<CmdbModelDataFieldRelationMapping>> entry : sourceGroupMap.entrySet()) {
                    String sourceModelCode = entry.getKey();
                    List<String> sourceFieldNames = entry.getValue().stream()
                            .map(CmdbModelDataFieldRelationMapping::getSourceModelFieldName)
                            .distinct()
                            .collect(Collectors.toList());

                    sourceFieldNames.add(ASSET_AI_ID);

                    List<Map<String, Object>> sourceData = queryLatestDataFromStarRocks(
                            sourceModelCode, sourceFieldNames, aiIdList);
                    sourceDataList.addAll(sourceData);
                }
            }

            List<Map<String, Object>> additionalData = queryAdditionalAssetData(modelCode, aiIdList, param.getEid(), param.getAiopsItem()
                    , param.getAssetId());
            if (!CollectionUtils.isEmpty(additionalData)) {
                Map<String, Map<String, Object>> sourceMapIndex = sourceDataList.stream()
                        .filter(map -> map.containsKey(ASSET_AI_ID) && map.get(ASSET_AI_ID) != null)
                        .collect(Collectors.toMap(
                                map -> String.valueOf(map.get(ASSET_AI_ID)),
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));

                for (Map<String, Object> additionalMap : additionalData) {
                    Object aiIdValue = additionalMap.get(ASSET_AI_ID);

                    if (aiIdValue == null) {
                        sourceDataList.add(additionalMap);
                        continue;
                    }

                    String lookupKey = String.valueOf(aiIdValue);
                    Map<String, Object> sourceMapToUpdate = sourceMapIndex.get(lookupKey);

                    if (sourceMapToUpdate != null) {
                        sourceMapToUpdate.putAll(additionalMap);
                    } else {
                        sourceDataList.add(additionalMap);
                    }
                }
            }

            // 处理目标数据查询（只有在mappingList不为空时才执行）
            List<Map<String, Object>> targetDataList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(mappingList)) {
                Map<String, List<CmdbModelDataFieldRelationMapping>> targetGroupMap = mappingList.stream()
                        .collect(Collectors.groupingBy(CmdbModelDataFieldRelationMapping::getTargetModelCode));

                for (Map.Entry<String, List<CmdbModelDataFieldRelationMapping>> entry : targetGroupMap.entrySet()) {
                    String targetModelCode = entry.getKey();
                    List<String> targetFieldNames = entry.getValue().stream()
                            .map(CmdbModelDataFieldRelationMapping::getTargetModelFieldName)
                            .distinct()
                            .collect(Collectors.toList());

                    targetFieldNames.add(ASSET_AI_ID);
                    targetFieldNames.add(ASSET_ID);

                    List<Map<String, Object>> targetData = queryDataFromStarRocks(
                            targetModelCode, targetFieldNames, aiIdList);
                    targetDataList.addAll(targetData);
                }
            }

            List<LinkedHashMap<String, Object>> finalDataList;
            if (!upsert) {
                // upsert=false: 处理新数据
                if (CollectionUtils.isEmpty(mappingList)) {
                    // 如果没有映射关系，直接处理新数据
                    finalDataList = processNewDataOnly(sourceDataList, targetDataList, modelCode, param.getAssetId());
                } else {
                    // 如果有映射关系，先处理新数据，然后进行数据合并
                    List<LinkedHashMap<String, Object>> newDataList = processNewDataOnly(sourceDataList, targetDataList, modelCode, param.getAssetId());

                    // 将新数据转换为Map格式以便进行合并
                    List<Map<String, Object>> newDataAsMapList = newDataList.stream()
                            .map(data -> (Map<String, Object>) data)
                            .collect(Collectors.toList());

                    // 进行数据合并：将新数据与目标数据合并
                    finalDataList = processDataMerge(sourceDataList, newDataAsMapList, mappingList);

                    log.info("upsert=false with mappingList: processed {} new records, merged with target data", newDataList.size());
                }
            } else {
                // 处于更新情况 不会更新资产名称、使用状态、管理状态，在这种情况下 即使存在自动更新关系，也不进行数据更新
                for (Map<String, Object> sourceData : sourceDataList) {
                    sourceData.remove(AssetMapField.USE_STATUS.getKey());
                    sourceData.remove(AssetMapField.MANAGER_STATUS.getKey());
                    sourceData.remove(AssetMapField.ASSET_NAME.getKey());
                }
                finalDataList = processDataMerge(sourceDataList, targetDataList, mappingList);
//                // upsert=true: 当mappingList为空时，直接使用queryAdditionalAssetData的数据
//                if (CollectionUtils.isEmpty(mappingList)) {
//                    // 如果没有映射关系，直接使用queryAdditionalAssetData的数据
//                    List<Map<String, Object>> additionalData = queryAdditionalAssetData(modelCode, aiIdList, param.getEid(), param.getAiopsItem()
//                            ,param.getAssetId());
//                    finalDataList = additionalData.stream()
//                            .map(LinkedHashMap::new)
//                            .collect(Collectors.toList());
//                } else {
//
//                }
            }

            if (CollectionUtils.isEmpty(finalDataList)) {
                return BaseResponse.ok("No data to process");
            }

            BaseResponse saveResult = assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(
                    finalDataList, modelCode, "servicecloud", param.getEid());

            log.info("Batch save instance data completed, processed data count: {}", finalDataList.size());
            return saveResult;

        } catch (AssetCodeRuleException e) {
            log.error("Batch save instance data failed", e);
            return BaseResponse.error("97003", "Batch save instance data failed: " + e.getMessage());
        } catch (Exception e) {
            log.error("Batch save instance data failed", e);
            return BaseResponse.error(INTERNAL_ERROR, "Batch save instance data failed: " + e.getMessage());
        }
    }


    private List<Map<String, Object>> queryLatestDataFromStarRocks(String tableName, List<String> fieldNames, List<Long> aiIdList) {
        try {
            String fields = String.join(", ", fieldNames);

            String aiIdCondition = aiIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String sql = String.format(
                    "SELECT %s FROM (" +
                            "  SELECT %s, ROW_NUMBER() OVER (PARTITION BY aiId ORDER BY collectedTime DESC) as rn " +
                            "  FROM servicecloud.%s " +
                            "  WHERE aiId IN (%s)" +
                            ") t WHERE rn = 1",
                    fields, fields, tableName, aiIdCondition
            );

            log.debug("Query latest data SQL: {}", sql);
            return bigDataUtil.starrocksQuery(sql);

        } catch (Exception e) {
            log.error("Query StarRocks latest data failed, table: {}, fields: {}", tableName, fieldNames, e);
            return new ArrayList<>();
        }
    }

    private List<Map<String, Object>> queryDataFromStarRocks(String tableName, List<String> fieldNames, List<Long> aiIdList) {
        try {
            String fields = String.join(", ", fieldNames);

            String aiIdCondition = aiIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String sql = String.format(
                    "SELECT %s FROM servicecloud.%s WHERE aiId IN (%s)",
                    fields, tableName, aiIdCondition
            );

            log.debug("Query data SQL: {}", sql);
            return bigDataUtil.starrocksQuery(sql);

        } catch (Exception e) {
            log.error("Query StarRocks data failed, table: {}, fields: {}", tableName, fieldNames, e);
            return new ArrayList<>();
        }
    }


    private List<LinkedHashMap<String, Object>> processNewDataOnly(
            List<Map<String, Object>> sourceDataList,
            List<Map<String, Object>> targetDataList,
            String modelCode, Long assetId) {


        Set<Long> existingAiIds = targetDataList.stream()
                .map(data -> Long.valueOf(data.get(ASSET_AI_ID).toString()))
                .collect(Collectors.toSet());

        List<Map<String, Object>> newSourceData = sourceDataList.stream()
                .filter(data -> !existingAiIds.contains(Long.valueOf(data.get(ASSET_AI_ID).toString())))
                .collect(Collectors.toList());

        List<LinkedHashMap<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> sourceData : newSourceData) {
            LinkedHashMap<String, Object> newData = new LinkedHashMap<>(sourceData);
            // 如果资产id存在，则资产名称不处理
            if (LongUtil.isNotEmpty(assetId)) {
                newData.put(ASSET_ID, assetId);
            }
            result.add(newData);
        }

        return result;
    }

    private List<LinkedHashMap<String, Object>> processDataMerge(
            List<Map<String, Object>> sourceDataList,
            List<Map<String, Object>> targetDataList,
            List<CmdbModelDataFieldRelationMapping> mappingList) {

        Map<Long, Map<String, Object>> sourceDataMap = sourceDataList.stream()
                .collect(Collectors.toMap(
                        data -> Long.valueOf(data.get(ASSET_AI_ID).toString()),
                        data -> data,
                        (existing, replacement) -> existing // If duplicate, keep the first one
                ));

        Map<String, String> fieldMappingMap = mappingList.stream()
                .collect(Collectors.toMap(
                        CmdbModelDataFieldRelationMapping::getSourceModelFieldName,
                        CmdbModelDataFieldRelationMapping::getTargetModelFieldName,
                        (existing, replacement) -> existing // If duplicate, keep the first one
                ));

        List<LinkedHashMap<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> targetData : targetDataList) {
            Long aiId = Long.valueOf(targetData.get(ASSET_AI_ID).toString());
            Map<String, Object> sourceData = sourceDataMap.get(aiId);

            if (sourceData != null) {
                LinkedHashMap<String, Object> mergedData = new LinkedHashMap<>(targetData);

                for (Map.Entry<String, Object> sourceEntry : sourceData.entrySet()) {
                    String sourceFieldName = sourceEntry.getKey();
                    Object sourceValue = sourceEntry.getValue();

                    String targetFieldName = fieldMappingMap.get(sourceFieldName);
                    if (targetFieldName != null) {
                        mergedData.put(targetFieldName, sourceValue);
                    } else if (!ASSET_AI_ID.equals(sourceFieldName)) {
                        mergedData.put(sourceFieldName, sourceValue);
                    }
                }

                result.add(mergedData);
            }
        }

        return result;
    }


    public List<Map<String, Object>> queryAdditionalAssetData(String modelCode, List<Long> aiIdList, Long eid, String aiopsItem, Long assetId) {
        try {
            MergeStatisticsDetailRequest request = new MergeStatisticsDetailRequest();
            request.setAiIdList(aiIdList);
            request.setAiopsAuthStatusList(INSTANCE_STATUS_LIST);
            request.setEid(eid);
            request.setAiopsItemList(Stream.of(aiopsItem).collect(Collectors.toList()));

            BaseResponse<List<MergeStatisticsDetailInfo>> response =
                    aioItmsFeignClient.getCommonAiopsInstanceAssetListNoPage(request);

            List<MergeStatisticsDetailInfo> assetInfoList = Optional.ofNullable(response)
                    .map(BaseResponse::getData)
                    .orElse(Collections.emptyList());

            return assetInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(i -> transformAssetInfoToMap(i, eid, assetId))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to query additional asset data for modelCode: {}, aiIdList: {}",
                    modelCode, aiIdList, e);
            return new ArrayList<>();
        }
    }


    private String determineOperationStatus(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            // 如果列表为空或第一个元素为null，则无法判断，视为NOT_INSTALLED
            return STATUS_NOT_INSTALLED;
        }

        LocalDateTime lastCheckInTime = deviceList.get(0).getLastCheckInTime();
        if (lastCheckInTime == null) {
            // 如果时间戳为null，也视为NOT_INSTALLED
            return STATUS_NOT_INSTALLED;
        }

        // 计算3天前的时间点
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);

        // 如果最后检入时间在3天前的时间点之后，则为Online
        if (lastCheckInTime.isAfter(threeDaysAgo)) {
            return STATUS_ONLINE;
        } else {
            return STATUS_OFFLINE;
        }
    }

    private String getDeviceId(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            return "";
        }

        String deviceId = deviceList.get(0).getDeviceId();
        if (StringUtils.isNotEmpty(deviceId)) {
            return deviceId;
        }

        return "";
    }

    private String getDeviceKitVersion(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            return "";
        }

        String aiopsKitVersion = deviceList.get(0).getAiopskitVersion();
        if (StringUtils.isNotEmpty(aiopsKitVersion)) {
            return aiopsKitVersion;
        }

        return "";
    }

    /**
     * 处理clearAiIdList逻辑：根据clearAiIdList和modelCode查找assetId，然后清空相关字段
     */
    private BaseResponse<String> processClearAiIdList(List<Long> clearAiIdList, String modelCode, Long eid) {
        try {
            log.info("Processing clearAiIdList: {}, modelCode: {}", clearAiIdList, modelCode);

            // 1. 根据clearAiIdList和modelCode从StarRocks查找assetId
            List<Map<String, Object>> assetDataList = queryDataFromStarRocks(modelCode, Arrays.asList(ASSET_ID, ASSET_AI_ID), clearAiIdList);

            if (CollectionUtils.isEmpty(assetDataList)) {
                log.warn("No asset data found for clearAiIdList: {}, modelCode: {}", clearAiIdList, modelCode);
                return BaseResponse.ok("No asset data found to clear");
            }

            // 2. 获取transformAssetInfoToMap中ret的key对应的字段名
            Set<String> transformFields = getTransformAssetInfoToMapFields();

            // 3. 查询targetFieldNames（从映射关系中获取）
            ResponseBase<List<CmdbModelDataFieldRelationMapping>> mappingResponse = assetCategoryService.getCmdbModelDataFieldRelationMappingList(modelCode);
            Set<String> targetFieldNames = new HashSet<>();
            if (mappingResponse.checkIsSuccess() && mappingResponse.getData() != null) {
                targetFieldNames = mappingResponse.getData().stream()
                        .map(CmdbModelDataFieldRelationMapping::getTargetModelFieldName)
                        .collect(Collectors.toSet());
            }

            // 4. 构建需要清空的数据
            List<LinkedHashMap<String, Object>> clearDataList = new ArrayList<>();
            for (Map<String, Object> assetData : assetDataList) {
                LinkedHashMap<String, Object> clearData = new LinkedHashMap<>();

                // 保留必要的标识字段
                clearData.put(ASSET_ID, assetData.get(ASSET_ID));
                clearData.put("eid", eid);
                clearData.put("collectedTime", DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
                clearData.put("flumeTimestamp", System.currentTimeMillis());
                // 清空transformAssetInfoToMap中的字段（设置为null）
                // 清空targetFieldNames对应的字段（设置为null）
                cleanData(transformFields, targetFieldNames, clearData);

                clearDataList.add(clearData);
            }

            // 5. 调用batchSaveToStarRocksAndHBaseWithUpdate保存清空的数据
            BaseResponse saveResult = assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(
                    clearDataList, modelCode, "servicecloud", eid);

            if (!saveResult.checkIsSuccess()) {
                log.error("Failed to save cleared data: {}", saveResult.getErrMsg());
                return BaseResponse.error("500", "Failed to save cleared data: " + saveResult.getErrMsg());
            }

            log.info("Successfully processed clearAiIdList, cleared {} records", clearDataList.size());
            return BaseResponse.ok("Successfully cleared " + clearDataList.size() + " records");

        } catch (Exception e) {
            log.error("Error processing clearAiIdList", e);
            return BaseResponse.error("500", "Error processing clearAiIdList: " + e.getMessage());
        }
    }

    /**
     * 主处理逻辑
     */
    public void cleanData(Set<String> transformFields, Set<String> targetFieldNames, Map<String, Object> clearData) {
        // 2. 调用重构后的方法，避免代码重复
        clearFields(transformFields, clearData);
        clearFields(targetFieldNames, clearData);
    }

    /**
     * @param fieldNames 要处理的字段名集合
     * @param dataMap    要修改的数据Map
     */
    private void clearFields(Collection<String> fieldNames, Map<String, Object> dataMap) {
        if (fieldNames == null) {
            return;
        }
        for (String fieldName : fieldNames) {
            if (!FIELDS_TO_KEEP.contains(fieldName)) {
                dataMap.put(fieldName, null);
            }
        }
    }

    /**
     * 获取transformAssetInfoToMap方法中ret.put的所有字段名
     */
    private Set<String> getTransformAssetInfoToMapFields() {
        // 使用Stream从枚举中直接生成所有key的Set
        return Arrays.stream(AssetMapField.values())
                .map(AssetMapField::getKey)
                .collect(Collectors.toSet());
    }

    private Map<String, Object> transformAssetInfoToMap(MergeStatisticsDetailInfo info, Long eid, Long assetId) {
        Map<String, Object> ret = new HashMap<>();

        ret.put(AssetMapField.ASSET_AI_ID.getKey(), info.getId());
        // 如果存在资产id 不会更新资产名称
        if (LongUtil.isEmpty(assetId)) {
            ret.put(AssetMapField.ASSET_NAME.getKey(), info.getDeviceName());
        }

        ret.put(AssetMapField.EID.getKey(), eid);
        ret.put(AssetMapField.AIOPS_ITEM.getKey(), info.getAiopsItem());
        ret.put(AssetMapField.AIOPS_ITEM_ID.getKey(), info.getAiopsItemId());
        ret.put(AssetMapField.COLLECTED_TIME.getKey(), DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        ret.put(AssetMapField.FLUME_TIMESTAMP.getKey(), System.currentTimeMillis());
        ret.put(AssetMapField.DEVICE_ID.getKey(), getDeviceId(info.getAiopsKitDeviceList()));
        ret.put(AssetMapField.USE_STATUS.getKey(), USE_STATUS_IN_USE);
        ret.put(AssetMapField.MANAGER_STATUS.getKey(), MANAGER_STATUS_MAINTENANCE_REQUIRED);

        String aiopsItemType = info.getAiopsItemType();

        switch (aiopsItemType) {
            case "PRODUCT_APP":
                ret.put(AssetMapField.AGENT_VERSION.getKey(), getDeviceKitVersion(info.getAiopsKitDeviceList()));
                break;
            case "TMP_RH":
                break;
            case "DATABASE":
                ret.put(AssetMapField.AGENT_VERSION.getKey(), getDeviceKitVersion(info.getAiopsKitDeviceList()));
                ret.put(AssetMapField.OPERATION_STATUS.getKey(), info.getDbStatus());
                break;
            case "HTTP":
            case "SNMP":
                ret.put(AssetMapField.AGENT_VERSION.getKey(), getDeviceKitVersion(info.getAiopsKitDeviceList()));
                String snmpStatus = info.getSnmpStatus();
                if (snmpStatus != null) {
                    if (!"-".equals(snmpStatus) && !snmpStatus.isEmpty()) {
                        if ("true".equalsIgnoreCase(snmpStatus)) {
                            ret.put(AssetMapField.OPERATION_STATUS.getKey(), STATUS_ONLINE);
                        } else if ("false".equalsIgnoreCase(snmpStatus)) {
                            ret.put(AssetMapField.OPERATION_STATUS.getKey(), STATUS_OFFLINE);
                        }
                    }
                }
                break;
            default:
                ret.put(AssetMapField.OPERATION_STATUS.getKey(), determineOperationStatus(info.getAiopsKitDeviceList()));
                ret.put(AssetMapField.AGENT_VERSION.getKey(), getDeviceKitVersion(info.getAiopsKitDeviceList()));
                break;
        }

        return ret;
    }

}
