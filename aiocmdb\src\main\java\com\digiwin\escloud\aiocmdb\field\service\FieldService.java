package com.digiwin.escloud.aiocmdb.field.service;

import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.aiocmdb.field.dao.FieldMapper;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldType;
import com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.fieldset.dao.FieldSetMapper;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSet;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSetMapping;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
@Service
public class FieldService implements IFieldService {
    @Autowired
    private FieldMapper fieldMapper;
    @Autowired
    private FieldSetMapper fieldSetMapper;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private CommonUtils commonUtils;

    private static final int DEFAULT_PAGE_NO = 1;
    private static final int DEFAULT_PAGE_SIZE = 20;

    @Override
    public PageInfo<Field> getFieldList(String modelCode, String fieldSetCode, String search, String searchFieldCode,
                                        String fieldType, Boolean system, boolean existModel, boolean existFieldSet,
                                        int pageNum, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("search", search);
        map.put("searchFieldCode", searchFieldCode);
        map.put("fieldType", fieldType);
        map.put("modelCode", modelCode);
        map.put("fieldSetCode", fieldSetCode);
        map.put("system", system);
        map.put("existModel", existModel);
        map.put("existFieldSet", existFieldSet);
        map.put("appCode", RequestUtil.getHeaderAppCode());
        //查询
        pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
        pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
        Page page = PageHelper.startPage(pageNum, pageSize);
        fieldMapper.getFieldList(map);
        return new PageInfo<>(page);
    }

    @Override
    public List<Field> getSystemFieldList(String modelCode, Boolean system, Boolean hide) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("modelCode", modelCode);
        map.put("system", system);
        map.put("hide", hide);
        return fieldMapper.getSystemFieldList(map);
    }

    @Override
    public Field getFieldDetail(String fieldCode) {
        return fieldMapper.findFieldCode(fieldCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse addField(Field field) {
        BaseResponse res = new BaseResponse();
        try {
            //字段编码不能为空
            if (StringUtils.isEmpty(field.getFieldCode())) {
                res.setCode(ResponseCode.FIELDCODE_IS_NULL.toString());
                res.setErrMsg(ResponseCode.FIELDCODE_IS_NULL.getMsg());
                return res;
            }
            //字段名称不能为空
            if (StringUtils.isEmpty(field.getFieldName())) {
                res.setCode(ResponseCode.FIELDNAME_IS_NULL.toString());
                res.setErrMsg(ResponseCode.FIELDNAME_IS_NULL.getMsg());
                return res;
            }
            //字段类型不能为空
            if (StringUtils.isEmpty(field.getFieldType())) {
                res.setCode(ResponseCode.FIELDTYPE_IS_NULL.toString());
                res.setErrMsg(ResponseCode.FIELDTYPE_IS_NULL.getMsg());
                return res;
            }

            //字段编汇总方式不能为空
            if (StringUtils.isEmpty(field.getSummaryMethod())) {
                res.setCode(ResponseCode.EDIT_REQUIRE_AUTOCOLLECTION_ALL_NULL.toString());
                res.setErrMsg(ResponseCode.EDIT_REQUIRE_AUTOCOLLECTION_ALL_NULL.getMsg());
                return res;
            }
            //1 校验，字段编码唯一性
            if (fieldMapper.findFieldCode(field.getFieldCode()) != null) {
                res.setCode(ResponseCode.FIELDCODE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.FIELDCODE_IS_EXIST.getMsg());
                return res;
            }
            //2 新增
            long sid = RequestUtil.getHeaderSid();
            field.setSid(sid);
            field.setEid(RequestUtil.getHeaderEid());
            field.setId(SnowFlake.getInstance().newId());
            field.setAppCode(RequestUtil.getHeaderAppCode());
            if (fieldMapper.addField(field)) {
                //当字段是枚举类型时，需要存枚举的下拉值
                if (field.getFieldType().equals(FieldType.ENUM.toString())) {
                    if (!CollectionUtils.isEmpty(field.getFieldTypeEnumList())) {
                        int index = 1;
                        for (int k = 0; k < field.getFieldTypeEnumList().size(); k++) {
                            field.getFieldTypeEnumList().get(k).setSid(sid);
                            field.getFieldTypeEnumList().get(k).setSort(index++);
                        }
                        //先删后增
                        fieldMapper.deleteFieldTypeEnum(field.getFieldCode());
                        Map<String, Object> map = new HashMap<>();
                        map.put("fieldCode", field.getFieldCode());
                        map.put("list", field.getFieldTypeEnumList());
                        fieldMapper.addFieldTypeEnumList(map);
                    }

                }
                res.setData(field.getFieldCode());
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.INSERT_FAILD.toString());
                res.setErrMsg(ResponseCode.INSERT_FAILD.getMsg());
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse modifyField(Field field) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验，字段编码唯一性
            Field existField = fieldMapper.findFieldCode(field.getFieldCode());
            if (existField != null && existField.getId() != field.getId()) {
                res.setCode(ResponseCode.FIELDCODE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.FIELDCODE_IS_EXIST.getMsg());
                return res;
            }
            //2 修改
            field.setSid(RequestUtil.getHeaderSid());
            if (fieldMapper.modifyField(field)) {
                //当字段是枚举类型时，需要存枚举的下拉值
                if (field.getFieldType().equals(FieldType.ENUM.toString())) {
                    if (!CollectionUtils.isEmpty(field.getFieldTypeEnumList())) {
                        for (int k = 0; k < field.getFieldTypeEnumList().size(); k++) {
                            field.getFieldTypeEnumList().get(k).setSid(RequestUtil.getHeaderSid());
                        }

                        //先删后增
                        fieldMapper.deleteFieldTypeEnum(field.getFieldCode());
                        Map<String, Object> map = new HashMap<>();
                        map.put("fieldCode", field.getFieldCode());
                        map.put("list", field.getFieldTypeEnumList());
                        fieldMapper.addFieldTypeEnumList(map);
                    } else {
                        fieldMapper.deleteFieldTypeEnum(field.getFieldCode());
                    }

                }
                List<String> modelCodes = fieldMapper.getModelCodesByField(field.getFieldCode());
                if (!CollectionUtils.isEmpty(modelCodes)) {
                    commonUtils.asyncBatchClearModelCache(modelCodes);
                }
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.UPDATE_FAILD.toString());
                res.setErrMsg(ResponseCode.UPDATE_FAILD.getMsg());
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse checkField(String fieldCode) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验，字段是否存在字段集引用
            List<FieldSet> existFieldSet = fieldSetMapper.checkFieldInFieldSet(fieldCode);
            List<Model> existModel = modelMapper.checkFieldInModel(fieldCode);
            if (!CollectionUtils.isEmpty(existFieldSet) || !CollectionUtils.isEmpty(existModel)) {
                String message = "";
                if (!CollectionUtils.isEmpty(existFieldSet)) {
                    StringBuffer stringBuffer1 = new StringBuffer();
                    stringBuffer1.append(" FieldSet: ");
                    for (FieldSet fieldSet : existFieldSet) {
                        stringBuffer1.append(fieldSet.getFieldSetName()).append(",");
                    }
                    message = stringBuffer1.substring(0, stringBuffer1.length() - 1);

                }
                if (!CollectionUtils.isEmpty(existModel)) {
                    StringBuffer stringBuffer2 = new StringBuffer();
                    stringBuffer2.append(" Model: ");
                    for (Model model : existModel) {
                        stringBuffer2.append(model.getModelName()).append(",");
                    }
                    message += stringBuffer2.substring(0, stringBuffer2.length() - 1);

                }
                res.setCode(ResponseCode.FIELD_IS_EXIST_IN_FIELDSET_OR_MODEL.toString());
                res.setErrMsg(ResponseCode.FIELD_IS_EXIST_IN_FIELDSET_OR_MODEL.getMsg());
                res.setData(message);
                return res;
            }


        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        res.setCode(ResponseCode.SUCCESS.toString());

        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deleteField(long id) {
        BaseResponse res = new BaseResponse();
        try {
            //0 先查找使用到该字段的模型(后面清理缓存用)
            List<String> modelCodeList = modelMapper.selectFieldRelateModelCode(id);
            //1 删除字段集及模型中的字段
            fieldSetMapper.deleteFieldSetMapping(id);
            modelMapper.deleteModelFieldMapping(id);
            //2 删除字段
            if (fieldMapper.deleteField(id)) {
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.DELETE_FAILD.toString());
                res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
            //清缓存
            commonUtils.asyncBatchClearModelCache(modelCodeList);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return res;
    }

    @Override
    public boolean sortField(List<ModelFieldMapping> modelFieldMappings) {
        return fieldMapper.sortField(modelFieldMappings) > 0;
    }

    @Override
    public boolean sortFieldSetField(List<FieldSetMapping> fieldSetMappings) {
        return fieldMapper.sortFieldSetField(fieldSetMappings) > 0;
    }

    @Override
    public boolean saveFieldFormula(List<ModelFieldFomula> modelFieldFomulas) {
        modelFieldFomulas.stream().forEach(o -> {
            if (o.getId() <= 0) {
                o.setId(SnowFlake.getInstance().newId());
            }
            fieldMapper.saveFieldFormula(o);
        });
        ModelFieldMapping modelFieldMappingById = modelMapper.getModelFieldMappingById(modelFieldFomulas.get(0).getModelFieldMappingId());
        commonUtils.asyncClearModelDetailCache(modelFieldMappingById.getModelCode());
        return true;
    }

    @Override
    public BaseResponse fieldTypeEnumSort(List<FieldTypeEnum> fieldTypeEnumList) {
        if(CollectionUtils.isEmpty(fieldTypeEnumList)){
            return BaseResponse.error(ResponseCode.FIELD_IS_NULL);
        }

        Integer isSuccess = fieldMapper.fieldTypeEnumSort(fieldTypeEnumList);
        return BaseResponse.ok(isSuccess > 0);
    }

    @Override
    public BaseResponse formatField(List<ModelFieldMapping> modelFieldMappingList) {
        if(CollectionUtils.isEmpty(modelFieldMappingList)){
            return BaseResponse.error(ResponseCode.FIELD_IS_NULL);
        }

        Integer isSuccess = fieldMapper.formatField(modelFieldMappingList);
        return BaseResponse.ok(isSuccess > 0);
    }

    @Override
    public List<Field>  findFieldCodeList(List<String> fieldCodeList) {
        return fieldMapper.findFieldCodeList(fieldCodeList);
    }

    @Override
    public List<FieldTypeEnum> getFieldTypeEnumByFieldCodes(List<String> fieldCodes) {
        return fieldMapper.getFieldTypeEnumByFieldCodes(fieldCodes);
    }
}
