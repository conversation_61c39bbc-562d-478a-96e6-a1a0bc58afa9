package com.digiwin.escloud.aioitms.bigdata.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.model.QueryWrapper;
import com.digiwin.escloud.aioitms.bigdata.model.QueryWrapperHelper;
import com.digiwin.escloud.aioitms.bigdata.service.IDataV3Service;
import com.digiwin.escloud.aioitms.bigdata.thread.NamedThreadFactory;
import com.digiwin.escloud.common.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date: 2022-10-13 15:18
 * @Description
 */
@Slf4j
@Service
public class DataV3Service extends DataV3Basic implements IDataV3Service {

    public final long dayOfMillis1 = 86400000;
    public static final long dayOfMillis7 = 604800000;

    public final String TABLE_DETAIL_CPU = "CpuCollected_sr_duplicate";
    public final String TABLE_DAY_CPU = "CpuCollected_day";
    public final String TABLE_DETAIL_MEMORY = "MemoryCollected_sr_duplicate";
    public final String TABLE_DAY_MEMORY = "MemoryCollected_day";
    public final String TABLE_DETAIL_NETWORK = "NetworkCollected_sr_duplicate";
    public final String TABLE_DAY_NETWORK = "NetworkCollected_day";
    public final String TABLE_DETAIL_DISKIO = "DiskioCollected_sr_duplicate";
    public final String TABLE_DAY_DISKIO = "DiskioCollected_day";
    public final String TABLE_DETAIL_DISK = "DiskCollected_sr_duplicate";
    public final String TABLE_DAY_DISK = "DiskCollected_day";
    public final String TABLE_DETAIL_DeviceAdvancedInfo = "DeviceAdvancedInfoCollected_sr_primary";
    public final String TABLE_DETAIL_WINDOWS_LOGIN_ERROR_COLLECTED = "WindowsLoginErrorCollected";


    @Autowired
    private BigDataUtil bigDataUtil;

    private final ExecutorService dataV3ExecutorService = new ThreadPoolExecutor(4, 6, 660, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(10), new NamedThreadFactory("DataV3Service"),
            new ThreadPoolExecutor.DiscardOldestPolicy());

    @Override
    public List<Map<String, Object>> cpuV3Cart(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "CpuCollected_sr_duplicate");
        qw.select("collectedTime", "ROUND(cpu__usage_system,2) as cpu__usage_system", "ROUND(cpu__usage_idle,2) as cpu__usage_idle");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> memoryV3Cart(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "MemoryCollected_sr_duplicate");
        qw.select("collectedTime", "ROUND(mem__available_percent,2) as mem__available_percent");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> networkV3Cart(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "NetworkCollected_sr_duplicate");
        qw.select("collectedTime", "ROUND(net__bytes_sent_per_sec,2) as net__bytes_sent_per_sec", "ROUND(net__bytes_recv_per_sec,2) as net__bytes_recv_per_sec");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> diskIoCartV3(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "DiskioCollected_sr_duplicate");
        qw.select("collectedTime", "diskio__read_bytes_per_sec", "diskio__write_bytes_per_sec","diskio__io_util");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    @Override
    public List<Map<String, Object>> swapCartV3(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), "SwapSpaceUsageCollected");
        qw.select("collectedTime", "ROUND( ( swapUsed / swapTotal ) * 100, 2 ) AS swap__used_percent");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    public List<Map<String, Object>> diskCartV3(String startTime, String endTime, String deviceId) {
        String maxCollectTime = bigDataUtil.getMaxTime("DiskCollected_sr_duplicate", "collectedTime", "deviceId",
                deviceId, "", endTime);
        if (StringUtils.isEmpty(maxCollectTime)) {
            log.warn("diskCart deviceId:{} max collectedTime is null", deviceId);
            return null;
        }

        LocalDateTime maxDateTime = DateUtil.tryParseLocalDateTime(maxCollectTime).get();
        String beforeDate = DateUtil.getSomeDateFormatString(maxDateTime.minusDays(1L), DateUtil.DATE_FORMATTER);
        String maxBeforeCollectTime = bigDataUtil.getMaxTime("DiskCollected_sr_duplicate", "collectedTime", "deviceId",
                deviceId, beforeDate + " 00:00:00", beforeDate + " 23:59:59");

        String selectSql = " select dsd.deviceId,dsd.disk__path, dsd.disk__total, dsd.disk__used, dsd.disk__free, dsd.disk__used_percent, dsd.collectedTime collectedtime, dsd.disk__device";
        String fromSql = " from " + bigDataUtil.getSrDbName() + ".DiskCollected_sr_duplicate dsd";
        String whereSql = " where dsd.deviceId='" + deviceId + "' and dsd.collectedTime='" + maxCollectTime + "' and disk__device  not like '%tmpfs%' and disk__device  not like '%loop%'  and disk__device  not like '//%' ";
        String orderSql = " order by dsd.disk__path";
        String sql;
        if (!StringUtils.isEmpty(maxBeforeCollectTime)) {
            selectSql += ",dsd2.disk__used before_disk__used,(dsd.disk__used-dsd2.disk__used) as addData";
            String joinSql = " left join (select deviceId,disk__path, disk__total, disk__used, disk__free, disk__used_percent, collectedTime" +
                    " from " + bigDataUtil.getSrDbName() + ".DiskCollected_sr_duplicate" +
                    " where deviceId='" + deviceId + "' and disk__device  not like '%tmpfs%' and disk__device  not like '%loop%'  and disk__device  not like '//%'  and collectedTime='" + maxBeforeCollectTime + "') dsd2" +
                    " on dsd.deviceId=dsd2.deviceId and dsd.disk__path=dsd2.disk__path";
            sql = selectSql + fromSql + joinSql + whereSql;
        } else {
            selectSql += ",0 before_disk__used,0 addData";
            sql = selectSql + fromSql + whereSql;
        }
        sql += orderSql;
        List<Map<String, Object>> data = bigDataUtil.srQuery(sql);
        return data;
    }

    /**
     * 超过7天为1天粒度,7天以下实时数据
     *
     * @param startTime
     * @param endTime
     * @param deviceId
     * @return
     */
    @Override
    public List<Map<String, Object>> commonCartV3(String startTime, String endTime, String deviceId, String modelCode) {
        String sql = "";
        LocalDateTime startDateTime = bigDataUtil.str2LocalDateTimedf2(startTime);
        LocalDateTime endTimeDateTime = bigDataUtil.str2LocalDateTimedf2(endTime);
        Duration between = Duration.between(startDateTime, endTimeDateTime);
        long millis = between.toMillis();
        if (millis < dayOfMillis7) {
            switch (modelCode) {
                case "CpuCollected":
                    sql = commonCartSql4Cpu(startTime, endTime, deviceId);
                    break;
                case "MemoryCollected":
                    sql = commonCartSql4Memory(startTime, endTime, deviceId);
                    break;
                case "DiskioCollected":
                    sql = commonCartSql4Diskio(startTime, endTime, deviceId);
                    break;
                case "NetworkCollected":
                    sql = commonCartSql4Network(startTime, endTime, deviceId);
                    break;
                case "DiskCollected":
                    return diskCartV3(startTime, endTime, deviceId);
                case "WindowsLoginErrorCollected":
                    sql = commonCartSqlLoginError(startTime, endTime, deviceId);
                    break;
                case "DiskCollected_Free":
                    sql = commonCartSql4DiskFree(startTime, endTime, deviceId);
                    break;
            }
        } else {
            switch (modelCode) {
                case "CpuCollected":
                    sql = commonCartSql4CpuDay(startTime, endTime, deviceId);
                    break;
                case "MemoryCollected":
                    sql = commonCartSql4MemoryDay(startTime, endTime, deviceId);
                    break;
                case "DiskioCollected":
                    sql = commonCartSql4DiskioDay(startTime, endTime, deviceId);
                    break;
                case "NetworkCollected":
                    sql = commonCartSql4NetworkDay(startTime, endTime, deviceId);
                    break;
                case "DiskCollected":
                    return diskCartV3(startTime, endTime, deviceId);
                case "WindowsLoginErrorCollected":
                    sql = commonCartSqlLoginErrorDay(startTime, endTime, deviceId);
                    break;
                case "DiskCollected_Free":
                    sql = commonCartSql4DiskFreeDay(DateUtil.getSomeDateFormatString(startDateTime.toLocalDate(), DateUtil.DATE_TIME_FORMATTER),
                            DateUtil.getSomeDateFormatString(endTimeDateTime.toLocalDate(), DateUtil.DATE_TIME_FORMATTER), deviceId);
                    break;
            }
        }
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);
        return dataList;
    }

    private String commonCartSql4DiskFreeDay(String startTime, String endTime, String deviceId) {
//        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DAY_DISK);
//        qw.select("collectedDate as collectedTime", "(disk__total-disk__used) as disk__free", "disk__path");
//        qw.select("collectedDate as collectedTime", "disk__total", "disk__used", "disk__free", "disk__path");
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_DISK);
        qw.select("DATE(collectedTime) as collectedTime", " ROUND(AVG(disk__total), 2) AS disk__total",
                " ROUND(AVG(disk__used), 2) AS disk__used", " ROUND(AVG(disk__free), 2) AS disk__free", "disk__path",
                " ROUND(AVG(disk__used_percent), 2) AS disk__used_percent", "ROUND(100 - AVG(disk__used_percent), 2) AS disk__free_percent");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime)
                .notlike("disk__path","%tmpfs%")
                .notlike("disk__path","%loop%")
                .notlike("disk__path","//%")
                .groupBy("DATE(collectedTime)", "disk__path")
                .orderByAsc("DATE(collectedTime)");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    @Override
    public List<Map<String, Object>> windowsLoginError(String startTime, String endTime, String deviceId, Integer topN) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_WINDOWS_LOGIN_ERROR_COLLECTED);
        qw.select("count(get_json_string(wel_event_datas,'$.[19].wel_value')) as loginErrorNum", "get_json_string(wel_event_datas,'$.[19].wel_value') as ip");
        qw.ne("get_json_string(wel_event_datas,'$.[19].wel_value')","-");
        qw.eq("deviceId", deviceId).between("wel_system_time", startTime, endTime);
        qw.orderByAsc("loginErrorNum");
        qw.groupBy("get_json_string(wel_event_datas,'$.[19].wel_value')");
        if (topN != -1) {
            qw.page(1, topN);
        }
        String sql = QueryWrapperHelper.getSql4Phoenix(qw);
        return bigDataUtil.srQuery(sql);
    }

    private String commonCartSqlLoginError(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_WINDOWS_LOGIN_ERROR_COLLECTED);
        qw.select("wel_system_time as t", "count(get_json_string(wel_event_datas,'$.[19].wel_value')) as loginErrorNum");
        qw.ne("get_json_string(wel_event_datas,'$.[19].wel_value')","-");
        qw.eq("deviceId", deviceId).between("wel_system_time", startTime, endTime).orderByAsc("wel_system_time")
                .groupBy("wel_system_time");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSqlLoginErrorDay(String startTime, String endTime, String deviceId) {
        startTime = startTime.substring(0, 10);
        endTime = endTime.substring(0, 10);
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_WINDOWS_LOGIN_ERROR_COLLECTED);
        qw.select("date(wel_system_time) as t", "count(get_json_string(wel_event_datas,'$.[19].wel_value')) as loginErrorNum");
        qw.ne("get_json_string(wel_event_datas,'$.[19].wel_value')","-");
        qw.eq("deviceId", deviceId).between("date(wel_system_time)", startTime, endTime).orderByAsc("t")
                .groupBy(" date(wel_system_time) ");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }


    private String commonCartSql4Cpu(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_CPU);
        qw.select("collectedTime as t", "ROUND(cpu__usage_idle,2) as cpu__usage_idle");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4Memory(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_MEMORY);
        qw.select("collectedTime as t", "ROUND(mem__available_percent,2) as mem__available_percent");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4Network(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_NETWORK);
        qw.select("collectedTime as t", "ROUND(net__bytes_sent_per_sec,2) as net__bytes_sent_per_sec", "ROUND(net__bytes_recv_per_sec,2) as net__bytes_recv_per_sec");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4Diskio(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_DISKIO);
        qw.select("collectedTime", "diskio__read_bytes_per_sec", "diskio__write_bytes_per_sec");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime).orderByAsc("collectedTime");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4DiskFree(String startTime, String endTime, String deviceId) {
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DETAIL_DISK);
        qw.select("collectedTime", "disk__total", "disk__used", "disk__free", "disk__path", "disk__used_percent", "100-disk__used_percent as disk__free_percent");
        qw.eq("deviceId", deviceId).between("collectedTime", startTime, endTime)
                .notlike("disk__path","%tmpfs%")
                .notlike("disk__path","%loop%")
                .notlike("disk__path","//%")
                .orderByAsc("collectedTime");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4CpuDay(String startTime, String endTime, String deviceId) {
        startTime = startTime.substring(0, 10);
        endTime = endTime.substring(0, 10);
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DAY_CPU);
        qw.select("collectedDate as t", "ROUND(cpu__usage_idle_sum/esCnt ,2) as cpu__usage_idle");
        qw.eq("deviceId", deviceId).between("collectedDate", startTime, endTime).orderByAsc("collectedDate");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4NetworkDay(String startTime, String endTime, String deviceId) {
        startTime = startTime.substring(0, 10);
        endTime = endTime.substring(0, 10);
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DAY_NETWORK);
        qw.select("collectedDate as t", "ROUND(net__bytes_sent_per_sec_sum/esCnt ,2) as net__bytes_sent_per_sec", "ROUND(net__bytes_recv_per_sec_sum/esCnt,2) as net__bytes_recv_per_sec");
        qw.eq("deviceId", deviceId).between("collectedDate", startTime, endTime).orderByAsc("collectedDate");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }

    private String commonCartSql4MemoryDay(String startTime, String endTime, String deviceId) {
        startTime = startTime.substring(0, 10);
        endTime = endTime.substring(0, 10);
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DAY_MEMORY);
        qw.select("collectedDate as t", "ROUND(mem__available_percent_sum/esCnt ,2) as mem__available_percent");
        qw.eq("deviceId", deviceId).between("collectedDate", startTime, endTime).orderByAsc("collectedDate");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }


    private String commonCartSql4DiskioDay(String startTime, String endTime, String deviceId) {
        startTime = startTime.substring(0, 10);
        endTime = endTime.substring(0, 10);
        QueryWrapper qw = new QueryWrapper(bigDataUtil.getSrDbName(), TABLE_DAY_DISKIO);
        qw.select("collectedDate as t", "ROUND(diskio__read_bytes_per_sec_sum/esCnt ,2) as diskio__read_bytes_per_sec", "ROUND(diskio__write_bytes_per_sec_sum/esCnt ,2) as diskio__write_bytes_per_sec");
        qw.eq("deviceId", deviceId).between("collectedDate", startTime, endTime).orderByAsc("collectedDate");
        return QueryWrapperHelper.getSql4Phoenix(qw);
    }


    @Override
    public Map<String, Object> deviceCurrentInfo(String deviceId) {
        Map<String, Object> result = new HashMap<>();
        List<Callable<List<Map<String, Object>>>> taskList = Lists.newArrayList();
        taskList.add(() -> {
            List<Map<String, Object>> osInfo = deviceInfo(deviceId);
            if (CollectionUtils.isEmpty(osInfo)) {
                result.put("osInfo", new HashMap<>());
            } else {
                Map<String, Object> map = osInfo.get(0);
                Object osInfoObj = map.get("os_info");
                if (osInfoObj != null) {
                    JSONObject jsonObject;
                    try {
                        jsonObject = JSONObject.parseObject(osInfoObj.toString());
                    } catch (Exception ex) {
                        String osInfoStr = osInfoObj.toString().replace("\\\"", "\\\"\"");
                        jsonObject = JSONObject.parseObject(osInfoStr);
                    }
                    map.put("os_product_name", jsonObject.getString("os_product_name"));
                    map.put("os_version", jsonObject.getString("os_version"));
                }
                result.put("osInfo", map);
            }
            return osInfo;
        });
        taskList.add(() -> {
            List<Map<String, Object>> cpuCurrent = cpuCurrent(deviceId);
            if (CollectionUtils.isEmpty(cpuCurrent)) {
                result.put("cpu", new HashMap<>());
            } else {
                Map<String, Object> map = cpuCurrent.get(0);
                result.put("cpu", map);
            }
            return cpuCurrent;
        });
        taskList.add(() -> {
            List<Map<String, Object>> diskCurrent = diskCurrent(deviceId);
            if (CollectionUtils.isEmpty(diskCurrent)) {
                result.put("disk", new ArrayList<>());
            } else {
                result.put("disk", diskCurrent);
            }
            return diskCurrent;
        });
        taskList.add(() -> {
            List<Map<String, Object>> memoryCurrent = memoryCurrent(deviceId);
            if (CollectionUtils.isEmpty(memoryCurrent)) {
                result.put("memory", new HashMap<>());
            } else {
                Map<String, Object> map = memoryCurrent.get(0);
                result.put("memory", map);
            }
            return memoryCurrent;
        });
        try {
            List<Future<List<Map<String, Object>>>> futures = dataV3ExecutorService.invokeAll(taskList);
            for (Future<List<Map<String, Object>>> resultFuture : futures) {
                resultFuture.get(5,TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private List<Map<String, Object>> memoryCurrent(String deviceId) {
        String sql = getSqlCurrent(bigDataUtil.getSrDbName()+"."+TABLE_DETAIL_MEMORY, deviceId, true, "mem__total", "mem__used_percent");
        return bigDataUtil.srQuery(sql);
    }

    private List<Map<String, Object>> diskCurrent(String deviceId) {
        return diskCartV3(null, null, deviceId);
    }


    private List<Map<String, Object>> cpuCurrent(String deviceId) {
        String sql = getSqlCurrent(bigDataUtil.getSrDbName()+"."+TABLE_DETAIL_CPU, deviceId, true, "cpu__usage_system", "cpu__usage_idle");
        return bigDataUtil.srQuery(sql);
    }


    private List<Map<String, Object>> deviceInfobak(String deviceId) {
        String sql = getSqlCurrent("DeviceAdvancedInfoCollected", deviceId, true, "os_info as \"os_info\"");
        List<Map<String, Object>> dataList = bigDataUtil.phoenixQuery(sql);
        return dataList;
    }

    private List<Map<String, Object>> deviceInfo(String deviceId) {
        String sql = getSqlCurrent(bigDataUtil.getSrDbName()+"."+TABLE_DETAIL_DeviceAdvancedInfo, deviceId, true, "os_info");
        return bigDataUtil.srQuery(sql);
    }
}
