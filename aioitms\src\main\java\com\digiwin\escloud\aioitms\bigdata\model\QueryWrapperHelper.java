package com.digiwin.escloud.aioitms.bigdata.model;

import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.common.util.DateUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Date 2022/3/25 15:51
 * @Created yanggld
 * @Description
 */
public class QueryWrapperHelper {

    private static final String MUTIL_VALUE_JOIN_SIGN = "-";

    /**
     * 内部类，用于存储JOIN配置和其索引
     */
    private static class JoinConfigWithIndex {
        final int index;
        final Query.JoinConfig joinConfig;

        JoinConfigWithIndex(int index, Query.JoinConfig joinConfig) {
            this.index = index;
            this.joinConfig = joinConfig;
        }
    }


    public static String doubleQuotation(String key) {
        return "\"" + key + "\"";
    }

    public static String quotation(String key) {
        return "'" + key + "'";
    }

    public static String getSql4Phoenix(QueryWrapper qw) {
        Query query = qw.getQuery();
        String[] columns = query.getColumns();
        String tableName = query.getTableName();
        List<Query.QueryCondition> queryConditions = query.getQueryCondition();
        List<Query.BusinessCondition> businessConditions = query.getBusinessCondition();
        String[] groupColumns = query.getGroupColumns();
        Query.TimeGroup timeGroup = query.getTimeGroup();
        List<Query.QueryOrder> orderFields = query.getOrderFields();
        Integer pageIndex = query.getPageIndex();
        Integer pageSize = query.getPageSize();
        StringBuilder sb = new StringBuilder();
        sb.append("select ").append(getColumns4Phoenix(columns)).append(" from ");
        String dbName = query.getDbName();
        if (!StringUtils.isEmpty(dbName)) {
            sb.append(dbName + ".");
        }
        sb.append(tableName);
        if (!CollectionUtils.isEmpty(queryConditions)) {
            sb.append(" where ").append(getCondition4Phoenix(queryConditions));
        }
        if (!CollectionUtils.isEmpty(businessConditions)) {
            if (sb.indexOf(" where ") > 0) {
                sb.append(getBusinessCondition4Sql(businessConditions));
            } else {
                sb.append(" where ").append(getBusinessCondition4Sql(businessConditions));
            }
        }
        if (!ArrayUtils.isEmpty(groupColumns)) {
            sb.append(" group by ");
            for (int i = 0; i < groupColumns.length; i++) {
                String groupColumn = groupColumns[i];
                if (i != 0) {
                    sb.append(",");
                }
                sb.append(groupColumn);
            }
        }
        if (!CollectionUtils.isEmpty(orderFields)) {
            sb.append(" order by ").append(getOrder4Phoenix(orderFields));
        }
        if (pageIndex == null && pageSize != null) {
            sb.append(" limit ").append(pageSize);
        } else if (pageIndex != null && pageSize != null) {
            sb.append(" limit ").append((pageIndex - 1) * pageSize).append(",").append(pageSize);
        }
        return sb.toString();
    }

    public static String getSql4Jdbc(QueryWrapper qw) {
        Query query = qw.getQuery();
        String[] columns = query.getColumns();
        String tableName = query.getTableName();
        List<Query.QueryCondition> queryConditions = query.getQueryCondition();
        List<Query.BusinessCondition> businessConditions = query.getBusinessCondition();
        //支持逻辑操作符 和 多组查询
        List<QueryV2.Condition> businessConditionV2 = query.getBusinessConditionV2();
        String[] groupColumns = query.getGroupColumns();
        List<Query.QueryOrder> orderFields = query.getOrderFields();
        Integer pageIndex = query.getPageIndex();
        Integer pageEndIndex = query.getPageEndIndex();
        Integer pageSize = query.getPageSize();
        List<Query.JoinConfig> joinConfigs = query.getJoin();
        StringBuilder sb = new StringBuilder();

        // 检查是否有JOIN配置且columns不是通配符
        boolean hasJoin = !CollectionUtils.isEmpty(joinConfigs) && !isWildcardColumns(columns);

        // 为JOIN表生成唯一别名
        Map<Integer, String> joinTableAliases = hasJoin ? generateUniqueTableAliases(joinConfigs) : new HashMap<>();

        // 构建SELECT子句
        String columnsStr;
        if (hasJoin) {
            columnsStr = getColumnsForJoinQuery(query, columns, tableName, joinConfigs, joinTableAliases);
        } else {
            columnsStr = getColumnsForQuery(query, columns);
        }
        sb.append("select ").append(columnsStr).append(" from ");

        // 构建FROM子句
        String fullTableName = getFullTableName(query.getDbName(), tableName);
        if (hasJoin) {
            // 如果有JOIN，主表需要别名
            sb.append(fullTableName).append(" ").append(tableName);
        } else {
            sb.append(fullTableName);
        }

        // 构建JOIN子句
        if (hasJoin) {
            for (int i = 0; i < joinConfigs.size(); i++) {
                Query.JoinConfig joinConfig = joinConfigs.get(i);
                String joinTableAlias = joinTableAliases.get(i);
                sb.append(" ").append(joinConfig.getJoinType().toUpperCase()).append(" JOIN ");
                String joinTableName = getFullTableName(query.getDbName(), joinConfig.getSinkName());
                sb.append(joinTableName).append(" ").append(joinTableAlias);
                // 构建JOIN ON条件，处理函数包装的字段
                String mainFieldName = joinConfig.getJoinOn().getMainFieldName();
                String joinOnCondition = buildJoinOnCondition(tableName, mainFieldName, joinTableAlias, joinConfig.getJoinOn().getFieldName());
                sb.append(" ON ").append(joinOnCondition);
            }
        }

        appendExtremeTimeColCondition(query.getExtremeValueCondition(), fullTableName,
                queryConditions, businessConditions,businessConditionV2);

        // 构建WHERE子句，如果有JOIN需要给条件字段加上表别名
        if (hasJoin) {
            appendWhereWithTableAlias(sb, queryConditions, businessConditions, businessConditionV2, tableName, joinConfigs, joinTableAliases);
        } else {
            appendWhere(sb, queryConditions, businessConditions, businessConditionV2);
        }

        // 构建GROUP BY子句
        if (!ArrayUtils.isEmpty(groupColumns)) {
            sb.append(" group by ");
            for (int i = 0; i < groupColumns.length; i++) {
                String groupColumn = groupColumns[i];
                if (i != 0) {
                    sb.append(",");
                }
                if (hasJoin) {
                    sb.append(tableName).append(".").append(groupColumn);
                } else {
                    sb.append(groupColumn);
                }
            }
        }

        // 构建ORDER BY子句
        if (!CollectionUtils.isEmpty(orderFields)) {
            if (hasJoin) {
                sb.append(" order by ").append(getOrderWithTableAlias(orderFields, tableName));
            } else {
                sb.append(" order by ").append(getOrder4Phoenix(orderFields));
            }
        }

        // 构建LIMIT子句
        if (pageIndex == null && pageSize != null) {
            sb.append(" limit ").append(pageSize);
        } else if (pageIndex != null && pageSize != null) {
            if (pageEndIndex == null) {
                sb.append(" limit ").append((pageIndex - 1) * pageSize).append(",").append(pageSize);
            } else {
                sb.append(" limit ").append((pageIndex - 1) * pageSize).append(",").append((pageEndIndex - pageIndex + 1) * pageSize);
            }
        }
        return sb.toString();
    }

    public static String getFullTableName(String dbName, String tableName) {
        if (StringUtils.isBlank(dbName)) {
            return tableName;
        }
        return dbName + "." + tableName;
    }

    public static String getCountSql4Jdbc(QueryWrapper qw) {
        Query query = qw.getQuery();
        String tableName = query.getTableName();
        List<Query.QueryCondition> queryConditions = query.getQueryCondition();
        List<Query.BusinessCondition> businessConditions = query.getBusinessCondition();
        //多种组合查询条件
        List<QueryV2.Condition> businessConditionV2 = query.getBusinessConditionV2();
        String[] groupColumns = query.getGroupColumns();
        List<Query.JoinConfig> joinConfigs = query.getJoin();
        StringBuilder sb = new StringBuilder();

        // 检查是否有JOIN配置且columns不是通配符
        boolean hasJoin = !CollectionUtils.isEmpty(joinConfigs) && !isWildcardColumns(query.getColumns());

        // 为JOIN表生成唯一别名
        Map<Integer, String> joinTableAliases = hasJoin ? generateUniqueTableAliases(joinConfigs) : new HashMap<>();

        sb.append("select count(*) total from ");
        String fullTableName = getFullTableName(query.getDbName(), tableName);

        if (hasJoin) {
            // 如果有JOIN，主表需要别名
            sb.append(fullTableName).append(" ").append(tableName);

            // 构建JOIN子句
            for (int i = 0; i < joinConfigs.size(); i++) {
                Query.JoinConfig joinConfig = joinConfigs.get(i);
                String joinTableAlias = joinTableAliases.get(i);
                sb.append(" ").append(joinConfig.getJoinType().toUpperCase()).append(" JOIN ");
                String joinTableName = getFullTableName(query.getDbName(), joinConfig.getSinkName());
                sb.append(joinTableName).append(" ").append(joinTableAlias);
                // 构建JOIN ON条件，处理函数包装的字段
                String mainFieldName = joinConfig.getJoinOn().getMainFieldName();
                String joinOnCondition = buildJoinOnCondition(tableName, mainFieldName, joinTableAlias, joinConfig.getJoinOn().getFieldName());
                sb.append(" ON ").append(joinOnCondition);
            }
        } else {
            sb.append(fullTableName);
        }

        // 构建WHERE子句
        if (hasJoin) {
            appendWhereWithTableAlias(sb, queryConditions, businessConditions, businessConditionV2, tableName, joinConfigs, joinTableAliases);
        } else {
            if (!CollectionUtils.isEmpty(queryConditions)) {
                sb.append(" where ").append(getCondition4Phoenix(queryConditions));
            }
            if (!CollectionUtils.isEmpty(businessConditions)) {
                if (sb.indexOf(" where ") > 0) {
                    sb.append(getBusinessCondition4Sql(businessConditions));
                } else {
                    sb.append(" where ").append(getBusinessCondition4Sql(businessConditions));
                }
            } else if (!CollectionUtils.isEmpty(businessConditionV2)) {
                sb.append(sb.indexOf(" where ") > 0 ? "" : " where ").append(getTagCondition4Sql(businessConditionV2));
            }
        }
        if (!ArrayUtils.isEmpty(groupColumns)) {
            sb.append(" group by ");
            for (int i = 0; i < groupColumns.length; i++) {
                String groupColumn = groupColumns[i];
                if (i != 0) {
                    sb.append(",");
                }
                if (hasJoin) {
                    sb.append(tableName).append(".").append(groupColumn);
                } else {
                    sb.append(groupColumn);
                }
            }
            sb.append(") tmp");
            sb.insert(0,"select count(*) total from (");
        }
        return sb.toString();
    }

    public static String getTimeGroupSql4Jdbc(QueryWrapper qw) {
        Query query = qw.getQuery();
        String tableName = query.getTableName();
        String[] columns = query.getColumns();
        List<Query.QueryCondition> queryConditions = query.getQueryCondition();
        List<Query.BusinessCondition> businessConditions = query.getBusinessCondition();
        List<QueryV2.Condition> businessConditionV2 = query.getBusinessConditionV2();
        Query.TimeGroup timeGroup = query.getTimeGroup();
        List<Query.JoinConfig> joinConfigs = query.getJoin();

        // 检查是否有JOIN配置且columns不是通配符
        boolean hasJoin = !CollectionUtils.isEmpty(joinConfigs) && !isWildcardColumns(columns);

        // 为JOIN表生成唯一别名
        Map<Integer, String> joinTableAliases = hasJoin ? generateUniqueTableAliases(joinConfigs) : new HashMap<>();

        columns = ArrayUtils.add(columns, getTimeGroup4Sql(timeGroup));
        StringBuilder sb = new StringBuilder();

        // 构建SELECT子句
        if (hasJoin) {
            String columnsStr = getColumnsForJoinQuery(query, columns, tableName, joinConfigs, joinTableAliases);
            sb.append("select ").append(columnsStr).append(" from ");
        } else {
            sb.append("select ").append(getColumns4Phoenix(columns)).append(" from ");
        }

        // 构建FROM子句
        String fullTableName = getFullTableName(query.getDbName(), tableName);
        if (hasJoin) {
            sb.append(fullTableName).append(" ").append(tableName);

            // 构建JOIN子句
            for (int i = 0; i < joinConfigs.size(); i++) {
                Query.JoinConfig joinConfig = joinConfigs.get(i);
                String joinTableAlias = joinTableAliases.get(i);
                sb.append(" ").append(joinConfig.getJoinType().toUpperCase()).append(" JOIN ");
                String joinTableName = getFullTableName(query.getDbName(), joinConfig.getSinkName());
                sb.append(joinTableName).append(" ").append(joinTableAlias);
                // 构建JOIN ON条件，处理函数包装的字段
                String mainFieldName = joinConfig.getJoinOn().getMainFieldName();
                String joinOnCondition = buildJoinOnCondition(tableName, mainFieldName, joinTableAlias, joinConfig.getJoinOn().getFieldName());
                sb.append(" ON ").append(joinOnCondition);
            }
        } else {
            sb.append(fullTableName);
        }

        appendExtremeTimeColCondition(query.getExtremeValueCondition(), fullTableName,
                queryConditions, businessConditions,businessConditionV2);

        // 构建WHERE子句
        if (hasJoin) {
            appendWhereWithTableAlias(sb, queryConditions, businessConditions, businessConditionV2, tableName, joinConfigs, joinTableAliases);
        } else {
            appendWhere(sb, queryConditions, businessConditions,businessConditionV2);
        }

        if (!ObjectUtils.isEmpty(timeGroup)) {
            sb.append(" group by groupTime");
            if (ArrayUtils.isNotEmpty(query.getGroupColumns())) {
                Arrays.stream(query.getGroupColumns()).filter(Objects::nonNull).forEach(column -> {
                    if (hasJoin) {
                        sb.append(",").append(tableName).append(".").append(column);
                    } else {
                        sb.append(",").append(column);
                    }
                });
            }
        }
        sb.append(" order by groupTime");
        return sb.toString();
    }

    private static void appendExtremeTimeColCondition(Query.ExtremeValueCondition extremeValueCondition,
                                                      String fullTableName,
                                                      List<Query.QueryCondition> queryConditions,
                                                      List<Query.BusinessCondition> businessConditions,
                                                      List<QueryV2.Condition> businessConditionV2) {
        if (Objects.isNull(extremeValueCondition)) {
            return;
        }
        String extremeType = extremeValueCondition.getExtremeType();
        String extremeValueCol = extremeValueCondition.getExtremeValueCol();
        StringBuilder maxTimeQuerySb = new StringBuilder("(select ");
        maxTimeQuerySb.append(extremeType).append("(").append(extremeValueCol).append(") from ")
                .append(fullTableName);
        if (extremeValueCondition.isUseSelfCondition()) {
            appendWhere(maxTimeQuerySb, queryConditions, businessConditions,businessConditionV2);
        }
        appendWhere(maxTimeQuerySb, null, extremeValueCondition.getBusinessConditionList(),businessConditionV2);
        maxTimeQuerySb.append(")");
        //理论上不会共用，看哪个有值就塞哪个
        boolean queryIsEmpty = CollectionUtils.isEmpty(queryConditions);
        if (!queryIsEmpty) {
            queryConditions.add(new Query.QueryCondition(extremeValueCol, SqlKeyword.EQ, maxTimeQuerySb.toString(),
                    false));
        }
        boolean businessIsEmpty = CollectionUtils.isEmpty(businessConditions);
        if (!businessIsEmpty || queryIsEmpty) {
            //业务条件不为空，或者两个都为空，那就找一个填进去
            businessConditions.add(new Query.BusinessCondition(extremeValueCol, "INT",
                    "=", maxTimeQuerySb.toString()));
        }
    }

    private static void appendWhere(StringBuilder sb, List<Query.QueryCondition> queryConditions,
                                    List<Query.BusinessCondition> businessConditions,List<QueryV2.Condition> businessConditionV2) {
        if (!CollectionUtils.isEmpty(queryConditions)) {
            sb.append(" where ").append(getCondition4Phoenix(queryConditions));
        }
        if (!CollectionUtils.isEmpty(businessConditions)) {
            if (sb.lastIndexOf(" where ") > 0) {
                sb.append(getBusinessCondition4Sql(businessConditions));
            } else {
                sb.append(" where ").append(getBusinessCondition4Sql(businessConditions));
            }
        } else if (!CollectionUtils.isEmpty(businessConditionV2)) {
            sb.append(sb.lastIndexOf(" where ") > 0 ? "" : " where ").append(getTagCondition4Sql(businessConditionV2));
        }
    }

    public static String getColumns4Phoenix(String[] columns) {
        String join = StringUtils.join(columns, ",");
        return join;
    }

    /**
     * 根据Query配置获取查询列
     * 如果配置了keyColumn、showColumns、windowColumns，则使用这些配置的fieldName
     * 否则使用原来的columns配置
     */
    public static String getColumnsForQuery(Query query, String[] columns) {
        // 检查是否有自定义列配置
        if (query.getKeyColumn() != null || !CollectionUtils.isEmpty(query.getShowColumns()) || !CollectionUtils.isEmpty(query.getWindowColumns())) {
            List<String> customColumns = new ArrayList<>();

            // 添加keyColumn
            if (query.getKeyColumn() != null) {
                customColumns.add(query.getKeyColumn().getFieldName());
            }

            // 添加showColumns
            if (!CollectionUtils.isEmpty(query.getShowColumns())) {
                for (Query.ColumnConfig showColumn : query.getShowColumns()) {
                    customColumns.add(showColumn.getFieldName());
                }
            }

            // 添加windowColumns
            if (!CollectionUtils.isEmpty(query.getWindowColumns())) {
                for (Query.ColumnConfig windowColumn : query.getWindowColumns()) {
                    customColumns.add(windowColumn.getFieldName());
                }
            }

            return StringUtils.join(customColumns, ",");
        }

        // 使用原来的columns配置
        return getColumns4Phoenix(columns);
    }

    public static String buildVal(String fieldType, String val) {
        if (FieldType.INT.toString().equalsIgnoreCase(fieldType) || FieldType.BIGINT.toString().equalsIgnoreCase(fieldType) ||
                FieldType.BIT.toString().equalsIgnoreCase(fieldType) || FieldType.DECIMAL.toString().equalsIgnoreCase(fieldType) ||
                FieldType.BOOLEAN.toString().equalsIgnoreCase(fieldType)) {
            return val;
        } else {
            return "'" + val + "'";
        }
    }

    private static String getVal(String fieldType, Object val, boolean in) {
        if (in) {
            String[] dataStr = val.toString().split(",");
            String result = "(";
            for (int i = 0; i < dataStr.length; i++) {
                result += buildVal(fieldType, dataStr[i]);
                if (i != dataStr.length - 1) {
                    result += ",";
                }
            }
            result += ")";
            return result;
        } else {
            return buildVal(fieldType, val.toString());
        }
    }

    public static String buildBusinessCondition4Sql(Query.BusinessCondition businessCondition) {
        String fieldCode = businessCondition.getFieldCode();
        String fieldType = businessCondition.getFieldType();
        String operator = businessCondition.getOperator();
        Object operatorValue = businessCondition.getOperatorValue();
        Object leftOperatorValue = businessCondition.getLeftOperatorValue();
        Object rightOperatorValue = businessCondition.getRightOperatorValue();
        StringBuilder sb = new StringBuilder();
        if (operator.equals("=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" = ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("!=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" != ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equalsIgnoreCase("exist")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" LIKE '%" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("notexist")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" NOT LIKE '%" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("start")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" LIKE '" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("notstart")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" NOT LIKE '" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("end")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" LIKE '%" + operatorValue.toString() + "'");
        } else if (operator.equalsIgnoreCase("notend")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" NOT LIKE '%" + operatorValue.toString() + "'");
        } else if (operator.equals(">")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" > ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals(">=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" >= ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("<")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append(" < ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("<=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(fieldCode).append("<=").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equalsIgnoreCase("in")) {
            if (operatorValue == null || StringUtils.isBlank(operatorValue.toString())) {
                return "";
            }
            sb.append(fieldCode).append(" in ").append(getVal(fieldType, operatorValue, true));
        }else if (operator.equalsIgnoreCase("notin")) {
            if (operatorValue == null || StringUtils.isBlank(operatorValue.toString())) {
                return "";
            }
            sb.append(fieldCode).append(" not in ").append(getVal(fieldType, operatorValue, true));
        } else if (operator.equalsIgnoreCase("isnull")) {
            sb.append(fieldCode).append(" is null ");
        } else if (operator.equalsIgnoreCase("isnotnull")) {
            sb.append(fieldCode).append(" is not null ");
        } else if (operator.equalsIgnoreCase("isempty")) {
            sb.append(fieldCode).append(" = ''");
        } else if (operator.equalsIgnoreCase("isnotempty")) {
            sb.append(fieldCode).append(" != ''");
        } else if (operator.equalsIgnoreCase("between")) {
            sb.append(fieldCode).append(" BETWEEN ").append(getVal(fieldType, leftOperatorValue, false)).append(" and ").append(getVal(fieldType, rightOperatorValue, false));
        } else {
            return "";
        }
        return sb.toString();
    }

    public static String getBusinessCondition4Sql(List<Query.BusinessCondition> businessConditions) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < businessConditions.size(); i++) {
            if (i != 0) {
                sb.append(" and ");
            }
            sb.append(buildBusinessCondition4Sql(businessConditions.get(i)));
        }
        return sb.toString();
    }

    private static String getTagCondition4Sql(List<QueryV2.Condition> businessConditionV2) {
        StringBuilder sb = new StringBuilder();
        List<QueryV2.Condition> sortedBusinessConditionV2 = businessConditionV2.stream()
                .sorted(Comparator.comparing(QueryV2.Condition::getOrderNum)).collect(Collectors.toList());
        for (int i = 0; i < sortedBusinessConditionV2.size(); i++) {
            QueryV2.Condition condition = sortedBusinessConditionV2.get(i);
            boolean isEnd = i == sortedBusinessConditionV2.size() - 1;
            // ()
            if (StringUtils.isNoneEmpty(condition.getLeftBracket()) && StringUtils.isNoneEmpty(condition.getRightBracket())) {
                sb.append(condition.getLeftBracket())
                        .append(buildBusinessCondition4Sql(condition))
                        .append(condition.getRightBracket());
                if (!isEnd) {
                    sb.append(" ")
                            .append(condition.getLogic().name())
                            .append(" ");
                }

            } else if (StringUtils.isNoneEmpty(condition.getRightBracket())) {
                // )
                sb.append(buildBusinessCondition4Sql(condition))
                        .append(condition.getRightBracket());
                if (!isEnd) {
                    sb.append(" ")
                            .append(condition.getLogic().name())
                            .append(" ");
                }
            } else if (StringUtils.isNoneEmpty(condition.getLeftBracket())) {
                // ( 一定不会是最后一个
                sb.append(condition.getLeftBracket())
                        .append(buildBusinessCondition4Sql(condition))
                        .append(" ").append(condition.getLogic().name())
                        .append(" ");
            } else {
                // 左右括号都不存在 直接拼接条件
                sb.append(buildBusinessCondition4Sql(condition));
                if (!isEnd) {
                    sb.append(" ")
                            .append(condition.getLogic().name())
                            .append(" ");
                }
            }
        }
        return sb.toString();
    }

    public static String getCondition4Phoenix(List<Query.QueryCondition> queryConditions) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < queryConditions.size(); i++) {
            if (i != 0) {
                sb.append(" and ");
            }
            Query.QueryCondition queryCondition = queryConditions.get(i);
            SqlKeyword keyword = queryCondition.getKeyword();
            String column = queryCondition.getColumn();
            Object val = queryCondition.getVal();
            String sqlSegment = SqlKeyword.getSqlSegment(keyword);
            sb.append(column).append(" " + sqlSegment + " ");
            if (queryCondition.isQuotation()) {
                if (SqlKeyword.LIKE.equals(keyword)) {
                    sb.append("'%" + val + "%'");
                } else {
                    sb.append("'" + val + "'");
                }
            } else {
                sb.append(val);
            }
        }
        return sb.toString();
    }

    public static String getOrder4Phoenix(List<Query.QueryOrder> orderFields) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < orderFields.size(); i++) {
            Query.QueryOrder queryOrder = orderFields.get(i);
            String column = queryOrder.getColumn();
            String ord = queryOrder.getOrd();
            sb.append(column).append(" ").append(ord);
            if (i < orderFields.size() - 1) {
                sb.append(" , ");
            }
        }
        return sb.toString();
    }

    public static String getTimeGroup4Sql(Query.TimeGroup timeGroup) {
        StringBuilder sb = new StringBuilder("DATE_FORMAT(concat(");
        String timeCol = timeGroup.getTimeCol();
        UnitType timeUnit = timeGroup.getTimeUnit();
        int groupInterval = timeGroup.getGroupInterval();
        String groupYear = "YEAR(" + timeCol + ")";
        String groupMonth = "MONTH(" + timeCol + ")";
        String groupDay = "DAY(" + timeCol + ")";
        String groupDate = "DATE(" + timeCol + ")";
        String groupHour = "HOUR(" + timeCol + ")";
        String groupMinute = "MINUTE(" + timeCol + ")";
        String groupSecond = "SECOND(" + timeCol + ")";
        String groupStr = "floor(%s/" + groupInterval + ")*" + groupInterval;
        switch (timeUnit) {
            case SECOND:
                sb.append(groupDate).append(",' ',").append(groupHour).append(",':',").append(groupMinute)
                        .append(",':',").append(String.format(groupStr, groupSecond)).append("),'%Y-%m-%d %H:%i:%s')");
                break;
            case MINUTE:
                sb.append(groupDate).append(",' ',").append(groupHour).append(",':',").append(String.format(groupStr, groupMinute))
                        .append("),'%Y-%m-%d %H:%i')");
                break;
            case HOUR:
                sb.append(groupDate).append(",' ',").append(String.format(groupStr, groupHour)).append("),'%Y-%m-%d %H')");
                break;
            case DAY:
                sb.append(groupYear).append(",'-',").append(groupMonth).append(",'-',").append(String.format(groupStr, groupDay))
                        .append("),'%Y-%m-%d')");
                break;
            default:
                sb.append(groupDate).append(",' ',").append(String.format(groupStr, groupHour)).append("),'%Y-%m-%d %H')");
                break;
        }
        return sb.append(" AS groupTime ").toString();
    }

    public static List<WebLogQuery.WebLogCondition> buildWebLogCondition(List<WebLogQuery.WebLogCondition> webLogCondition) {
        if (CollectionUtils.isEmpty(webLogCondition)) {
            return null;
        }
        return webLogCondition.stream()
                .filter(o -> (o.getOperatorValue() != null && StringUtils.isNotBlank(o.getOperatorValue().toString())) ||
                        (o.getLeftOperatorValue() != null && StringUtils.isNotBlank(o.getLeftOperatorValue().toString())) ||
                        (o.getRightOperatorValue() != null && StringUtils.isNotBlank(o.getRightOperatorValue().toString())))
                .peek(o -> {
                    if (!ObjectUtils.isEmpty(o.getOperatorValue()) && StringUtils.isNotBlank(o.getOperatorValue().toString())) {
                        String operatorValue = o.getOperatorValue().toString()
                                .replace("{", "").replace("}", "");
                        o.setOperatorValue(operatorValue);
                    }
                    if (FieldType.DATETIME.toString().equals(o.getFieldType())) {
                        if (!ObjectUtils.isEmpty(o.getLeftOperatorValue()) && StringUtils.isNotBlank(o.getLeftOperatorValue().toString())) {
                            String leftOperatorValue = o.getLeftOperatorValue().toString();
                            String sb = leftOperatorValue.substring(0, 4) +
                                    "-" +
                                    leftOperatorValue.substring(4, 6) +
                                    "-" +
                                    leftOperatorValue.substring(6, 8) +
                                    " " +
                                    leftOperatorValue.substring(8, 10) +
                                    ":" +
                                    leftOperatorValue.substring(10, 12) +
                                    ":" +
                                    leftOperatorValue.substring(12, 14);
                            o.setLeftOperatorValue(sb);
                        }
                        if (!ObjectUtils.isEmpty(o.getRightOperatorValue()) && StringUtils.isNotBlank(o.getRightOperatorValue().toString())) {
                            String rightOperatorValue = o.getRightOperatorValue().toString();
                            String sb = rightOperatorValue.substring(0, 4) +
                                    "-" +
                                    rightOperatorValue.substring(4, 6) +
                                    "-" +
                                    rightOperatorValue.substring(6, 8) +
                                    " " +
                                    rightOperatorValue.substring(8, 10) +
                                    ":" +
                                    rightOperatorValue.substring(10, 12) +
                                    ":" +
                                    rightOperatorValue.substring(12, 14);
                            o.setRightOperatorValue(sb);
                        }
                    }
                }).collect(Collectors.toList());
    }

    public static Query.TimeGroup buildTimeGroupByWebLog(WebLogQuery.WebLogCondition webLogCondition) {
        String leftOperatorValue = webLogCondition.getLeftOperatorValue().toString();
        if (!StringUtils.isNotBlank(leftOperatorValue)) {
            return null;
        }
        String rightOperatorValue = webLogCondition.getRightOperatorValue().toString();
        if (!StringUtils.isNotBlank(rightOperatorValue)) {
            return null;
        }
        LocalDateTime beginTime = DateUtil.tryParseLocalDateTime(leftOperatorValue, DateUtil.DATE_TIME_FORMATTER).orElseGet(() -> null);
        if (ObjectUtils.isEmpty(beginTime)) {
            return null;
        }
        LocalDateTime endTime = DateUtil.tryParseLocalDateTime(rightOperatorValue, DateUtil.DATE_TIME_FORMATTER).orElseGet(() -> null);
        if (ObjectUtils.isEmpty(endTime)) {
            return null;
        }
        if (beginTime.plusHours(4).compareTo(endTime) > 0) {
            return new Query.TimeGroup(webLogCondition.getFieldCode(), 1, UnitType.MINUTE);
        } else if (beginTime.plusHours(4).compareTo(endTime) <= 0 && beginTime.plusHours(12).compareTo(endTime) > 0) {
            return new Query.TimeGroup(webLogCondition.getFieldCode(), 10, UnitType.MINUTE);
        } else if (beginTime.plusHours(12).compareTo(endTime) <= 0 && beginTime.plusHours(24).compareTo(endTime) > 0) {
            return new Query.TimeGroup(webLogCondition.getFieldCode(), 30, UnitType.MINUTE);
        } else if (beginTime.plusHours(24).compareTo(endTime) <= 0 && beginTime.plusHours(7L * 24L).compareTo(endTime) > 0) {
            return new Query.TimeGroup(webLogCondition.getFieldCode(), 1, UnitType.HOUR);
        } else {
            return new Query.TimeGroup(webLogCondition.getFieldCode(), 1, UnitType.DAY);
        }
    }

    public String getSql4Imapla(QueryWrapper qw) {
        return "";
    }

    /**
     * 检查columns是否包含通配符
     */
    public static boolean isWildcardColumns(String[] columns) {
        if (ArrayUtils.isEmpty(columns)) {
            return false;
        }
        for (String column : columns) {
            if ("*".equals(column)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 为JOIN配置生成唯一的表别名映射
     * 如果多个JOIN指向同一个表，会生成不同的别名（如：table1, table1_2, table1_3）
     */
    public static Map<Integer, String> generateUniqueTableAliases(List<Query.JoinConfig> joinConfigs) {
        Map<Integer, String> aliasMap = new HashMap<>();
        Map<String, Integer> tableNameCounter = new HashMap<>();

        for (int i = 0; i < joinConfigs.size(); i++) {
            Query.JoinConfig joinConfig = joinConfigs.get(i);
            String sinkName = joinConfig.getSinkName();

            // 计算该表名出现的次数
            int count = tableNameCounter.getOrDefault(sinkName, 0) + 1;
            tableNameCounter.put(sinkName, count);

            // 生成唯一别名
            String alias = count == 1 ? sinkName : sinkName + "_" + count;
            aliasMap.put(i, alias);
        }

        return aliasMap;
    }

    /**
     * 为JOIN查询构建列字符串
     */
    public static String getColumnsForJoinQuery(Query query, String[] columns, String mainTableName, List<Query.JoinConfig> joinConfigs) {
        // 为了向后兼容，生成默认的别名映射
        Map<Integer, String> joinTableAliases = new HashMap<>();
        for (int i = 0; i < joinConfigs.size(); i++) {
            joinTableAliases.put(i, joinConfigs.get(i).getSinkName());
        }
        return getColumnsForJoinQuery(query, columns, mainTableName, joinConfigs, joinTableAliases);
    }

    /**
     * 为JOIN查询构建列字符串（使用唯一别名）
     */
    public static String getColumnsForJoinQuery(Query query, String[] columns, String mainTableName,
                                              List<Query.JoinConfig> joinConfigs, Map<Integer, String> joinTableAliases) {
        final String separatorInSql = ", '" + MUTIL_VALUE_JOIN_SIGN + "', ";

        List<String> allColumns = new ArrayList<>();

        // 添加主表的列，并加上表别名和AS子句
        for (String column : columns) {
            allColumns.add(mainTableName + "." + column + " as " + column);
        }

        // 添加JOIN表的showColumns
        for (int i = 0; i < joinConfigs.size(); i++) {
            Query.JoinConfig joinConfig = joinConfigs.get(i);
            if (!CollectionUtils.isEmpty(joinConfig.getShowColumns())) {
                String joinTableAlias = joinTableAliases.get(i);

                // 1. 使用Stream将每个列配置转换为 "tableAlias.fieldName" 格式的字符串
                String concatenatedColumns = joinConfig.getShowColumns().stream()
                        .map(showColumn -> joinTableAlias + "." + showColumn.getFieldName())
                        .collect(Collectors.joining(separatorInSql)); // 2. 使用指定的SQL分隔符连接它们

                // 3. 构建完整的 aql 语句，别名使用原始字段名（不包含函数包装）
                String mainFieldName = joinConfig.getJoinOn().getMainFieldName();
                String extractedFieldName = extractFieldNameFromFunction(mainFieldName);
                String aliasName = mainTableName + "_" + extractedFieldName;
                String finalColumn = String.format("concat(%s) as %s", concatenatedColumns, aliasName);

                allColumns.add(finalColumn);
            }
        }

        return StringUtils.join(allColumns, ",");
    }

    /**
     * 为JOIN查询构建带表别名的WHERE子句
     */
    public static void appendWhereWithTableAlias(StringBuilder sb, List<Query.QueryCondition> queryConditions,
                                                List<Query.BusinessCondition> businessConditions,
                                                List<QueryV2.Condition> businessConditionV2, String mainTableAlias,
                                                List<Query.JoinConfig> joinConfigs) {
        // 为了向后兼容，生成默认的别名映射
        Map<Integer, String> joinTableAliases = new HashMap<>();
        for (int i = 0; i < joinConfigs.size(); i++) {
            joinTableAliases.put(i, joinConfigs.get(i).getSinkName());
        }
        appendWhereWithTableAlias(sb, queryConditions, businessConditions, businessConditionV2, mainTableAlias, joinConfigs, joinTableAliases);
    }

    /**
     * 为JOIN查询构建带表别名的WHERE子句（使用唯一别名）
     */
    public static void appendWhereWithTableAlias(StringBuilder sb, List<Query.QueryCondition> queryConditions,
                                                List<Query.BusinessCondition> businessConditions,
                                                List<QueryV2.Condition> businessConditionV2, String mainTableAlias,
                                                List<Query.JoinConfig> joinConfigs, Map<Integer, String> joinTableAliases) {
        boolean hasWhere = false;

        if (!CollectionUtils.isEmpty(queryConditions)) {
            sb.append(" where ").append(getConditionWithTableAlias(queryConditions, mainTableAlias));
            hasWhere = true;
        }

        if (!CollectionUtils.isEmpty(businessConditions)) {
            String businessConditionStr = getBusinessConditionWithTableAliasAndJoin(businessConditions, mainTableAlias, joinConfigs, joinTableAliases);
            if (StringUtils.isNotBlank(businessConditionStr)) {
                if (hasWhere) {
                    sb.append(" and ").append(businessConditionStr);
                } else {
                    sb.append(" where ").append(businessConditionStr);
                    hasWhere = true;
                }
            }
        } else if (!CollectionUtils.isEmpty(businessConditionV2)) {
            String businessConditionV2Str = getTagConditionWithTableAlias(businessConditionV2, mainTableAlias);
            if (StringUtils.isNotBlank(businessConditionV2Str)) {
                if (hasWhere) {
                    sb.append(" and ").append(businessConditionV2Str);
                } else {
                    sb.append(" where ").append(businessConditionV2Str);
                }
            }
        }
    }

    /**
     * 为JOIN查询构建带表别名的ORDER BY子句
     */
    public static String getOrderWithTableAlias(List<Query.QueryOrder> orderFields, String mainTableAlias) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < orderFields.size(); i++) {
            if (i != 0) {
                sb.append(",");
            }
            Query.QueryOrder queryOrder = orderFields.get(i);
            sb.append(mainTableAlias).append(".").append(queryOrder.getColumn()).append(" ").append(queryOrder.getOrd());
        }
        return sb.toString();
    }

    /**
     * 构建带表别名的查询条件
     */
    public static String getConditionWithTableAlias(List<Query.QueryCondition> queryConditions, String tableAlias) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < queryConditions.size(); i++) {
            if (i != 0) {
                sb.append(" and ");
            }
            Query.QueryCondition queryCondition = queryConditions.get(i);
            SqlKeyword keyword = queryCondition.getKeyword();
            String column = queryCondition.getColumn();
            Object val = queryCondition.getVal();
            String sqlSegment = SqlKeyword.getSqlSegment(keyword);
            sb.append(tableAlias).append(".").append(column).append(" " + sqlSegment + " ");
            if (queryCondition.isQuotation()) {
                if (SqlKeyword.LIKE.equals(keyword)) {
                    sb.append("'%" + val + "%'");
                } else {
                    sb.append("'" + val + "'");
                }
            } else {
                sb.append(val);
            }
        }
        return sb.toString();
    }

    /**
     * 构建带表别名的业务条件
     */
    public static String getBusinessConditionWithTableAlias(List<Query.BusinessCondition> businessConditions, String tableAlias) {
        StringBuilder sb = new StringBuilder();
        boolean isFirst = true;
        for (Query.BusinessCondition businessCondition : businessConditions) {
            String condition = buildBusinessConditionWithTableAlias(businessCondition, tableAlias);
            if (StringUtils.isNotBlank(condition)) {
                if (!isFirst) {
                    sb.append(" and ");
                }
                sb.append(condition);
                isFirst = false;
            }
        }
        return sb.toString();
    }

    /**
     * 构建带表别名和JOIN支持的业务条件
     * 如果businessCondition的fieldCode对应某个JOIN表的mainFieldName，
     * 则使用JOIN表的showColumns来构建搜索条件
     */
    public static String getBusinessConditionWithTableAliasAndJoin(List<Query.BusinessCondition> businessConditions,
                                                                  String tableAlias,
                                                                  List<Query.JoinConfig> joinConfigs) {
        // 为了向后兼容，生成默认的别名映射
        Map<Integer, String> joinTableAliases = new HashMap<>();
        for (int i = 0; i < joinConfigs.size(); i++) {
            joinTableAliases.put(i, joinConfigs.get(i).getSinkName());
        }
        return getBusinessConditionWithTableAliasAndJoin(businessConditions, tableAlias, joinConfigs, joinTableAliases);
    }

    /**
     * 构建带表别名和JOIN支持的业务条件（使用唯一别名）
     * 如果businessCondition的fieldCode对应某个JOIN表的mainFieldName，
     * 则使用JOIN表的showColumns来构建搜索条件
     */
    public static String getBusinessConditionWithTableAliasAndJoin(List<Query.BusinessCondition> businessConditions,
                                                                  String tableAlias,
                                                                  List<Query.JoinConfig> joinConfigs,
                                                                  Map<Integer, String> joinTableAliases) {
        StringBuilder sb = new StringBuilder();
        boolean isFirst = true;
        for (Query.BusinessCondition businessCondition : businessConditions) {
            String condition = buildBusinessConditionWithTableAliasAndJoin(businessCondition, tableAlias, joinConfigs, joinTableAliases);
            if (StringUtils.isNotBlank(condition)) {
                if (!isFirst) {
                    sb.append(" and ");
                }
                sb.append(condition);
                isFirst = false;
            }
        }
        return sb.toString();
    }

    /**
     * 构建带表别名的标签条件
     */
    public static String getTagConditionWithTableAlias(List<QueryV2.Condition> businessConditionV2, String tableAlias) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < businessConditionV2.size(); i++) {
            QueryV2.Condition condition = businessConditionV2.get(i);
            String conditionStr = buildBusinessConditionWithTableAlias(condition, tableAlias);
            if (StringUtils.isNotBlank(conditionStr)) {
                if (i > 0) {
                    sb.append(" ").append(condition.getLogic().name()).append(" ");
                }
                if (StringUtils.isNotBlank(condition.getLeftBracket())) {
                    sb.append(condition.getLeftBracket());
                }
                sb.append(conditionStr);
                if (StringUtils.isNotBlank(condition.getRightBracket())) {
                    sb.append(condition.getRightBracket());
                }
            }
        }
        return sb.toString();
    }

    /**
     * 构建单个带表别名的业务条件
     */
    public static String buildBusinessConditionWithTableAlias(Query.BusinessCondition businessCondition, String tableAlias) {
        String fieldCode = businessCondition.getFieldCode();
        String fieldType = businessCondition.getFieldType();
        String operator = businessCondition.getOperator();
        Object operatorValue = businessCondition.getOperatorValue();
        Object leftOperatorValue = businessCondition.getLeftOperatorValue();
        Object rightOperatorValue = businessCondition.getRightOperatorValue();
        StringBuilder sb = new StringBuilder();

        if (operator.equals("=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" = ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("!=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" != ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equalsIgnoreCase("exist")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" LIKE '%" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("notexist")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" NOT LIKE '%" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("start")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" LIKE '" + operatorValue.toString() + "%'");
        } else if (operator.equalsIgnoreCase("end")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" LIKE '%" + operatorValue.toString() + "'");
        } else if (operator.equalsIgnoreCase("in")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" in ").append(getVal(fieldType, operatorValue, true));
        } else if (operator.equalsIgnoreCase("notin")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" not in ").append(getVal(fieldType, operatorValue, true));
        } else if (operator.equals(">")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" > ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals(">=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" >= ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("<")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" < ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equals("<=")) {
            if (operatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" <= ").append(getVal(fieldType, operatorValue, false));
        } else if (operator.equalsIgnoreCase("between")) {
            if (leftOperatorValue == null || rightOperatorValue == null) {
                return "";
            }
            sb.append(tableAlias).append(".").append(fieldCode).append(" between ").append(getVal(fieldType, leftOperatorValue, false))
                    .append(" and ").append(getVal(fieldType, rightOperatorValue, false));
        } else if (operator.equalsIgnoreCase("isnull")) {
            sb.append(tableAlias).append(".").append(fieldCode).append(" is null");
        } else if (operator.equalsIgnoreCase("isnotnull")) {
            sb.append(tableAlias).append(".").append(fieldCode).append(" is not null");
        }
        return sb.toString();
    }

    /**
     * 构建单个带表别名和JOIN支持的业务条件
     * 如果businessCondition的fieldCode对应某个JOIN表的mainFieldName，
     * 则使用JOIN表的showColumns来构建搜索条件
     */
    public static String buildBusinessConditionWithTableAliasAndJoin(Query.BusinessCondition businessCondition,
                                                                    String tableAlias,
                                                                    List<Query.JoinConfig> joinConfigs) {
        // 为了向后兼容，生成默认的别名映射
        Map<Integer, String> joinTableAliases = new HashMap<>();
        for (int i = 0; i < joinConfigs.size(); i++) {
            joinTableAliases.put(i, joinConfigs.get(i).getSinkName());
        }
        return buildBusinessConditionWithTableAliasAndJoin(businessCondition, tableAlias, joinConfigs, joinTableAliases);
    }

    /**
     * 构建单个带表别名和JOIN支持的业务条件（使用唯一别名）
     * 如果businessCondition的fieldCode对应某个JOIN表的mainFieldName，
     * 则使用JOIN表的showColumns来构建搜索条件
     */
    public static String buildBusinessConditionWithTableAliasAndJoin(Query.BusinessCondition businessCondition,
                                                                    String tableAlias,
                                                                    List<Query.JoinConfig> joinConfigs,
                                                                    Map<Integer, String> joinTableAliases) {
        String fieldCode = businessCondition.getFieldCode();

        // 检查是否有JOIN配置，且fieldCode对应某个JOIN表的mainFieldName
        JoinConfigWithIndex matchedJoinConfig = findJoinConfigWithIndexByMainFieldName(fieldCode, joinConfigs);

        if (matchedJoinConfig != null) {
            // 使用JOIN表的showColumns构建搜索条件，使用唯一别名
            String joinTableAlias = joinTableAliases.get(matchedJoinConfig.index);
            return buildJoinTableSearchCondition(businessCondition, matchedJoinConfig.joinConfig, joinTableAlias);
        } else {
            // 使用原有逻辑，在主表中搜索
            return buildBusinessConditionWithTableAlias(businessCondition, tableAlias);
        }
    }

    /**
     * 根据mainFieldName查找对应的JoinConfig（向后兼容版本）
     */
    private static Query.JoinConfig findJoinConfigByMainFieldName(String fieldCode, List<Query.JoinConfig> joinConfigs) {
        JoinConfigWithIndex result = findJoinConfigWithIndexByMainFieldName(fieldCode, joinConfigs);
        return result != null ? result.joinConfig : null;
    }

    /**
     * 根据mainFieldName查找对应的JoinConfig和索引
     * 支持函数包装的字段匹配，如lower(test_manufacturer)匹配test_manufacturer
     */
    private static JoinConfigWithIndex findJoinConfigWithIndexByMainFieldName(String fieldCode, List<Query.JoinConfig> joinConfigs) {
        if (CollectionUtils.isEmpty(joinConfigs)) {
            return null;
        }

        // 提取函数包装字段的原始字段名
        String extractedFieldName = extractFieldNameFromFunction(fieldCode);

        for (int i = 0; i < joinConfigs.size(); i++) {
            Query.JoinConfig joinConfig = joinConfigs.get(i);
            if (joinConfig.getJoinOn() != null) {
                String mainFieldName = joinConfig.getJoinOn().getMainFieldName();
                // 直接匹配或函数包装字段匹配
                if (fieldCode.equals(mainFieldName) || extractedFieldName.equals(mainFieldName)) {
                    return new JoinConfigWithIndex(i, joinConfig);
                }
            }
        }
        return null;
    }

    /**
     * 从函数包装的字段代码中提取原始字段名
     * 例如：lower(test_manufacturer) -> test_manufacturer
     */
    private static String extractFieldNameFromFunction(String fieldCode) {
        if (StringUtils.isBlank(fieldCode)) {
            return fieldCode;
        }

        // 匹配常见的SQL函数模式：function_name(field_name)
        String pattern = "^\\w+\\(([^)]+)\\)$";
        if (fieldCode.matches(pattern)) {
            return fieldCode.replaceAll(pattern, "$1").trim();
        }

        // 如果不匹配函数模式，返回原始字段代码
        return fieldCode;
    }

    /**
     * 构建JOIN表的搜索条件
     * 使用JOIN表的showColumns作为外显值进行搜索
     */
    private static String buildJoinTableSearchCondition(Query.BusinessCondition businessCondition,
                                                       Query.JoinConfig joinConfig) {
        return buildJoinTableSearchCondition(businessCondition, joinConfig, joinConfig.getSinkName());
    }

    /**
     * 构建JOIN表的搜索条件（使用指定别名）
     * 使用JOIN表的showColumns作为外显值进行搜索，支持函数包装的字段
     */
    private static String buildJoinTableSearchCondition(Query.BusinessCondition businessCondition,
                                                       Query.JoinConfig joinConfig, String joinTableAlias) {
        String operator = businessCondition.getOperator();
        String fieldCode = businessCondition.getFieldCode();
        Object operatorValue = businessCondition.getOperatorValue();
        String fieldType = businessCondition.getFieldType();

        if (operatorValue == null) {
            return "";
        }

        List<Query.ColumnConfig> showColumns = joinConfig.getShowColumns();
        if (CollectionUtils.isEmpty(showColumns)) {
            return "";
        }

        // 提取函数包装信息
        String functionWrapper = extractFunctionWrapper(fieldCode);

        StringBuilder sb = new StringBuilder();

        // 如果有多个showColumns，使用OR连接
        if (showColumns.size() > 1) {
            sb.append("(");
        }

        for (int i = 0; i < showColumns.size(); i++) {
            if (i > 0) {
                sb.append(" OR ");
            }

            Query.ColumnConfig showColumn = showColumns.get(i);
            String columnName = showColumn.getFieldName();

            // 构建带函数包装的列名
            String wrappedColumnName = applyFunctionWrapper(joinTableAlias + "." + columnName, functionWrapper);

            // 根据操作符构建条件
            if (operator.equalsIgnoreCase("exist") || operator.equalsIgnoreCase("like")) {
                sb.append(wrappedColumnName)
                  .append(" LIKE '%").append(operatorValue.toString()).append("%'");
            } else if (operator.equalsIgnoreCase("notexist")) {
                sb.append(wrappedColumnName)
                  .append(" NOT LIKE '%").append(operatorValue.toString()).append("%'");
            } else if (operator.equalsIgnoreCase("start")) {
                sb.append(wrappedColumnName)
                  .append(" LIKE '").append(operatorValue.toString()).append("%'");
            } else if (operator.equalsIgnoreCase("end")) {
                sb.append(wrappedColumnName)
                  .append(" LIKE '%").append(operatorValue.toString()).append("'");
            } else if (operator.equals("=")) {
                sb.append(wrappedColumnName)
                  .append(" = ").append(getVal(fieldType, operatorValue, false));
            } else if (operator.equals("!=")) {
                sb.append(wrappedColumnName)
                  .append(" != ").append(getVal(fieldType, operatorValue, false));
            } else if (operator.equalsIgnoreCase("in")) {
                sb.append(wrappedColumnName)
                  .append(" in ").append(getVal(fieldType, operatorValue, true));
            } else if (operator.equalsIgnoreCase("notin")) {
                sb.append(wrappedColumnName)
                  .append(" not in ").append(getVal(fieldType, operatorValue, true));
            } else {
                // 对于其他操作符，默认使用LIKE进行模糊搜索
                sb.append(wrappedColumnName)
                  .append(" LIKE '%").append(operatorValue.toString()).append("%'");
            }
        }

        if (showColumns.size() > 1) {
            sb.append(")");
        }

        return sb.toString();
    }

    /**
     * 提取函数包装器
     * 例如：lower(test_manufacturer) -> lower
     */
    private static String extractFunctionWrapper(String fieldCode) {
        if (StringUtils.isBlank(fieldCode)) {
            return null;
        }

        // 匹配函数模式：function_name(field_name)
        String pattern = "^(\\w+)\\([^)]+\\)$";
        if (fieldCode.matches(pattern)) {
            return fieldCode.replaceAll(pattern, "$1");
        }

        return null;
    }

    /**
     * 应用函数包装器到列名
     * 例如：applyFunctionWrapper("table.column", "lower") -> "lower(table.column)"
     */
    private static String applyFunctionWrapper(String columnName, String functionWrapper) {
        if (StringUtils.isBlank(functionWrapper)) {
            return columnName;
        }

        return functionWrapper + "(" + columnName + ")";
    }

    /**
     * 构建JOIN ON条件，处理函数包装的字段
     * 例如：buildJoinOnCondition("test11", "lower(test_manufacturer)", "AssetManufacturer", "assetId")
     * 生成：lower(test11.test_manufacturer) = AssetManufacturer.assetId
     */
    private static String buildJoinOnCondition(String mainTableName, String mainFieldName, String joinTableAlias, String joinFieldName) {
        // 提取函数包装信息
        String functionWrapper = extractFunctionWrapper(mainFieldName);
        String extractedMainFieldName = extractFieldNameFromFunction(mainFieldName);

        // 构建主表字段部分
        String mainTableField = mainTableName + "." + extractedMainFieldName;
        if (functionWrapper != null) {
            mainTableField = functionWrapper + "(" + mainTableField + ")";
        }

        // 构建JOIN表字段部分
        String joinTableField = joinTableAlias + "." + joinFieldName;

        return mainTableField + " = " + joinTableField;
    }

}
