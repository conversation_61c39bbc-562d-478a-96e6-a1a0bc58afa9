package com.digiwin.escloud.aioitms.report.constant;

import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport.BaseSpecification;
import com.digiwin.escloud.aioitms.report.model.db.SqlServerErpReport.Product.ProductModel;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL Server EDR 報告相關常量
 */
public final class SqlServerEdrReportConst {
    // ERP產品"代碼":"值"對照
    public static final List<ProductModel> ERP_PRODUCT_LIST = Collections.unmodifiableList(
            Arrays.asList(
                    new ProductModel("02", "WF-ERP"),
                    new ProductModel("61", "CosmosERP"),
                    new ProductModel("10", "SERP")));

    // ERPII產品"代碼":"值"對照
    public static final List<ProductModel> ERP2_PRODUCT_LIST = Collections.unmodifiableList(
            Arrays.asList(
                    new ProductModel("151", "智聯雲倉軟體"),
                    new ProductModel("154", "智聯雲采"),
                    new ProductModel("142", "智能物流管理系統"),
                    new ProductModel("KMI", "機台整合平"),
                    new ProductModel("156", "sQMS"),
                    new ProductModel("SPC", "SPC"),
                    new ProductModel("137", "sMES"),
                    new ProductModel("SFT", "SFT"),
                    new ProductModel("POT", "PORTAL"),
                    new ProductModel("PLM", "PLM"),
                    new ProductModel("PDM", "PDM"),
                    new ProductModel("OPD", "OPEN PDM"),
                    new ProductModel("MD", "MD"),
                    new ProductModel("136", "IMG"),
                    new ProductModel("MES", "iMES"),
                    new ProductModel("HR", "HR"),
                    new ProductModel("HEP", "HEP"),
                    new ProductModel("eKB", "eKB"),
                    new ProductModel("EIS", "EIS"),
                    new ProductModel("31", "EFGP"),
                    new ProductModel("05", "e-B Chain"),
                    new ProductModel("38", "EasyFlow"),
                    new ProductModel("EAI", "EAI"),
                    new ProductModel("CRM", "CRM"),
                    new ProductModel("BSC", "BSC"),
                    new ProductModel("BI", "BI"),
                    new ProductModel("Z02", "B2B"),
                    new ProductModel("APS", "APS(IPPC)"),
                    new ProductModel("145", "145智戰情"),
                    new ProductModel("POS", "CosmosPOS")));

    public static final Pattern IPV4_PATTERN = Pattern.compile(
            "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    public static final String HAS = "有";

    public static final String NO_HAS = "無";

    public static final String YES = "是";

    public static final String NO = "否";

    public static final BaseSpecification WIN10_SPECIFICATION = createBaseSpecification(
            "Windows 10 Professional",
            "Intel Core i5 (含)以上 主流CPU",
            "剩餘空間至少30GB以上, 建議SSD",
            "建議8GB以上",
            "最新",
            "1Gbps",
            "WFGP4、WFIGP、WFAIGP、CMIGP、CMAIGP、SM9、ISM、AISM"
    );

    public static final BaseSpecification WIN11_SPECIFICATION = createBaseSpecification(
            "Windows 11 Professional",
            "Intel Core i5 (含)以上 主流CPU",
            "剩餘空間至少30GB以上, 建議SSD",
            "建議8GB以上",
            "最新",
            "1Gbps",
            "WFGP4、WFIGP、WFAIGP、CMIGP、CMAIGP、SM9、ISM、AISM"
    );

    public static final List<BaseSpecification> ALL_SPECIFICATION = Arrays.asList(
            WIN10_SPECIFICATION,
            WIN11_SPECIFICATION
    );

    private static BaseSpecification createBaseSpecification(
            String osTitle, String cpu, String diskSpace, String memorySpace,
            String windowsUpdate, String networkBandwidth, String applicableVersions) {
        BaseSpecification spec = new BaseSpecification();
        spec.setOsTitle(osTitle);
        spec.setCpu(cpu);
        spec.setDiskSpace(diskSpace);
        spec.setMemorySpace(memorySpace);
        spec.setWindowsUpdate(windowsUpdate);
        spec.setNetworkBandwidth(networkBandwidth);
        spec.setApplicableVersions(applicableVersions);
        return spec;
    }
}
