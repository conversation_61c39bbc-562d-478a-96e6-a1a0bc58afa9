package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产类别表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@ApiModel(value = "AssetInstanceMapping对象")
public class AssetInstanceMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    private Long sid;

    private Long eid;

    private Long aiId;

    @ApiModelProperty("运维项目")
    private String aiopsItem;

    @ApiModelProperty("资产id")
    private Long assetId;

    private String modelCode;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public Long getAiId() {
        return aiId;
    }

    public void setAiId(Long aiId) {
        this.aiId = aiId;
    }

    public String getAiopsItem() {
        return aiopsItem;
    }

    public void setAiopsItem(String aiopsItem) {
        this.aiopsItem = aiopsItem;
    }

    public Long getAssetId() {
        return assetId;
    }

    public void setAssetId(Long assetId) {
        this.assetId = assetId;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AssetInstanceMapping{" +
            "id = " + id +
            ", sid = " + sid +
            ", eid = " + eid +
            ", aiId = " + aiId +
            ", aiopsItem = " + aiopsItem +
            ", assetId = " + assetId +
            ", modelCode = " + modelCode +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
