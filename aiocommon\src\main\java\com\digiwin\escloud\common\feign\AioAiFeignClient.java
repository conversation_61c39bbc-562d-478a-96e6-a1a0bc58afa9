package com.digiwin.escloud.common.feign;

import com.digiwin.escloud.aioai.model.ChatMessage;
import com.digiwin.escloud.aiochat.model.IssueDescRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(value = "aioai", url = "${feign.aioai.url:}")
public interface AioAiFeignClient {

    @PostMapping(value = "/issue/chat/getIssueDescForReport")
    ResponseBase getIssueDescForReport(@RequestHeader("eid") String eid,
                                       @RequestHeader("sid") String sid,
                                       @RequestHeader("token") String token,
                                       @RequestHeader("Accept-Language") String lang,@RequestBody IssueDescRequest request);

    @PostMapping(value = "/chat/invokeIndepthAIAgent/sync")
    ResponseBase invokeIndepthAIAgentSync(@RequestHeader("eid") String eid,
                                       @RequestHeader("sid") String sid,
                                       @RequestHeader("token") String token,
                                       @RequestHeader("Accept-Language") String lang,
                                       @RequestBody ChatMessage chatMessage);
}