package com.digiwin.escloud.aioitms.bigdata.joinway.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.model.Field;
import com.digiwin.escloud.aioitms.bigdata.joinway.JoinWayConverter;
import com.digiwin.escloud.aioitms.bigdata.model.Query;
import com.digiwin.escloud.common.feign.AioCmdbFeignClient;
import com.digiwin.escloud.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 基于fieldName的JoinWay转换器
 * 支持格式：{"fieldName": "eid"}
 * 
 * 转换逻辑：
 * 1. 根据fieldName查询cmdb_field表获取fieldCode
 * 2. 根据fieldCode查询相关配置信息（queryJson）
 * 3. 解析配置信息构建JoinConfig
 */
@Slf4j
@Component
public class FieldNameBasedJoinWayConverter implements JoinWayConverter {

    @Autowired
    private AioCmdbFeignClient aioCmdbFeignClient;
    
    @Override
    public boolean supports(Query.JoinWay joinWay) {
        // 支持只有fieldName字段的joinWay
        return joinWay != null 
                && StringUtils.hasText(joinWay.getFieldName());
    }
    
    @Override
    public List<Query.JoinConfig> convert(Query.JoinWay joinWay) {
        log.info("Converting fieldName-based joinWay: {}", joinWay);

        String fieldName = joinWay.getFieldName();

        // 提取函数包装字段的原始字段名，用于查询cmdb_field表
        String extractedFieldName = extractFieldNameFromFunction(fieldName);

        // 1. 根据提取的fieldName查询cmdb_field表获取fieldCode
        Field field = queryFieldCodeByFieldName(extractedFieldName);
        if (Objects.isNull(field)) {
            throw new RuntimeException("未找到fieldName对应的fieldCode: " + extractedFieldName + " (原始输入: " + fieldName + ")");
        }

        // 2. 根据fieldCode查询配置信息
        JoinConfigInfo configInfo = queryJoinConfigInfo(field);
        if (configInfo == null) {
            throw new RuntimeException("未找到fieldCode对应的配置信息: " + field.getFieldCode());
        }

        // 3. 构建JoinConfig，使用原始的fieldName（包含函数包装）作为mainFieldName
        Query.JoinConfig joinConfig = buildJoinConfig(configInfo, fieldName);

        List<Query.JoinConfig> result = new ArrayList<>();
        result.add(joinConfig);
        return result;
    }

    /**
     * 从函数包装的字段代码中提取原始字段名
     * 例如：lower(test_manufacturer) -> test_manufacturer
     */
    private String extractFieldNameFromFunction(String fieldCode) {
        if (!StringUtils.hasText(fieldCode)) {
            return fieldCode;
        }

        // 匹配常见的SQL函数模式：function_name(field_name)
        String pattern = "^\\w+\\(([^)]+)\\)$";
        if (fieldCode.matches(pattern)) {
            return fieldCode.replaceAll(pattern, "$1").trim();
        }

        // 如果不匹配函数模式，返回原始字段代码
        return fieldCode;
    }
    
    @Override
    public int getOrder() {
        return 1; // 优先级较高
    }
    
    /**
     * 根据fieldName查询cmdb_field表获取Field信息
     */
    private Field queryFieldCodeByFieldName(String fieldName) {
        log.debug("Querying field info for fieldName: {}", fieldName);

        try {
            // 注意：这里假设fieldName就是fieldCode，如果不是，需要调用其他接口
            // 或者需要AioCmdbFeignClient提供根据fieldName查询的接口
            BaseResponse<List<Field>> response =
                    aioCmdbFeignClient.getFieldListByFieldCodeList(Stream.of(fieldName).collect(Collectors.toList()));

            if (!response.checkIsSuccess()) {
                log.error("Failed to query field by fieldName {}: {}", fieldName, response.getErrMsg());
                return null;
            }

            List<Field> fields = response.getData();
            if (CollectionUtils.isEmpty(fields)) {
                log.warn("No field found for fieldName: {}", fieldName);
                return null;
            }

            Field field = fields.get(0);
            log.debug("Found field: fieldCode={}, fieldName={}, queryJson={}",
                    field.getFieldCode(), field.getFieldName(), field.getFieldTypeSettingJson());

            return field;
        } catch (Exception e) {
            log.error("Error querying field by fieldName: {}", fieldName, e);
            throw new RuntimeException("查询字段信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据Field查询JOIN配置信息
     * 解析Field中的queryJson字段
     */
    private JoinConfigInfo queryJoinConfigInfo(Field field) {
        log.debug("Querying join config info for fieldCode: {}", field.getFieldCode());

        String queryJson = field.getFieldTypeSettingJson();
        if (!StringUtils.hasText(queryJson)) {
            log.warn("Field {} has no queryJson configuration", field.getFieldCode());
            return null;
        }

        try {
            // 解析queryJson字符串
            JSONObject queryJsonObj = JSON.parseObject(queryJson);

            // 提取JOIN配置信息
            return parseQueryJsonToJoinConfigInfo(queryJsonObj);
        } catch (Exception e) {
            log.error("Failed to parse queryJson for field {}: {}", field.getFieldCode(), queryJson, e);
            throw new RuntimeException("解析queryJson失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析queryJson为JoinConfigInfo
     */
    private JoinConfigInfo parseQueryJsonToJoinConfigInfo(JSONObject queryJsonObj) {
        JoinConfigInfo configInfo = new JoinConfigInfo();

        // 从queryJson中提取tableName作为sinkName
        String tableName = queryJsonObj.getString("tableName");
        if (!StringUtils.hasText(tableName)) {
            throw new RuntimeException("queryJson中缺少tableName字段");
        }
        configInfo.setSinkName(tableName);

        // 从queryJson中提取keyColumn的fieldName作为JOIN字段
        JSONObject keyColumn = queryJsonObj.getJSONObject("keyColumn");
        if (keyColumn == null) {
            throw new RuntimeException("queryJson中缺少keyColumn字段");
        }

        String keyColumnFieldName = keyColumn.getString("fieldName");
        if (!StringUtils.hasText(keyColumnFieldName)) {
            throw new RuntimeException("queryJson中keyColumn缺少fieldName字段");
        }
        configInfo.setKeyColumnFieldName(keyColumnFieldName);

        // 从queryJson中提取showColumns
        List<Query.ColumnConfig> showColumns = new ArrayList<>();
        if (queryJsonObj.containsKey("showColumns")) {
            List<JSONObject> showColumnsList = queryJsonObj.getJSONArray("showColumns").toJavaList(JSONObject.class);
            for (JSONObject showColumnObj : showColumnsList) {
                String fieldName = showColumnObj.getString("fieldName");
                String fieldPath = showColumnObj.getString("fieldPath");
                if (StringUtils.hasText(fieldName)) {
                    showColumns.add(new Query.ColumnConfig(fieldName, fieldPath));
                }
            }
        }
        configInfo.setShowColumns(showColumns);

        log.debug("Parsed JOIN config: sinkName={}, keyColumnFieldName={}, showColumns={}",
                configInfo.getSinkName(), configInfo.getKeyColumnFieldName(), showColumns.size());

        return configInfo;
    }






    
    /**
     * 构建JoinConfig
     */
    private Query.JoinConfig buildJoinConfig(JoinConfigInfo configInfo, String mainFieldCode) {
        Query.JoinConfig joinConfig = new Query.JoinConfig();
        
        // 设置基本信息
        joinConfig.setSinkName(configInfo.getSinkName());
        joinConfig.setJoinType("LEFT"); // 写死为LEFT
        joinConfig.setShowColumns(configInfo.getShowColumns());
        
        // 设置JOIN条件
        Query.JoinOn joinOn = new Query.JoinOn();
        joinOn.setFieldName(configInfo.getKeyColumnFieldName()); // JOIN表的字段
        joinOn.setMainFieldName(mainFieldCode); // 主表的字段
        joinConfig.setJoinOn(joinOn);
        
        return joinConfig;
    }
    
    /**
     * JOIN配置信息内部类
     */
    private static class JoinConfigInfo {
        private String sinkName;
        private String keyColumnFieldName;
        private List<Query.ColumnConfig> showColumns;
        
        // getters and setters
        public String getSinkName() { return sinkName; }
        public void setSinkName(String sinkName) { this.sinkName = sinkName; }
        
        public String getKeyColumnFieldName() { return keyColumnFieldName; }
        public void setKeyColumnFieldName(String keyColumnFieldName) { this.keyColumnFieldName = keyColumnFieldName; }
        
        public List<Query.ColumnConfig> getShowColumns() { return showColumns; }
        public void setShowColumns(List<Query.ColumnConfig> showColumns) { this.showColumns = showColumns; }
    }
}
