package com.digiwin.escloud.aioitms.temprh.web.dto;

import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.temprh.model.TempRhAuthDetail;
import com.digiwin.escloud.aioitms.temprh.utils.BigDataMappingKey;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryTempRhAuthDetail extends TempRhAuthDetail implements BigDataMappingKey {

    @ApiModelProperty("客户简称")
    private String customer_short_name;

    @ApiModelProperty("合约截止日期")
    private String contract_end_date;

    @ApiModelProperty("最后记录时间")
    private String last_device_collect_time;

    @ApiModelProperty("最后同步时间")
    private String last_device_sync_time;

    @ApiModelProperty("最后采集温度")
    private String degrees;

    @ApiModelProperty("最后采集湿度")
    private String wetness;

    @ApiModelProperty("設備類型名稱_CN")
    private String deviceTypeName_CN;

    @ApiModelProperty("設備類型名稱_TW")
    private String deviceTypeName_TW;

    @ApiModelProperty("溫濕度主機平台名稱")
    private String tmp_rh_host_name;                   // th_host_info.id

    @ApiModelProperty("虛擬設備 aiops_deive.ID")
    private String adId;

    @ApiModelProperty("虛擬設備 aiops_deive.deviceId")
    private String deviceId;

    @ApiModelProperty("設備放置點")
    private String device_location;      // '設備放置點',
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("客户注记")
    private String customerNotes;
    @ApiModelProperty("温度閥值")
    private String degreesThreshold; // 溫度閥值
    @ApiModelProperty("濕度閥值")
    private String wetnessThreshold; // 濕度閥值

    private Long aiId;

    private AssetAiopsInstance assetAiopsInstance;

    @Override
    @JsonIgnore
    public String getKey() {
        // 己這邊來說, device id 只是要去串預警, 才需用, 不然可以直接用 溫濕度的設備號, 來做 mapping.
        return this.getEid() + this.getDevice_serial();
    }
}
