package com.digiwin.escloud.aiocmdb.field.dao;

import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSetMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface FieldMapper {
    List<Field> getFieldList(Map<String, Object> map);

    List<Field> getSystemFieldList(Map<String, Object> map);

    Field findFieldCode(@Param(value = "fieldCode") String fieldCode);

    List<Field> findFieldCodeList(@Param(value = "fieldCodeList") List<String> fieldCodeList);

    boolean addField(Field field);

    int batchAddField(List<Field> list);

    boolean deleteFieldTypeEnum(@Param(value = "fieldCode") String fieldCode);

    boolean addFieldTypeEnumList(Map<String, Object> map);

    boolean modifyField(Field field);

    boolean deleteField(@Param(value = "id") long id);

    boolean deleteFieldByFieldCode(@Param(value = "fieldCode") String fieldCode);

    int sortField(List<ModelFieldMapping> modelFieldMappings);

    int sortFieldSetField(List<FieldSetMapping> fieldSetMappings);

    int saveFieldFormula(ModelFieldFomula modelFieldFomula);

    List<String> getModelCodesByField(String fieldCode);

    int deleteFieldByFieldCodes(Map<String, Object> map);

    List<Field> getFieldByCodes(@Param(value = "fieldCodes") List<String> fieldCodes);

    Integer fieldTypeEnumSort(@Param(value = "fieldTypeEnumList") List<FieldTypeEnum> fieldTypeEnumList);

    Integer formatField(@Param(value = "modelFieldMappingList") List<ModelFieldMapping> modelFieldMappingList);

    List<FieldTypeEnum> getFieldTypeEnumByFieldCodes(@Param(value = "fieldCodes") List<String> fieldCodes);
}

