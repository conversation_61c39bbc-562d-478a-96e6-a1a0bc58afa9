package com.digiwin.escloud.aiocmdb.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 资产类别查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel(value = "AssetCategoryQueryParam对象", description = "资产类别查询参数")
public class AssetCategoryQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    private Long sid;

    private String scopeId;

    @ApiModelProperty("分类ID")
    private Long classificationId;

    private String categoryType;
    @ApiModelProperty("类别编号(取模型编号)")
    private String categoryNumber;

    @ApiModelProperty("类别名称")
    private String categoryName;

    @ApiModelProperty("图标URL")
    private String iconUrl;

    private String modelCode;

    private String sinkName;

    @ApiModelProperty("状态")
    private AssetCategory.Status status;

    @ApiModelProperty("建立模式")
    private AssetCategory.CreationMode creationMode;

    private String aiopsItem;

    // 分页参数
    @ApiModelProperty("页码，从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty("是否需要分页，true-分页查询，false-不分页查询")
    private Boolean needPaging = true;

    private Long eid;
}
