package com.digiwin.escloud.aiocmdb.assetmaintenancev2.model;

import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class AssetAttribute {
    private Long eid = 0L;
    private Long hbaseRowId = 0L;
    private String hbaseModelCode;
    private String hbaseTargetValue;
//    private String srJson;
    private SrData srData;

    @Data
    public static class SrData {
        private String dbName; //": "servicecloud",
        private String tableName; //": "NetworkSecurityExamInfoSystem",
        private String sinkType; //": "starrocks"
        private String schemaName; //": "default",
        private List<Object> data;
    }
}
