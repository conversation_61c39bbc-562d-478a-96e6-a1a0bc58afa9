package com.digiwin.escloud.aioitms.instance.model;

import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("SNMP信息")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SNMPInfo extends AiopsSNMPInstance {

    @ApiModelProperty("运维项目群组")
    private String aiopsItemGroup;

    @ApiModelProperty("代理运维设备列表")
    private List<AiopsKitDevice> aiopsKitDeviceList;

    private AssetAiopsInstance assetAiopsInstance;
}
