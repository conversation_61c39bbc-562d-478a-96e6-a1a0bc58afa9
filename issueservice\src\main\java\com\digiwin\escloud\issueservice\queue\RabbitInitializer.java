package com.digiwin.escloud.issueservice.queue;

import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGMQConstant;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.requirement.RequirementFGMQConstant;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayMQConstant;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.requirement.RequirementWorkDayMQConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RabbitInitializer implements InitializingBean {

    @Autowired
    private AmqpAdmin rabbitAdmin1;

    /**
     * 初始化交换机，队列，绑定关系
     */
    @Override
    public void afterPropertiesSet() {
//        init();
    }

    public void init(){
        DirectExchange FGDirectExchange = declareDirectExchange(IssueFGMQConstant.exchange);
        rabbitAdmin1.declareExchange(FGDirectExchange);
        Queue FGQueue = declareQueue(IssueFGMQConstant.queue,false);
        rabbitAdmin1.declareQueue(FGQueue);
        Binding FGBinding = declareDirectBinding(FGQueue, FGDirectExchange, IssueFGMQConstant.routingkey);
        rabbitAdmin1.declareBinding(FGBinding);

        DirectExchange RequirementFGDirectExchange = declareDirectExchange(RequirementFGMQConstant.exchange);
        rabbitAdmin1.declareExchange(RequirementFGDirectExchange);
        Queue RequirementFGQueue = declareQueue(RequirementFGMQConstant.queue,false);
        rabbitAdmin1.declareQueue(RequirementFGQueue);
        Binding RequirementFGBinding = declareDirectBinding(RequirementFGQueue, RequirementFGDirectExchange, RequirementFGMQConstant.routingkey);
        rabbitAdmin1.declareBinding(RequirementFGBinding);

        DirectExchange directExchange = declareDirectExchange(IssueWorkDayMQConstant.exchange);
        rabbitAdmin1.declareExchange(directExchange);
        Queue queue = declareQueue(IssueWorkDayMQConstant.queue,false);
        rabbitAdmin1.declareQueue(queue);
        Binding binding = declareDirectBinding(queue, directExchange, IssueWorkDayMQConstant.routingkey);
        rabbitAdmin1.declareBinding(binding);

        DirectExchange reportExchange = declareDirectExchange(RequirementWorkDayMQConstant.exchange);
        rabbitAdmin1.declareExchange(reportExchange);
        Queue serviceReportQueue = declareQueue(RequirementWorkDayMQConstant.queue,false);
        rabbitAdmin1.declareQueue(serviceReportQueue);
        Binding serviceReportBinding = declareDirectBinding(serviceReportQueue, reportExchange, RequirementWorkDayMQConstant.routingkey);
        rabbitAdmin1.declareBinding(serviceReportBinding);
    }

    public Queue declareQueue(String queueName,boolean durable){
        return new Queue(queueName,durable,false,false,null);
    }

    public DirectExchange declareDirectExchange(String exchangeName){
        return new DirectExchange(exchangeName,true,false,null);
    }

    public Binding declareDirectBinding(Queue queue, DirectExchange exchange,String routingKey){
        return BindingBuilder.bind(queue).to(exchange).with(routingKey);
    }
}