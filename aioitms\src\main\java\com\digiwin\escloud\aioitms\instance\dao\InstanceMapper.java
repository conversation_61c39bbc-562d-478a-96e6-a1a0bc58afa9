package com.digiwin.escloud.aioitms.instance.dao;

import com.digiwin.escloud.aiobasic.edr.model.edr.EdrDevice;
import com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig;
import com.digiwin.escloud.aioitms.device.model.*;
import com.digiwin.escloud.aioitms.layer.model.AiopsModuleCollectMapping;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.instance.model.*;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.instance.AiopsEDRInstance;
import com.digiwin.escloud.aioitms.instance.model.AiopsEaiCloudInstanceDetail;
import com.digiwin.escloud.aioitms.instance.model.AiopsEaiCloudInstance;
import com.digiwin.escloud.aioitms.model.instance.AiopsSmartMeterInstance;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface InstanceMapper {

    /**
     * 依据运维项目代号列表查询运维项目列表
     *
     * @param codeList 运维项目代号列表
     * @return 运维项目列表
     */
    List<AiopsItem> selectAiopsItemByCodeList(@Param("codeList") Collection<String> codeList);

    /**
     * 依据运维项目上下文列表批量更新运维实例运维商运维模组类别明细Id及运维项目Id
     *
     * @param aicList 运维项目上下文列表
     * @return 影响笔数
     */
    Integer batchUpdateAiopsInstanceSamcdIdAndAiopsItemIdByAicList(
            @Param("aicList") Collection<AiopsItemContext> aicList);

    /**
     * 依据运维实例Id列表删除运维实例
     *
     * @param aiIdList 运维实例Id列表
     * @return 影响笔数
     */
    Integer deleteAiopsInstanceByAiIdList(@Param("aiIdList") List<Long> aiIdList);

    /**
     * 依据运维项目上下文查询已授权的运维实例是否存在
     *
     * @param aiopsAuthStatus 运维授权状态
     * @param aicList         运维项目上下文列表
     * @return 运维实例列表
     */
    List<AiopsInstance> selectAiopsInstanceExistByAicList(
            @Param("aiopsAuthStatus") AiopsAuthStatus aiopsAuthStatus,
            @Param("aicList") Collection<AiopsItemContext> aicList);

    /**
     * 查询运维SNMP实例代理设备列表
     *
     * @param map 条件字典
     * @return SNMP信息列表
     */
    List<SNMPInfo> selectAiopsSnmpInstanceAgentDevice(Map<String, Object> map);

    /**
     * 批量新增更新运维实例
     *
     * @param aiCollection 运维实例列表
     * @return 影响笔数
     */
    Integer batchInsertOrUpdateAiopsInstance(@Param("aiCollection") Collection<AiopsInstance> aiCollection);

    /**
     * 批量新增运维实例
     *
     * @param aiCollection 运维实例列表
     * @return 影响笔数
     */
    Integer batchInsertAiopsInstance(@Param("aiCollection") Collection<AiopsInstance> aiCollection);

    /**
     * 批量更新运维实例
     *
     * @param aiCollection 运维实例列表
     * @return 影响笔数
     */
    Integer batchUpdateAiopsInstance(@Param("aiCollection") List<AiopsInstance> aiCollection);

    /**
     * 批量更新运维实例执行参数模型代号与内容
     *
     * @param aiCollection 运维实例列表
     * @return 影响笔数
     */
    Integer batchUpdateAiopsInstanceOriExecParamsContent(
            @Param("aiCollection") Collection<AiopsInstance> aiCollection);

    /**
     * 依据租户模组合约明细Id查询已授权的实例数量
     *
     * @param tmcdId            租户模组合约明细Id
     * @param isContainHoldAuth 是否包含占用授权标的
     * @return 已授权的实例数量
     */
    Integer selectAuthedCountByTmcdId(@Param("tmcdId") Long tmcdId,
                                      @Param("isContainHoldAuth") boolean isContainHoldAuth);

    /**
     * 依据运维项目查询租户模组合约明细Id已授权的实例数量字典
     *
     * @param eid           租户Id
     * @param aiopsItemList 运维项目列表
     * @return 租户模组合约明细Id与已授权的实例数量字典列表
     */
    List<Map<String, Object>> selectAuthedCountByAiopsItemList(@Param("eid") Long eid,
                                                               @Param("aiopsItemList") List<String> aiopsItemList);

    /**
     * 依据租户Id查询SNMP实例列表
     *
     * @param eid 租户Id
     * @return SNMP实例列表
     */
    List<AiopsItemStatistics> selectStatisticsValidAiopsItemListByEid(@Param("eid") Long eid);

    /**
     * 查询批量授权列表
     *
     * @param map 条件字典
     * @return 批量授权列表
     */
    List<List<Map<String, Object>>> selectBatchAuthList(Map<String, Object> map);

    /**
     * 依据SNMPId查询SNMP实例
     *
     * @param snmpId SNMP的Id
     * @return SNMP实例
     */
    AiopsSNMPInstance selectAiopsSNMPInstanceBySNMPId(@Param("snmpId") String snmpId);

    /**
     * 依据SNMP实例Id查询SNMPId
     *
     * @param asiId SNMP实例Id
     * @return SNMPId
     */
    String selectSNMPIdByAsiId(@Param("asiId") Long asiId);

    /**
     * 依据租户Id查询SNMP实例列表
     *
     * @param eid      租户Id
     * @param snmpType snmp设备类型
     * @return SNMP实例列表
     */
    List<AiopsSNMPInstance> selectSNMPInstanceListByEid(@Param("eid") Long eid,
                                                        @Param("snmpType") String snmpType);

    /**
     * 批量新增或更新SNMP实例
     *
     * @param aiopsSNMPInstanceList SNMP实例列表
     * @return 影响笔数
     */
    Integer batchInsertOrUpdateAiopsSNMPInstance(
            @Param("aiopsSNMPInstanceList") List<AiopsSNMPInstance> aiopsSNMPInstanceList);

    /**
     * 查询已经存在的SNMP实例
     *
     * @param aiopsSNMPInstanceList snmp实例列表
     * @return SNMP实例列表
     */
    List<AiopsSNMPInstance> selectExistSNMPInstances(
            @Param("aiopsSNMPInstanceList") List<AiopsSNMPInstance> aiopsSNMPInstanceList);

    /**
     * 依据租户Id查询Http实例列表
     *
     * @param eid            租户Id
     * @param apiCollectType api 收集类型
     * @return
     */
    List<AiopsHttpInstance> selectHttpInstanceListByEid(@Param("eid") Long eid,
                                                        @Param("apiCollectType") String apiCollectType);

    /**
     * 查询Http实例列表
     *
     * @param map 條件字典
     * @return AiopsHttpInstance信息列表
     */
    List<AiopsHttpInstance> selectHttpInstanceList(Map<String, Object> map);

    /**
     * 依据Http实例Id查询 apiCollectId
     *
     * @param ahiId Http实例Id
     * @return apiCollectId
     */
    String selectApiCollectIdByAhiId(@Param("ahiId") Long ahiId);

    /**
     * 依据apiCollectId查询Http实例
     *
     * @param apiCollectId apiCollectId
     * @return Http实例
     */
    AiopsHttpInstance selectAiopsHTTPInstanceByApiCollectId(@Param("apiCollectId") String apiCollectId);

    /**
     * 批量新增或更新Http实例
     *
     * @param aiopsHttpInstanceList Http实例列表
     * @return 影响笔数
     */
    Integer batchInsertOrUpdateAiopsHttpInstance(
            @Param("aiopsHttpInstanceList") List<AiopsHttpInstance> aiopsHttpInstanceList);

    /**
     * 查询已经存在的Http实例
     *
     * @param aiopsHttpInstanceList Http实例列表
     * @return Http实例列表
     */
    List<AiopsHttpInstance> selectExistHttpInstances(
            @Param("aiopsHttpInstanceList") List<AiopsHttpInstance> aiopsHttpInstanceList);

    /**
     * 依据运维项目Id列表查询运维实例
     *
     * @param aiopsItemIdList 运维项目Id列表
     * @return 运维实例列表
     */
    List<AiopsInstance> selectAiopsInstanceByAiopsItemIdList(
            @Param("aiopsItemIdList") Collection<String> aiopsItemIdList);

    /**
     * 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
     * 批量新增或更新 AppAutoUpdate 实例
     *
     * @param aiopsAppAutoUpdateInstanceList AppAutoUpdate 实例列表
     * @return 影响笔数
     */
    Integer batchInsertOrUpdateAiopsAppAutoUpdateInstance(
            @Param("aiopsAppAutoUpdateInstanceList") List<AiopsAppAutoUpdateInstance> aiopsAppAutoUpdateInstanceList);

    /**
     * 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
     * 查询已经存在的 AppAutoUpdate 实例
     *
     * @param aiopsAppAutoUpdateInstanceList snmp实例列表
     * @return AppAutoUpdate 实例列表
     */
    List<AiopsAppAutoUpdateInstance> selectExistAppAutoUpdateInstances(
            @Param("aiopsAppAutoUpdateInstanceList") List<AiopsAppAutoUpdateInstance> aiopsAppAutoUpdateInstanceList);

    /**
     * 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
     * 依据 aiopsItemId 查询 AppAutoUpdate 实例
     *
     * @param aiopsItemId aiopsItemId
     * @return AppAutoUpdate 实例
     */
    AiopsAppAutoUpdateInstance selectAiopsAppAutoUpdateInstanceByAiPsItemId(@Param("aiopsItemId") String aiopsItemId);

    /**
     * 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
     * 依据 AppAutoUpdate 实例Id查询 aiopsItemId
     *
     * @param asiId AppAutoUpdate 实例Id
     * @return aioPsItemId
     */
    String selectAppAutoUpdateAioPsItemIdByAsiId(@Param("asiId") Long asiId);

    /**
     * 依据运维项目Id查询运维实例Id
     *
     * @param aiopsItemId 运维项目Id
     * @return 运维实例Id
     */
    Long selectAiIdByAiopsItemId(@Param("aiopsItemId") String aiopsItemId);

    /**
     * 依据运维实例Id查询运维项目Id
     *
     * @param aiId 运维实例Id
     * @return 运维项目Id
     */
    String selectAiopsItemIdByAiId(@Param("aiId") Long aiId);

    /**
     * 授权到期更新运维实例授权状态
     *
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 影响笔数
     */
    Integer updateAiopsInstanceAuthStatusByContractExpire(
            @Param("tmcdIdList") List<Long> tmcdIdList);

    /**
     * 依据租户模组合约明细Id列表查询租户Id列表
     *
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 租户Id列表
     */
    List<Long> selectAiEidListByTmcdIdList(@Param("tmcdIdList") List<Long> tmcdIdList);

    /**
     * 依据运维项目Id列表查询授权状态字典列表
     *
     * @param aiopsItemIdList 运维项目Id列表
     * @return 授权状态字典列表
     */
    List<Map<String, String>> selectAuthStatusMapByAiopsItemIdList(
            @Param("aiopsItemIdList") Collection<String> aiopsItemIdList);

    /**
     * 依据设备Id查询SNMP信息列表
     *
     * @param deviceId 设备Id
     * @return SNMP信息列表
     */
    List<SNMPInfo> selectSNMPInstanceListByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 依据租户模组合约明细Id列表查询实例已授权数量字典
     *
     * @param eid               租户Id
     * @param isContainHoldAuth 是否包含占用授权标的
     * @param tmcdIdList        租户模组合约明细Id列表
     * @return 实例已授权数量字典
     */
    List<Map<String, Object>> selectAuthedCountMapByTmcdIdList(
            @Param("eid") Long eid,
            @Param("isContainHoldAuth") boolean isContainHoldAuth,
            @Param("tmcdIdList") List<Long> tmcdIdList);

    /**
     * 依据运维项目上下文列表查询设备Id列表
     *
     * @param aicList 运维项目上下文列表
     * @return 设备Id列表
     */
    List<String> selectDeviceIdListByAicList(@Param("aicList") List<AiopsItemContext> aicList);

    /**
     * 作废设备实例授权
     *
     * @param deviceId 设备Id
     * @return 影响笔数
     */
    int invalidDeviceAiopsInstanceAuthStatus(@Param("deviceId") String deviceId);

    /**
     * 依据运维实例Id列表查询运维实例
     *
     * @param aiIdList 运维实例Id列表
     * @return 运维实例列表
     */
    List<AiopsInstance> selectAiopsInstanceByAiIdList(@Param("aiIdList") List<Long> aiIdList);

    /**
     * 依据SNMPId查询SNMP实例
     *
     * @param snmpId SNMPId
     * @return SNMP实例
     */
    AiopsSNMPInstance selectSNMPInstanceBySNMPId(@Param("snmpId") String snmpId);

    /**
     * 依据备份软件Id查询备份软件实例
     *
     * @param backupSoftwareId 备份软件Id
     * @return 备份软件实例
     */
    AiopsBackupSoftwareInstance selectAiopsBackupSoftwareInstanceByBackupSoftwareId(
            @Param("backupSoftwareId") String backupSoftwareId);

    /**
     * 依据备份软件Id查询备份软件实例
     *
     * @param absiId 备份软件主键Id
     * @return 备份软件Id
     */
    String selectAiopsBackupSoftwareInstanceByAbsiId(@Param("absiId") Long absiId);

    /**
     * 批量新增或更新运维备份软件实例
     *
     * @param aiopsBackupSoftwareInstanceListInstanceList 运维备份软件实例列表
     * @return 影响笔数
     */
    Integer batchInsertOrUpdateAiopsBackupSoftwareInstance(
            @Param("aiopsBackupSoftwareInstanceList") List<AiopsBackupSoftwareInstance> aiopsBackupSoftwareInstanceListInstanceList);

    /**
     * 依据条件字典获取运维实例列表
     *
     * @param map 条件字典
     * @return 运维实例列表
     */
    List<AiopsInstance> selectAiopsInstanceByMap(Map<String, Object> map);

    List<AiopsInstance> getInstances(Map<String, Object> map);


    List<AiopsInstance> getInstanceByInstanceIds(@Param("instanceIds") List<String> instanceIds,
                                                 @Param("aiopsItem") String aiopsItem);

    List<AiopsItem> getAiopsItemByTenantSid(@Param("sid") long sid,
                                            @Param("tenantSid") long tenantSid,
                                            @Param("showInApp") Boolean showInApp);

    List<AiopsItem> getAiopsItem();

    /**
     * 依据条件字典查询添加设备收集项明细信息
     *
     * @param map 条件字典
     * @return 添加设备收集项明细信息
     */
    List<Map<String, Object>> selectAddAdcdInfo(Map<String, Object> map);

    /**
     * 依据关系查询运维实例项目Id
     *
     * @param truestInstanceTableName 真实实例表名称
     * @param businessPKValueMap      业务主键值字典
     * @param aiopsItemIdColumnName   运维项目Id栏位名称
     * @return 运维实例项目Id
     */
    String selectAiopsItemIdByRelate(@Param("truestInstanceTableName") String truestInstanceTableName,
                                     @Param("businessPKValueMap") Map<String, Object> businessPKValueMap,
                                     @Param("aiopsItemIdColumnName") String aiopsItemIdColumnName);

    /**
     * 依据条件字典查询运维实例项目Id列表
     *
     * @param map 条件字典
     * @return 运维实例项目Id列表
     */
    List<String> selectAiopsItemIdListByMap(Map<String, Object> map);

    /**
     * 查询已经存在的EDR实例列表
     *
     * @param edrIdList EDRId列表
     * @return EDR实例列表
     */
    List<AiopsEDRInstance> selectExistEDRInstances(@Param("edrIdList") Collection<String> edrIdList);

    /**
     * 批量新增运维EDR实例
     *
     * @param aeiList 运维EDR实例列表
     * @return 影响笔数
     */
    Integer batchInsertAiopsEDRInstance(@Param("aeiList") Collection<AiopsEDRInstance> aeiList);

    /**
     * 查询已经存在的智能电表实例列表
     *
     * @param smartMeterIdList 智能电表Id列表
     * @return 智能电表实例列表
     */
    List<AiopsSmartMeterInstance> selectExistSmartMeterInstances(
            @Param("smartMeterIdList") Collection<String> smartMeterIdList);

    /**
     * 批量更新智能电表实例
     * @param asmiList 智能电表实例列表
     * @return 影响笔数
     */
    Integer batchUpdateAiopsSmartMeterInstance(@Param("asmiList") List<AiopsSmartMeterInstance> asmiList);

    /**
     * 批量新增运维智能电表实例
     *
     * @param asmiList 运维智能电表实例列表
     * @return 影响笔数
     */
    Integer batchInsertAiopsSmartMeterInstance(@Param("asmiList") Collection<AiopsSmartMeterInstance> asmiList);

    /**
     * 查询已经存在的云备援实例列表
     *
     * @param eaiCloudIdList eaiCloudId列表
     * @return 云备援实例列表
     */
    List<AiopsEaiCloudInstance> selectExistEaiCloudInstance(@Param("eaiCloudIdList") Collection<String> eaiCloudIdList);

    /**
     * 批量新增云备援实例
     *
     * @param aeciList 云备援实例列表
     * @return 影响笔数
     */
    Integer batchInsertAiopsEaiCloudInstance(@Param("aeciList") Collection<AiopsEaiCloudInstance> aeciList);

    /**
     * 更新云备援预警状态
     *
     * @param isWarningEnable   预警状态
     * @param serviceCode 客代
     * @param eaiType  云备援类型
     * @return 影响笔数
     */
    Integer updateAiopsEaiCloudInstance(@Param("isWarningEnable") Boolean isWarningEnable,
                                        @Param("serviceCode") String serviceCode,
                                        @Param("eaiType") String eaiType,
                                        @Param("aiopsItemType") String aiopsItemType);

    /**
     * 依据客代Id查询云备援预警状态
     *
     * @param serviceCode 云备援客代Id
     * @return 字典对象(云备援预警状态、运维实例Id)
     */
    Map<String, Object> selectAiopsEaiCloudIsWarningEnable(@Param("serviceCode") String serviceCode,
                                                           @Param("aiopsItemType") String aiopsItemType);

    /**
     * 依据客代Id查询云备援收集项详情信息
     *
     * @param serviceCode 云备援客代Id
     * @param uploadDataModelCode 模型名称
     * @param eaiType 类型
     * @return 云备援实例详情
     */
    List<AiopsEaiCloudInstanceDetail> selectAiopsEaiCloudInstanceDetail(
            @Param("serviceCode") String serviceCode,
            @Param("uploadDataModelCode") String uploadDataModelCode,
            @Param("eaiType") String eaiType,
            @Param("aiopsItemType") String aiopsItemType);

    /**
     * 查询云备援设备收集项预警Id列表
     *
     * @param serviceCode 客代
     * @param eaiType  云备援类型
     * @return 设备收集项预警Id列表
     */
    List<Long> selectAiopsEaiCloudAdcwIdList(@Param("serviceCode") String serviceCode,
                                             @Param("eaiType") String eaiType,
                                             @Param("aiopsItemType") String aiopsItemType);

    /**
     * 移除云备援设备收集项预警Id列表
     * @param serviceCode 客代
     * @param eaiType  云备援类型
     * @return 影響筆數
     */
    Integer removeAiopsEaiCloudInstanceDetail(@Param("serviceCode") String serviceCode,
                                             @Param("eaiType") String eaiType);

    /**
     * 取得模組的收集项及預警項詳情
     * @param eaiType  云备援类型
     * @return 影響筆數
     */
    List<AiopsModuleCollect> getModuleCollectDetail(@Param("eaiType") String eaiType);

    /**
     * 取得實例的收集项及預警項詳情
     * @param eaiType  云备援类型
     * @return 影響筆數
     */
    List<AiopsInstanceCollectMapping> getInstanceCollectDetail(@Param("serviceCode") String serviceCode,
                                                               @Param("eaiType") String eaiType);

    /**
     * 添加無設備實例的收集项
     * @param mapList 參數 id, accId, collectName, aiId
     * @return 影響筆數
     */
    Integer batchAddNewInstanceCollectDetail(@Param("mapList") List<Map<String, Object> > mapList);

    /**
     * 添加無設備實例的預警項
     * @param mapList 參數 id, accId, collectName, aiId
     * @return 影響筆數
     */
    Integer batchAddNewInstanceWarningDetail(@Param("mapList") List<Map<String, Object> > mapList);

    /**
     * 依据条件字典查询上传大数据信息
     *
     * @param map 条件字典
     * @return 上传大数据数据上下文
     */
    List<UploadBigDataContext> selectUploadBigDataInfoByMap(Map<String, Object> map);

    /**
     * 依据条件字典查询真实表明细
     * @param map 条件字典
     * @return 真实表明细
     */
    List<Map<String, Object>> selectTrustDetailByMap(Map<String, Object> map);

    /**
     * 查询报表设备应用实例信息
     * @param map 条件字典
     * @return 设备应用实例信息列表
     */
    List<Map<String, Object>> selectReportDeviceAppInstanceInfo(Map<String, Object> map);

    Integer selectDatabaseInstanceCount(Map<String, Object> map);

    /**
     * 查询报表数据库实例信息
     * @param map 条件字典
     * @return 数据库实例信息列表
     */
    List<Map<String, Object>> selectReportDbInstanceInfo(Map<String, Object> map);

    List<String> selectCategoryNamesByAiopsItems(List<String> aiopsItems);

    /**
     * 依据条件字典查询合并统计明细列表
     * @param map 条件字典
     * @return 合并统计明细列表
     */
    List<MergeStatisticsDetailInfo> selectMergeStatisticsDetailByMap(Map<String, Object> map);

    /**
     * 根据条件字典查询运维实例真实表通用信息
     * @param map 条件字典
     * @return 运维实例真实表通用信息
     */
    List<Map<String, Object>> selectAiTrustCommonInfoByMap(Map<String, Object> map);

    /**
     * 依据条件字典查询运维项目列表
     * @param map 条件字典
     * @return 运维项目列表
     */
    Set<String> selectAiopsItemListByMap(Map<String, Object> map);

    /**
     * 依据条件字典列表更新实例表租户模组明细Id
     * @param mapList 条件字典列表
     * @return 影响笔数
     */
    Integer updateAiopsInstanceTmcdIdByMapList(@Param("mapList") List<Map<String, Object>> mapList);

    /**
     * 依据条件字典查询运维实例关系租户Sid列表
     * @param map 条件字典
     * @return 租户Sid列表
     */
    List<Map<String, Object>> selectAiRelateEidList(Map<String, Object> map);

    /**
     * 依据运维项目Id列表查询运维项目实例名称
     * @param aiopsItemIdList 运维项目Id列表
     * @return 字典列表
     */
    List<Map<String, Object>> selectAiopsInstanceNameByAiopsItemIdList(Collection<String> aiopsItemIdList);

    /**
     * 依据租户模组合约明细Id列表查询实例表已授权数量
     * @param tmcdIdList 租户模组合约明细Id
     * @return 字典列表
     */
    List<Map<String, Object>> selectAiAuthedCountByTmcdIdList(@Param("tmcdIdList") Collection<Long> tmcdIdList);


    Integer selectAiAuthedCountByAiopsItemIds(@Param("aiopsItemIdList") List<String> aiopsItemIdList);

    /**
     *
     * @param tmcdId 租户模组合约明细Id
     * @return 字典列表
     */
    List<Long> selectAiIdByTmcdId(@Param("tmcdId") Long tmcdId,@Param("limit") int limit);
    void updateAiAuthRecoverAuth(@Param("aiIdList") Collection<Long> aiIdList);


    /**
     * 通过dbid获取扩展内容
     *
     * @param aiopsItemId 数据库id
     * @return
     */
    Map<String, String> selectInstanceExecParamsContent(String aiopsItemId);

    /**
     * 更新授权状态
     *
     * @param aiIdList
     */
    void updateRuleEngineAuthStatus(@Param("aiIdList") List<Long> aiIdList);

    /**
     * 通过设备id获取
     * @param deviceId
     */
    List<String> queryModuleCodeListByDeviceId(String deviceId);

    /**
     * 根据实例查询设备id
     * @param aiIdList
     * @return
     */
    List<String> selectDeviceIdByAiIdList(@Param("aiIdList")List<Long> aiIdList);
    AiopsTpModuleInstance selectAiopsTpInstanceByAiopsItemId(@Param("aiopsItemId")String aiopsItemId);
    String selectTpAiopsItemIdByAtmiId(@Param("atmiId")Long atmiId);
    List<AiopsTpModuleInstance> selectDeviceTpInstanceByDeviceIdAndEid(@Param("eid")Long eid,@Param("deviceId")String deviceId,@Param("tpModule")String tpModule);
    Integer batchInsertOrUpdateTpModuleInstance(
            @Param("aiopsTpModuleInstances") List<AiopsTpModuleInstance> aiopsTpModuleInstances);
    String selectAiopsInstanceAiopsItemByDeviceId(String deviceId);

    /**
     * 更新扫描时间
     * @param tpTenantId
     * @param tpDeviceId
     * @param lastScanTime
     * @return
     */
    int updateModuleInstanceTpLastScanTime(@Param("tpTenantId") String tpTenantId,
                                           @Param("tpDeviceId") String tpDeviceId,
                                           @Param("lastScanTime") String lastScanTime);

    /**
     * 获取当前租户下最新设备扫描时间
     *
     * @param eid
     * @param tpTenantId
     * @return
     */
    List<AiopsTpModuleInstance> selectAiopsTpModuleInstance(@Param("eid") Long eid,
                                                            @Param("tpTenantId") String tpTenantId);

    /**
     * 获取三方实例最新扫描漏洞时间
     *
     * @param deviceId
     * @return
     */
    String getTpMaxLastScanTimeByDeviceId(String deviceId);
	
	/**
     * 通过eid获取运维实例
     *
     * @param aiopsItemList
     */
    List<DeviceAiopsItem> selectAiopsItemListByEid(@Param("eid") Long eid,
                                                   @Param("aiopsItemList") List<String> aiopsItemList);

    /**
     * 查询我们的设备  1.根据eid 查询ad表 并且查询ai表是已授权的 2.不在adt表 3.不再atmi表 的我们的设备
     * @param eid
     * @return
     */
    List<AiopsKitDevice> selectDeviceByEid(Long eid);

    /**
     * 查询 亚信设备id
     * @param eid
     * @return
     */
    List<String> selectAsiaDeviceIdByEid(Long eid);

    AiopsTpModuleInstance selectAiopsTpInstanceByUniqueKey(@Param("deviceId")String deviceId,@Param("eid")Long eid,@Param("tpModule")String tpModule,@Param("tpTenantId")String tpTenantId,@Param("tpDeviceId")String tpDeviceId);

    /**
     * 根据条件查询运维实例Id列表
     * @param map 条件字典
     * @return 运维实例Id列表
     */
    List<Long> selectAiIdListByMap(Map<String, Object> map);

    /**
     * 依据实例Id查询租户Sid
     * @param aiId 实例Id
     * @return 租户Sid
     */
    Long selectEidByAiId(@Param("aiId") Long aiId);

    /**
     * 获取设备在线数
     *
     * @param eid
     * @return
     */
    List<Map<Object, Object>> queryDeviceOnline(@Param("eid") long eid, @Param("samcdIdList") List<Long> samcdIdList);

    /**
     * 获取主机终端在线离线设备数
     *
     * @param eid
     */
    List<Map<Object, Object>> selectHostAndClientOnline(Long eid);

    /**
     * 依据租户 查询设备看板明细
     *
     * @param eid 租户Id
     * @return 看板明细数据
     */
    List<DeviceDashboardDetail> selectDeviceDashboardDetail(@Param("eid") Long eid);

    String selectItemType(@Param("aiopsItem") String aiopsItem);

    List<DeviceInstanceInfo> selectAiopsKitDeviceList(@Param("aiIdList") Set<Long> aiIdList, @Param("eid") Long eid);

    List<EdrDevice> selectEdrServerIdList(String deviceId);

    List<AiopsItemExtend> selectAiopsItemByGroupCode(@Param("eid") Long eid,
                                            @Param("aiopsItemGroup") String aiopsItemGroup,
                                            @Param("aiopsItemList") Set<String> aiopsItemList,
                                            @Param("aiopsAuthStatusList") List<AiopsAuthStatus> aiopsAuthStatusList);

    List<Map<String,Object>> selectTrustDetailByAiIdList(@Param("aiopsItemType") String aiopsItemType,
                                                       @Param("aiIdList") Set<Long> aiIdList,
                                                       @Param("eid") Long eid);
    String selectAiopsItemTypeByAiopsItem(@Param("aiopsItem") String aiopsItem);

    Long getEidByServiceCode(@Param("serviceCode") String serviceCode);

    /**
     * 根据运维项目查询收集项
     * @param aiopsItemList
     * @return
     */
    List<CollectConfigDTO> selectAccByAiopsItem(AccRequest request);
    List<CollectConfig> selectAiopsItem(@Param("aiopsItemList") List<String> aiopsItemList);

    /**
     * 根据运维实例Id列表查询aiopsItem列表
     * @param aiIdList 运维实例Id列表
     * @return aiopsItem列表
     */
    List<String> selectAiopsItemListByAiIdList(@Param("aiIdList") List<Long> aiIdList);
}