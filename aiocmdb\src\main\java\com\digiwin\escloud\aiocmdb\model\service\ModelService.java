package com.digiwin.escloud.aiocmdb.model.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.asset.service.impl.ModelEventListener;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.exception.BizException;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.common.util.auto.MessageUtils;
import com.digiwin.escloud.aiocmdb.cache.ModelCache;
import com.digiwin.escloud.aiocmdb.constant.Constant;
import com.digiwin.escloud.aiocmdb.etl.dao.EdgeEtlMapper;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineRespDTO;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineSaveReqDTO;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.etl.service.*;
import com.digiwin.escloud.aiocmdb.field.dao.FieldMapper;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.fieldset.dao.FieldSetMapper;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSet;
import com.digiwin.escloud.aiocmdb.model.ModelEtlInfo;
import com.digiwin.escloud.aiocmdb.model.ModelRelate;
import com.digiwin.escloud.aiocmdb.model.ModelRelateInstance;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.dto.InnerFieldDTO;
import com.digiwin.escloud.aiocmdb.model.dto.ModelAndEtlReqDTO;
import com.digiwin.escloud.aiocmdb.model.dto.ModelAndEtlRespDTO;
import com.digiwin.escloud.aiocmdb.model.dto.ModelGroupRespDTO;
import com.digiwin.escloud.aiocmdb.model.model.*;
import com.digiwin.escloud.aiocmdb.model.vo.ModelGroupVo;
import com.digiwin.escloud.aiocmdb.model.vo.ModelVo;
import com.digiwin.escloud.aiocmdb.util.BizUtils;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.etl.model.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.asset.service.impl.AssetCategoryServiceImpl.CATEGORY_DEFAULT_SCOPE_ID;
import static com.digiwin.escloud.common.constant.AioConstant.GLOBAL_APP_CODE;
import static com.digiwin.escloud.aiocmdb.constant.Constant.*;

@Slf4j
@Service
public class ModelService implements IModelService, ParamCheckHelp {
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private FieldMapper fieldMapper;
    @Autowired
    private FieldSetMapper fieldSetMapper;
    @Autowired
    private ModelCache modelCache;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private EtlMapper etlMapper;
    @Autowired
    private EdgeEtlMapper edgeEtlMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Value("${esc.integration.datauri}")
    private String dataUri;
    @Value("${esc.cmdb.defaultlanguage:zh_CN}")
    private String cmdbDefaultLanguage;
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;
    @Autowired
    private BizUtils bizUtils;
    @Autowired
    private IEdgeEtlService edgeEtlService;
    @Autowired
    private IEtlService etlService;
    @Autowired
    private EtlStoreServiceFactory etlStoreServiceFactory;
    @Autowired
    private MessageUtils messageUtils;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Override
    public BaseResponse<List<ModelGroup>> getModelGroupList(String appCode) {
        long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        if (StringUtils.isBlank(appCode)) {
            appCode = RequestUtil.getHeaderAppCode();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        if (StringUtils.isBlank(appCode)) {
            map.put("appCode", GLOBAL_APP_CODE);
        } else {
            map.put("appCodeList", Stream.of(appCode, GLOBAL_APP_CODE).collect(Collectors.toList()));
        }
        return BaseResponse.ok(modelMapper.getModelGroupList(map));
    }

    @Override
    public List<ModelGroupRespDTO> getModelGroup(String modelGroupCode) {
        // 获取所有的内置字段包含该分类已选择的字段
        List<InnerFieldDTO> innerFieldList = modelMapper.getFieldInModelGroup(modelGroupCode);
        // 获取用途下的所有字段和该分类所选择的用途
        List<ModelGroupFunction> modelGroupFunctionList = modelMapper.getModelGroupFunction(modelGroupCode);
        List<ModelGroupRespDTO> respDTO = new ArrayList<>();
        /* modelGroupFunctionList 通过code分组得到 fieldCode集合 */
        LinkedHashMap<String, List<String>> groupedFieldCodes = modelGroupFunctionList.stream().sorted(Comparator.comparing(ModelGroupFunction::getSort))
                .collect(Collectors.groupingBy(
                        ModelGroupFunction::getCode, LinkedHashMap::new,
                        Collectors.mapping(ModelGroupFunction::getFieldCode, Collectors.toList())
                ));
        for (Map.Entry<String, List<String>> entry : groupedFieldCodes.entrySet()) {
            ModelGroupRespDTO modelGroupRespDTO = new ModelGroupRespDTO();
            String modelGroupFunctionCode = entry.getKey();
            // 从modelGroupFunctionList找到第一个code和modelGroupFunctionCode相等的元素、填充用途的信息和是否被选择了
            modelGroupFunctionList.stream().filter(modelGroupFunction -> modelGroupFunction.getCode().equals(modelGroupFunctionCode))
                    .findFirst()
                    .ifPresent(modelGroupFunction -> {
                        modelGroupRespDTO.setModelGroupFunctionName(modelGroupFunction.getName());
                        modelGroupRespDTO.setModelGroupFunctionCode(modelGroupFunction.getCode());
                        modelGroupRespDTO.setSelected(modelGroupFunction.isSelected());
                        respDTO.add(modelGroupRespDTO);
                    });
            List<String> requiredFieldCodeList = entry.getValue();
            // 遍历所有内置字段，并且根据该用途配置的字段,生成新的内置字段列表
            List<InnerFieldDTO> innerFields = new ArrayList<>();
            innerFieldList.forEach(innerField -> {
                InnerFieldDTO innerFieldDTO = new InnerFieldDTO();
                innerFieldDTO.setId(innerField.getId());
                innerFieldDTO.setFieldCode(innerField.getFieldCode());
                innerFieldDTO.setFieldName(innerField.getFieldName());
                innerFieldDTO.setKey(innerField.isKey() && modelGroupRespDTO.isSelected());
                // 字段选中的同时，该用途也被选中，则认为该字段被选择了
                innerFieldDTO.setSelected(innerField.isSelected() && modelGroupRespDTO.isSelected());
                // 如果是用途配置的字段，则该字段必填,并且默认选中
                if (requiredFieldCodeList.contains(innerField.getFieldCode())) {
                    innerFieldDTO.setSelected(true);
                    innerFieldDTO.setRequired(true);
                    // 必填字段的排序为该用途配置的顺序
                    innerFieldDTO.setSort(requiredFieldCodeList.indexOf(innerField.getFieldCode()));
                } else {
                    // 非必填字段，排序为内置字段的id
                    innerFieldDTO.setSort(innerField.getId());
                }
                innerFields.add(innerFieldDTO);
            });
            // 排序innerFields,必填字段，选择字段, 未选字段名称
            List<InnerFieldDTO> sortedInnerFields = innerFields.stream().sorted(
                    Comparator.comparing(InnerFieldDTO::getSort)
                            .thenComparing(InnerFieldDTO::isRequired, Comparator.reverseOrder())
                            .thenComparing(InnerFieldDTO::isSelected, Comparator.reverseOrder())
                            .thenComparing(InnerFieldDTO::getFieldName, Comparator.reverseOrder())
            ).collect(Collectors.toList());
            // 填充改用途下的内置字段列表
            modelGroupRespDTO.setInnerFields(sortedInnerFields);
        }
        return respDTO;
    }

    @Override
    public BaseResponse addModelGroup(ModelGroup modelGroup) {
        BaseResponse res = new BaseResponse();
        try {
            // 新增
            long sid = RequestUtil.getHeaderSid();
            modelGroup.setSid(sid);
            modelGroup.setEid(RequestUtil.getHeaderEid());
            modelGroup.setId(SnowFlake.getInstance().newId());
            modelGroup.setModelGroupCode(Constant.MODEL_GROUP + modelGroup.getId());
            modelGroup.setAppCode(RequestUtil.getHeaderAppCode());
            // 保存分类的用途
            modelGroup.setModelGroupFunctionCode(modelGroup.getModelGroupFunctionCode());
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelGroupName", modelGroup.getModelGroupName());
            map.put("mode", "insert");
            Integer modelGroupExist = modelMapper.getModelGroupNameExist(map);
            if (!ObjectUtils.isEmpty(modelGroupExist)) {
                res.setCode(ResponseCode.MODELGROUPNAME_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODELGROUPNAME_IS_EXIST.getMsg());
                return res;
            }
            if (modelMapper.addModelGroup(modelGroup)) {
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.INSERT_FAILD.toString());
                res.setErrMsg(ResponseCode.INSERT_FAILD.getMsg());
            }
            // 保存内置字段
            List<ModelGroupField> modelGroupFields = buildModelGroupField(modelGroup);
            if (!CollectionUtils.isEmpty(modelGroupFields)) {
                modelMapper.batchInsertModelGroupField(modelGroupFields);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    public List<ModelGroupField> buildModelGroupField(ModelGroup modelGroup) {
        List<ModelGroupField> poList = new ArrayList<>();
        List<Field> modelGroupFunctionFields = modelGroup.getModelGroupFunctionFields();
        if (!CollectionUtils.isEmpty(modelGroupFunctionFields)) {
            for (int i = 0; i < modelGroupFunctionFields.size(); i++) {
                Field field = modelGroupFunctionFields.get(i);
                ModelGroupField po = new ModelGroupField();
                po.setId(SnowFlake.getInstance().newId());
                po.setSid(RequestUtil.getHeaderSid());
                po.setEid(RequestUtil.getHeaderEid());
                po.setAppCode(RequestUtil.getHeaderAppCode());
                po.setModelGroupCode(modelGroup.getModelGroupCode());
                po.setFieldCode(field.getFieldCode());
                po.setKey(field.isKey());
                po.setSort(i + 1);
                poList.add(po);
            }
        }
        return poList;
    }

    @Override
    public BaseResponse modifyModelGroup(ModelGroup modelGroup) {
        BaseResponse res = new BaseResponse();
        try {
            // 修改
            long sid = RequestUtil.getHeaderSid();
            modelGroup.setSid(sid);
            HashMap<String, Object> map = new HashMap<>();
            map.put("id", modelGroup.getId());
            map.put("modelGroupName", modelGroup.getModelGroupName());
            map.put("mode", "update");
            Integer modelGroupExist = modelMapper.getModelGroupNameExist(map);
            if (!ObjectUtils.isEmpty(modelGroupExist)) {
                res.setCode(ResponseCode.MODELGROUPNAME_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODELGROUPNAME_IS_EXIST.getMsg());
                return res;
            }
            // 原来的分组内置字段
            List<ModelGroupField> modelGroupFieldPoList = modelMapper.getModelGroupField(modelGroup.getModelGroupCode());
            if (modelMapper.modifyModelGroup(modelGroup)) {
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.UPDATE_FAILD.toString());
                res.setErrMsg(ResponseCode.UPDATE_FAILD.getMsg());
            }

            // 原来的分组字段和现在的分组字段比较子之后。得到需要删除的字段，需要新增的字段:
            Map<String, List<String>> compareModelGroupField = compareModelGroupField(modelGroupFieldPoList, modelGroup.getModelGroupFunctionFields());
            if (!CollectionUtils.isEmpty(compareModelGroupField.get(deleteKey)) || !CollectionUtils.isEmpty(compareModelGroupField.get(addKey))) {
                // 修改内置字段
                modelMapper.deleteModelGroupFieldByGroupCode(modelGroup.getModelGroupCode());
                List<ModelGroupField> modelGroupFields = buildModelGroupField(modelGroup);
                if (!CollectionUtils.isEmpty(modelGroupFields)) {
                    modelMapper.batchInsertModelGroupField(modelGroupFields);
                }
                // 修改模型BasicInfo数据
//                 updateModelBasicInfo4ModelGroup(modelGroup,modelGroupFieldPoList,modelGroupFields);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    final String deleteKey = "fieldCodeListNeedDelete";
    final String addKey = "fieldCodeListNeedAdd";

    private Map<String, List<String>> compareModelGroupField(List<ModelGroupField> modelGroupFieldPoList, List<Field> modelGroupFunctionFields) {
        // 从modelGroupFieldPoList中找到需要删除的fieldCode
        List<String> fieldCodeListNeedDelete = new ArrayList<>();
        for (ModelGroupField modelGroupFieldPo : modelGroupFieldPoList) {
            boolean poNeedDeleteFlag = true;
            for (Field modelGroupFunctionField : modelGroupFunctionFields) {
                if (modelGroupFieldPo.getFieldCode().equalsIgnoreCase(modelGroupFunctionField.getFieldCode()) && modelGroupFieldPo.isKey() == modelGroupFunctionField.isKey()) {
                    poNeedDeleteFlag = false;
                    break;
                }
            }
            if (poNeedDeleteFlag) {
                fieldCodeListNeedDelete.add(modelGroupFieldPo.getFieldCode());
            }
        }
        // 从modelGroupFunctionFields中找到需要新增的fieldCode
        List<String> fieldCodeListNeedAdd = new ArrayList<>();
        for (Field modelGroupFunctionField : modelGroupFunctionFields) {
            boolean functionFieldNeedAddFlag = true;
            for (ModelGroupField modelGroupFieldPo : modelGroupFieldPoList) {
                if (modelGroupFieldPo.getFieldCode().equalsIgnoreCase(modelGroupFunctionField.getFieldCode()) && modelGroupFieldPo.isKey() == modelGroupFunctionField.isKey()) {
                    functionFieldNeedAddFlag = false;
                    break;
                }
            }
            if (functionFieldNeedAddFlag) {
                fieldCodeListNeedAdd.add(modelGroupFunctionField.getFieldCode());
            }
        }
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put(deleteKey, fieldCodeListNeedDelete);
        resultMap.put(addKey, fieldCodeListNeedAdd);
        return resultMap;
    }

    /**
     * 修改模型BasicInfo数据
     *
     * @param modelGroup 新的分组入参
     * @param modelGroupFieldPoList  原分组的数据
     */
    private void updateModelBasicInfo4ModelGroup(ModelGroup modelGroup, List<ModelGroupField> modelGroupFieldPoList, List<ModelGroupField> modelGroupFieldNewList) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("modelGroupId", modelGroup.getId());
        // 需要修改的模型数据
        List<Model> existModelList = modelMapper.getModelByCond(map);
        if (CollectionUtils.isEmpty(existModelList)) {
            return;
        }
        List<String> modelCodeList = existModelList.stream().map(model -> model.getModelCode()).collect(Collectors.toList());
        // 删除某些字段：
        if (!CollectionUtils.isEmpty(modelGroupFieldPoList)) {
            List<String> fieldCodeListNeedDelete = modelGroupFieldPoList.stream().map(po -> po.getFieldCode()).collect(Collectors.toList());
            modelMapper.batchDeleteModelFieldMapping(Constant.BASE_GROUP_CODE, modelCodeList, fieldCodeListNeedDelete);
        }
        // 新增某些字段：
        if (!CollectionUtils.isEmpty(modelGroupFieldNewList)) {
            List<ModelFieldMapping> modelFieldMappings = buildModelFieldMapping(modelCodeList, modelGroupFieldNewList);
            modelMapper.batchAddModelFieldMapping(modelFieldMappings);
        }
    }

    private List<ModelFieldMapping> buildModelFieldMapping(List<String> modelCodeList, List<ModelGroupField> modelGroupFieldNewList) {
        List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();
        long sid = RequestUtil.getHeaderSid();
        for (ModelGroupField modelGroupField : modelGroupFieldNewList) {
            for (String modelCode : modelCodeList) {
                ModelFieldMapping mapping = new ModelFieldMapping();
                mapping.setId(SnowFlake.getInstance().newId());
                mapping.setSid(sid);
                mapping.setEid(RequestUtil.getHeaderEid());
                mapping.setModelCode(modelCode);
                mapping.setModelFieldGroupCode(Constant.BASE_GROUP_CODE);
                mapping.setModelSettingType(ModelSettingType.field.toString());
                mapping.setTargetCode(modelGroupField.getFieldCode());
                mapping.setHide(true);
                mapping.setSort(modelGroupField.getSort());
                modelFieldMappingList.add(mapping);
            }
        }
        return modelFieldMappingList;
    }

    @Override
    public BaseResponse deleteModelGroup(long id) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验 删除模型分类时，该分类下已定义了模型则不可删除分类
            Map<String, Object> map = new HashMap<>();
            map.put("sid", RequestUtil.getHeaderSid());
            map.put("modelGroupId", id);
            List<Model> existModelList = modelMapper.getModelByCond(map);
            if (!CollectionUtils.isEmpty(existModelList)) {
                res.setCode(ResponseCode.MODEL_IN_MODELGROUP.toString());
                res.setErrMsg(ResponseCode.MODEL_IN_MODELGROUP.getMsg());
                return res;
            }
            ModelGroup modelGroup = modelMapper.getModelGroup(id);
            //2 删除模型分类
            boolean result = modelMapper.deleteModelGroup(id);
            if (result) {
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.DELETE_FAILD.toString());
                res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
            }
            //3 删除模型分类下的内置字段
            if (modelGroup != null) {
                modelMapper.deleteModelGroupFieldByGroupCode(modelGroup.getModelGroupCode());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public List<ModelGroup> getAllModel(String appCode, String modelGroupCode, String modelGroupFunctionCode, Boolean showAll, String content) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("globalAppCode", GLOBAL_APP_CODE);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eidList", bizUtils.buildEidList());
        if (StringUtils.isBlank(appCode)) {
            appCode = RequestUtil.getHeaderAppCode();
        }
        map.put("appCode", appCode);
        map.put("modelGroupCode", modelGroupCode);
        map.put("modelGroupFunctionCode", modelGroupFunctionCode);
        map.put("content", content);
        buildShowAllModelParams(showAll, map);
        return modelMapper.getAllModel(map);
    }

    private void buildShowAllModelParams(Boolean showAll, HashMap<String, Object> map) {
        if (Boolean.FALSE.equals(showAll)) {
            map.put("modelGroupCodeNotInList", Arrays.asList("Runner", "CollectorElement", "ParserElement", "TransformElement", "SenderElement", "ExecParams", "DesignElement", "DynamicInstance", "OperatorElement", "DeviceAppTypeElement", "EdgeOps", "GuardianElement", "GuardianCustomizedElement", "GuardianRunningElement", "GuardianDataOptionElement", "GuardianDataTransformElement", "GuardianDataCollectionElement", "GuardianConnectElement"));
        }
    }

    @Override
    public PageInfo<ModelGroupVo> getAllModel(String appCode, String modelGroupCode, String content, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ModelGroup> allModel = getAllModel(appCode, modelGroupCode, null, null, content);

        Set<String> modelCodes = allModel.stream()
                .flatMap(mg -> mg.getModellist().stream())
                .map(Model::getModelCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(modelCodes)) {
            return new PageInfo<>();
        }
        Set<String> accModelCodes = modelMapper.selectAccModelByModelCodes(modelCodes);
        List<ModelGroupVo> modelRet = allModel.stream()
                .map(mg -> {
                    List<ModelVo> modelVoList = mg.getModellist().stream()
                            .map(model -> {
                                ModelVo modelVo = new ModelVo();
                                BeanUtils.copyProperties(model, modelVo);
                                modelVo.setModelCode(model.getModelCode());
                                modelVo.setAssociationCollect(accModelCodes.contains(model.getModelCode()));
                                return modelVo;
                            })
                            .collect(Collectors.toList());
                    ModelGroupVo modelGroupVo = new ModelGroupVo();
                    BeanUtils.copyProperties(mg, modelGroupVo);
                    modelGroupVo.setModelGroupCode(mg.getModelGroupCode());
                    modelGroupVo.setModelGroupName(mg.getModelGroupName());
                    modelGroupVo.setModellist(modelVoList);
                    return modelGroupVo;
                })
                .collect(Collectors.toList());

        PageInfo<ModelGroupVo> ret = new PageInfo<>();
        BeanUtils.copyProperties(new PageInfo<>(allModel), ret);
        ret.setList(modelRet);
        return ret;
    }

    @Override
    public BaseResponse addModel(Model model) {
        BaseResponse res = new BaseResponse();
        //1 校验，模型编码唯一性
        if (modelMapper.findModelCode(model.getModelCode()) != null) {
            res.setCode(ResponseCode.MODELCODE_IS_EXIST.toString());
            res.setErrMsg(ResponseCode.MODELCODE_IS_EXIST.getMsg());
            return res;
        }
        List<ModelGroupField> modelGroupFields = modelMapper.getModelGroupField(model.getModelGroupCode());
        if (CollectionUtils.isEmpty(modelGroupFields)) {
            res.setCode(ResponseCode.MODELGROUP_FIELD_NOT_SET.toString());
            res.setErrMsg(ResponseCode.MODELGROUP_FIELD_NOT_SET.getMsg());
            return res;
        }
        Optional<ModelGroupField> keyModelGroupField = modelGroupFields.stream()
                .filter(ModelGroupField::isKey)
                .findAny();
        if (!keyModelGroupField.isPresent()) {
            res.setCode(ResponseCode.MODELGROUP_FIELD_NOT_KEY.toString());
            res.setErrMsg(ResponseCode.MODELGROUP_FIELD_NOT_KEY.getMsg());
            return res;
        }
        boolean result;
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            //2 新增模型时，要初始化基本资讯模型分类，初始化系统字段到基本资讯分类下
            long sid = RequestUtil.getHeaderSid();
            long eid = RequestUtil.getHeaderEid();
            //2.1 初始化基本资讯模型分类
            ModelFieldGroup modelFieldGroup = new ModelFieldGroup();
            modelFieldGroup.setSid(sid);
            modelFieldGroup.setModelCode(model.getModelCode());
            modelFieldGroup.setModelFieldGroupCode(BASE_GROUP_CODE);
            HashMap<String, String> groupNameMap = new HashMap<>();
            groupNameMap.put("zh_CN", "基本资讯");
            groupNameMap.put("zh_TW", "基本資訊");
            groupNameMap.put("vi_VN", "基本資訊");
            groupNameMap.put("th_TH", "基本資訊");
            groupNameMap.put("ms_MY", "基本資訊");
            String groupName = groupNameMap.get(cmdbDefaultLanguage);
            modelFieldGroup.setModelFieldGroupName(groupName);
            modelFieldGroup.setFold(true);//默认基本资讯是展开的
            modelFieldGroup.setId(SnowFlake.getInstance().newId());
            result = modelMapper.addModelFieldGroup(modelFieldGroup);
            //2.2 初始化系统字段到基本资讯分类下
            if (result) {
                List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();
                for (ModelGroupField modelGroupField : modelGroupFields) {
                    ModelFieldMapping modelFieldMapping = new ModelFieldMapping();
                    modelFieldMapping.setId(SnowFlake.getInstance().newId());
                    modelFieldMapping.setModelCode(model.getModelCode());
                    modelFieldMapping.setModelFieldGroupCode(modelFieldGroup.getModelFieldGroupCode());
                    modelFieldMapping.setModelSettingType(ModelSettingType.field.name());
                    modelFieldMapping.setTargetCode(modelGroupField.getFieldCode());
                    modelFieldMapping.setCollection(false);
                    modelFieldMapping.setHide(false);
                    modelFieldMapping.setShowTitle(true);
                    modelFieldMapping.setSid(sid);
                    modelFieldMapping.setEid(eid);
                    modelFieldMapping.setSort(modelGroupField.getSort());
                    modelFieldMapping.setKey(modelGroupField.isKey());
                    modelFieldMappingList.add(modelFieldMapping);
                }
                //批量将系统字段插入到模型字段映射表
                result = modelMapper.batchAddModelFieldMapping(modelFieldMappingList);
            }
            //2.2 如果分组的用途下有默认的分组，则需要给模型加入该分组
            saveModelFieldGroup(model);
            //2.3 新增模型
            if (result) {
                model.setSid(sid);
                model.setEid(eid);
                model.setId(SnowFlake.getInstance().newId());
                model.setStatus(ModelStatus.Y.toString());
                model.setAppCode(RequestUtil.getHeaderAppCode());
                result = modelMapper.addModel(model);
            }
            transactionManager.commit(transactionStatus);
            Map<String, Object> resultMap = new HashMap<>(2);
            resultMap.put("modelCode", model.getModelCode());
            resultMap.put("id", model.getId());
            res.setData(resultMap);
            res.setCode(ResponseCode.SUCCESS.toString());
        } catch (Exception e) {
            log.error("addModel", e);
            transactionManager.rollback(transactionStatus);
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(e.getMessage());
        }
        if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
            Runnable runnable = () -> {
                modelCache.insertModelDetail(model.getModelCode());
                modelCache.insertModelJson(model.getModelCode());
                commonUtils.saveEtlEngine(Stream.of(model.getModelCode()).collect(Collectors.toList()));
                //根据配置自动建表
                commonUtils.autoCreateTable(Stream.of(model.getModelCode()).collect(Collectors.toList()));
            };
            commonUtils.asyncRun(runnable);
        }
        return res;
    }

    /**
     * 如果分组的用途下有默认的分组，则需要给模型加入该分组
     * @param model
     */
    private void saveModelFieldGroup(Model model) {
        List<ModelGroupFunctionFieldGroup> modelGroupFunctionFieldGroupList = modelMapper.getModelGroupFunctionFieldGroupByModelGroupCode(model.getModelGroupCode());
        if (!CollectionUtils.isEmpty(modelGroupFunctionFieldGroupList)) {
            for (ModelGroupFunctionFieldGroup modelGroupFunctionFieldGroup : modelGroupFunctionFieldGroupList) {
                ModelFieldGroup modelFieldGroup = new ModelFieldGroup();
                modelFieldGroup.setSid(RequestUtil.getHeaderSid());
                modelFieldGroup.setModelCode(model.getModelCode());
                modelFieldGroup.setModelFieldGroupCode(modelGroupFunctionFieldGroup.getModelFieldGroupCode());
                modelFieldGroup.setModelFieldGroupName(modelGroupFunctionFieldGroup.getModelFieldGroupName());
                modelFieldGroup.setFold(true);
                modelFieldGroup.setId(SnowFlake.getInstance().newId());
                modelMapper.addModelFieldGroup(modelFieldGroup);
            }
        }
    }

    @Override
    public BaseResponse modifyModel(Model model) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验，字段集编码唯一性
            Model existModel = modelMapper.findModelCode(model.getModelCode());
            if (existModel != null && existModel.getId() != model.getId()) {
                res.setCode(ResponseCode.MODELCODE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODELCODE_IS_EXIST.getMsg());
                return res;
            }
            //todo 校验引用的模型或字段集名称
            //2 修改
            long sid = RequestUtil.getHeaderSid();
            model.setSid(sid);
            if (modelMapper.modifyModel(model)) {
                commonUtils.asyncClearModelCache(model.getModelCode());
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.UPDATE_FAILD.toString());
                res.setErrMsg(ResponseCode.UPDATE_FAILD.getMsg());
            }
            // 重置资产模型得默认显示字段
            eventPublisher.publishEvent(new ModelModifiedEvent(this, model.getModelCode(), sid));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }


    @Override
    public BaseResponse checkModelRelate(String modelCode) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验，模型中是否存在关联
            long sid = RequestUtil.getHeaderSid();
            List<ModelRelate> existModelRelate = modelMapper.getModelRelateList(sid, modelCode);
            if (!CollectionUtils.isEmpty(existModelRelate)) {
                StringBuffer stringBuffer = new StringBuffer();
                for (ModelRelate modelRelate : existModelRelate) {
                    stringBuffer.append(modelRelate.getTargetModelName()).append(",");
                }
                String targetModelName = stringBuffer.substring(0, stringBuffer.length() - 1);
                res.setCode(ResponseCode.MODERELATE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODERELATE_IS_EXIST.getMsg());
                res.setData(targetModelName);
                return res;
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        res.setCode(ResponseCode.SUCCESS.toString());

        return res;
    }

    @Override
    @Transactional
    public BaseResponse deleteModel(String modelCode) {
        return deleteModelCode(RequestUtil.getHeaderAppCode(), modelCode);
    }

    private BaseResponse deleteModelCode(String appCode, String modelCode) {
        BaseResponse res = new BaseResponse();
        try {
            BaseResponse baseResponse = relateAssetCheck(modelCode);
            if (!baseResponse.checkIsSuccess()) {
                return baseResponse;
            }
            //1 删除模型关联
            modelMapper.deleteModelRelate(modelCode);
            modelMapper.deleteModelRelateInstance(modelCode);
            //2 自动建表自动删除
            commonUtils.autoDeleteTable(Stream.of(modelCode).collect(Collectors.toList()));
            //3 删除模型
            if (modelMapper.deleteModel(modelCode)) {
                etlMapper.deleteEtlEngine(appCode, modelCode);
                edgeEtlMapper.deleteByAppCodeModelCode(appCode, modelCode);
                modelCache.clearCurrentModel(modelCode);
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.DELETE_FAILD.toString());
                res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    private BaseResponse relateAssetCheck(String modelCode) {
        int cnt = assetCategoryMapper.countBySidScopeIdCategoryNumber(RequestUtil.getHeaderSid(), CATEGORY_DEFAULT_SCOPE_ID, modelCode, null,null);
        if (cnt > 0) {
            return BaseResponse.error(ResponseCode.ASSET_BE_RELATED);
        }
        return BaseResponse.ok();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse batchDeleteModel(List<Map<String, Object>> mapList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(mapList, "mapList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        String headerAppCode = RequestUtil.getHeaderAppCode();
        BaseResponse res = mapList.stream().filter(CollectionUtil::isNotEmpty).distinct().map(x -> {
            String appCode = Objects.toString(x.get("appCode"), headerAppCode);
            if (StringUtils.isBlank(appCode)) {
                appCode = headerAppCode;
            }
            String modelCode = Objects.toString(x.get("modelCode"), "");
            if (StringUtils.isBlank(modelCode)) {
                return BaseResponse.ok();
            }
            return deleteModelCode(appCode, modelCode);
        }).filter(x -> !x.checkIsSuccess()).findFirst().orElse(BaseResponse.ok(mapList.size()));
        if (!res.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return res;
    }

    @Override
    public Map<String, Object> getModelDetail(String modelCode) {
        long sid = RequestUtil.getHeaderSid();
        //1 获取模型中的属性
        ModelDetail modelDetail = modelCache.selectModelDetail(modelCode);
        //2 获取模型的关联
        List<ModelRelate> relateList = modelMapper.getModelRelateList(sid, modelCode);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("field", modelDetail);
        resultMap.put("relation", relateList);

        return resultMap;
    }

    @Override
    public BaseResponse getModelDetailList(List<String> modelCodeList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCodeList, "modelCodeList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(modelCodeList.stream().filter(StringUtils::isNotBlank).distinct().map(x -> {
            Map<String, Object> resultMap = getModelDetail(x);
            resultMap.put("modelCode", x);
            return resultMap;
        }).collect(Collectors.toMap(x -> Objects.toString(x.get("modelCode"), ""), x -> {
            x.remove("modelCode");
            return x;
        }, (x, y) -> y)));
    }

    @Override
    public List<ModelFieldGroup> getModelFieldGroupList(String modelCode) {
        long sid = RequestUtil.getHeaderSid();
        return modelMapper.getModelFieldGroupList(sid, modelCode);
    }

    @Override
    public BaseResponse addModelFieldGroup(ModelFieldGroup modelFieldGroup) {
        BaseResponse res = new BaseResponse();
        try {
            // 新增
            long sid = RequestUtil.getHeaderSid();
            modelFieldGroup.setSid(sid);
            modelFieldGroup.setId(SnowFlake.getInstance().newId());
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelCode", modelFieldGroup.getModelCode());
            map.put("modelFieldGroupCode", modelFieldGroup.getModelFieldGroupCode());
            map.put("mode", "insert");
            Integer modelFieldGroupExist = modelMapper.getModelFieldGroupExist(map);
            if (!ObjectUtils.isEmpty(modelFieldGroupExist)) {
                res.setCode(ResponseCode.MODELFIELDGROUPCODE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODELFIELDGROUPCODE_IS_EXIST.getMsg());
                return res;
            }
            if (modelMapper.addModelFieldGroup(modelFieldGroup)) {
                commonUtils.asyncClearModelCache(modelFieldGroup.getModelCode());
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.INSERT_FAILD.toString());
                res.setErrMsg(ResponseCode.INSERT_FAILD.getMsg());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse modifyModelFieldGroup(ModelFieldGroup modelFieldGroup) {
        BaseResponse res = new BaseResponse();
        try {
            // 修改
            long sid = RequestUtil.getHeaderSid();
            modelFieldGroup.setSid(sid);
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelCode", modelFieldGroup.getModelCode());
            map.put("modelFieldGroupCode", modelFieldGroup.getModelFieldGroupCode());
            map.put("id", modelFieldGroup.getId());
            map.put("mode", "update");
            Integer modelFieldGroupExist = modelMapper.getModelFieldGroupExist(map);
            if (!ObjectUtils.isEmpty(modelFieldGroupExist)) {
                res.setCode(ResponseCode.MODELFIELDGROUPCODE_IS_EXIST.toString());
                res.setErrMsg(ResponseCode.MODELFIELDGROUPCODE_IS_EXIST.getMsg());
                return res;
            }
            if (modelMapper.modifyModelFieldGroup(modelFieldGroup)) {
                commonUtils.asyncClearModelCache(modelFieldGroup.getModelCode());
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.UPDATE_FAILD.toString());
                res.setErrMsg(ResponseCode.UPDATE_FAILD.getMsg());
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse deleteModelFieldGroup(String modelCode, String modelFieldGroupCode) {
        BaseResponse res = new BaseResponse();
        try {
            //1 校验 删除模型字段分组时，该分组下已定义了字段则不可删除分组
            Map<String, Object> map = new HashMap<>();
            map.put("sid", RequestUtil.getHeaderSid());
            map.put("modelCode", modelCode);
            map.put("modelFieldGroupCode", modelFieldGroupCode);
            List<ModelFieldMapping> existModelFieldMappingList = modelMapper.getModelFieldMappingList(map);
            if (!CollectionUtils.isEmpty(existModelFieldMappingList)) {
                res.setCode(ResponseCode.MODELFIELDMAPPING_IN_MODELFIELDGROUP.toString());
                res.setErrMsg(ResponseCode.MODELFIELDMAPPING_IN_MODELFIELDGROUP.getMsg());
                return res;
            }
            //2 删除模型字段分组
            boolean result = modelMapper.deleteModelFieldGroup(modelCode, modelFieldGroupCode);
            if (result) {
                commonUtils.asyncClearModelCache(modelCode);
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.DELETE_FAILD.toString());
                res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse addFieldToModel(String modelCode, String modelFieldGroupCode, List<Field> list) {
        BaseResponse res = new BaseResponse();
        try {
            if (CollectionUtils.isEmpty(list)) {
                res.setCode(ResponseCode.FIELD_IS_NULL.toString());
                res.setErrMsg(ResponseCode.FIELD_IS_NULL.getMsg());
                return res;
            }
            // 新增
            long sid = RequestUtil.getHeaderSid();
            List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();

            //查该模型现有的最大sort
            int sort = modelMapper.selectMaxSort(sid, modelCode);
            for (Field field : list) {
                ModelFieldMapping mapping = new ModelFieldMapping();
                mapping.setId(SnowFlake.getInstance().newId());
                mapping.setSid(sid);
                mapping.setEid(RequestUtil.getHeaderEid());
                mapping.setModelCode(modelCode);
                mapping.setModelFieldGroupCode(modelFieldGroupCode);
                mapping.setModelSettingType(ModelSettingType.field.toString());
                mapping.setTargetCode(field.getFieldCode());
                mapping.setHide(true);
                mapping.setSort(++sort);
                modelFieldMappingList.add(mapping);
            }

            if (modelMapper.batchAddModelFieldMapping(modelFieldMappingList)) {
                commonUtils.asyncClearModelCache(modelCode);
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.INSERT_FAILD.toString());
                res.setErrMsg(ResponseCode.INSERT_FAILD.getMsg());
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse addFieldSetToModel(String modelCode, String modelFieldGroupCode, List<FieldSet> list) {
        BaseResponse res = new BaseResponse();
        try {
            if (CollectionUtils.isEmpty(list)) {
                res.setCode(ResponseCode.FIELDSET_IS_NULL.toString());
                res.setErrMsg(ResponseCode.FIELDSET_IS_NULL.getMsg());
                return res;
            }
            // 新增
            long sid = RequestUtil.getHeaderSid();
            List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();

            //查该模型现有的最大sort
            int sort = modelMapper.selectMaxSort(sid, modelCode);
            for (FieldSet fieldSet : list) {
                ModelFieldMapping mapping = new ModelFieldMapping();
                mapping.setId(SnowFlake.getInstance().newId());
                mapping.setSid(sid);
                mapping.setEid(RequestUtil.getHeaderEid());
                mapping.setModelCode(modelCode);
                mapping.setModelFieldGroupCode(modelFieldGroupCode);
                mapping.setModelSettingType(ModelSettingType.fieldSet.toString());
                mapping.setTargetCode(fieldSet.getFieldSetCode());
                mapping.setHide(true);
                mapping.setSort(++sort);
                modelFieldMappingList.add(mapping);
            }
            if (modelMapper.batchAddModelFieldMapping(modelFieldMappingList)) {

                commonUtils.asyncClearModelDetailCache(modelCode);
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.INSERT_FAILD.toString());
                res.setErrMsg(ResponseCode.INSERT_FAILD.getMsg());
            }

        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }

        return res;
    }

    @Override
    public BaseResponse getModelRelateType() {
        try {
            return BaseResponse.ok(modelMapper.getModelRelateType());
        } catch (Exception ex) {
            log.error("getModelRelateType", ex);
            return BaseResponse.error(ex);
        }
    }

    @Override
    public BaseResponse saveModelRelate(ModelRelate modelRelate) {
        try {
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelRelateCode", modelRelate.getModelRelateCode());
            if (modelRelate.getId() > 0)
                map.put("mode", "update");
            else {
                map.put("mode", "insert");
                modelRelate.setId(SnowFlake.getInstance().newId());
            }
            Integer modelRelateExist = modelMapper.getModelRelateExist(map);
            if (!ObjectUtils.isEmpty(modelRelateExist))
                return BaseResponse.error(ResponseCode.MODELRELATECODE_IS_EXIST);
            modelRelate.setSid(RequestUtil.getHeaderSid());
            modelMapper.saveModelRelate(modelRelate);
            commonUtils.asyncClearModelDetailCache(modelRelate.getSourceModelCode());
            return BaseResponse.ok();
        } catch (Exception ex) {
            log.error("saveModelRelate", ex);
            return BaseResponse.error(ex);
        }
    }

    @Override
    public BaseResponse batchSaveModelRelate(List<ModelRelate> modelRelateList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelRelateList, "modelRelateList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        modelRelateList.stream().filter(x -> LongUtil.isEmpty(x.getId()))
                .forEach(x -> x.setId(SnowFlake.getInstance().newId()));

        return BaseResponse.ok(modelMapper.batchInsertOrUpdateModelRelate(modelRelateList));
    }


    @Override
    public HashMap<String, Object> getModelRelateChecklist(ModelRelate modelRelate) {
        HashMap<String, Object> map = new HashMap<>();
        List<String> res = new ArrayList<>();
        ModelRelateInstance modelRelateInstance = modelMapper.getModelRelateInstance(modelRelate.getModelRelateCode());
        Optional.ofNullable(modelRelateInstance).ifPresent(o -> {
            String sourceFieldValue = o.getSourceFieldValue();
            String targetFieldValue = o.getTargetFieldValue();
            res.add(StringUtils.isEmpty(sourceFieldValue) ?
                    o.getSourceModelCode() : o.getSourceModelCode() + "(" + sourceFieldValue + ")");
            res.add(StringUtils.isEmpty(targetFieldValue) ?
                    o.getTargetModelCode() : o.getTargetModelCode() + "(" + targetFieldValue + ")");
        });
        if (!CollectionUtils.isEmpty(res)) {
            map.put("modelRelate", res);
            return map;
        }
        ResponseBase bindProductCodes = aioItmsFeignClient.getBindProductCodes(modelRelate.getSourceModelCode(), modelRelate.getTargetModelCode());
        if (ResponseCode.SUCCESS.toString().equals(bindProductCodes.getCode()) && bindProductCodes.getData() != null) {
            map.put("productCodeRelate", bindProductCodes.getData());
            return map;
        }
        return ObjectUtils.isEmpty(map) ? null : map;
    }

    @Override
    @Transactional
    public BaseResponse deleteModelRelate(ModelRelate modelRelate) {
        try {
            modelMapper.deleteModelRelateByRelateCode(modelRelate.getModelRelateCode());
            modelMapper.deleteModelRelateInstanceByRelateCode(modelRelate.getModelRelateCode());
            commonUtils.asyncClearModelDetailCache(modelRelate.getSourceModelCode());
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("deleteModelRelate", e);
            return BaseResponse.error(e);
        }
    }

    @Override
    public BaseResponse setFieldCollection(ModelFieldMapping modelFieldMapping) {
        try {
            if (modelFieldMapping.isCollection()) {
                String targetCode = modelFieldMapping.getTargetCode();
                Integer fieldSetKeyExist = fieldSetMapper.getFieldSetKeyExist(modelFieldMapping.getTargetCode());
                if (ObjectUtils.isEmpty(fieldSetKeyExist))
                    return BaseResponse.error(ResponseCode.FIELDSET_NOT_SET_KEY);
            }
            boolean res = modelMapper.setFieldCollection(modelFieldMapping) > 0;
            if (res)
                commonUtils.asyncClearModelCache(modelFieldMapping.getModelCode());
            return BaseResponse.ok(modelMapper.setFieldCollection(modelFieldMapping) > 0);
        } catch (Exception e) {
            log.error("setFieldCollection", e);
            return BaseResponse.error(e);
        }
    }

    @Override
    public List<Field> getFieldsByFieldGroup(String modelCode, String modelFieldGroupCode) {
        List<ModelFieldMapping> fieldMappings = modelMapper.getFieldsByFieldGroup(modelCode, modelFieldGroupCode);
        List<Field> fields = fieldMappings.stream()
                .filter(o -> "field".equals(o.getModelSettingType()))
                .map(o -> new Field(o.getField().getFieldCode(), o.getField().getFieldName()))
                .collect(Collectors.toList());
        fieldMappings.stream()
                .filter(o -> "fieldSet".equals(o.getModelSettingType()))
                .map(o -> o.getFieldSet())
                .forEach(o -> fields.addAll(o.getFieldList()));

        return fields.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ModelRelateInstance> getModelRelateInstanceByModel(ModelRelateInstance modelRelateInstance) {
        List<ModelRelateInstance> modelRelateInstances = modelMapper.getModelRelateInstanceByModel(modelRelateInstance);
        if (CollectionUtils.isEmpty(modelRelateInstances))
            return null;
        else {
            modelRelateInstances.stream().forEach(o -> o.setTargetJsonObject(commonUtils.getMrDetail(o.getTargetModelCode(),
                    o.getTargetFieldValue(), 0L)));
        }
        return modelRelateInstances;
    }

    @Override
    public BaseResponse getModelRelateInstanceBySourceInfo(List<Map<String, Object>> sourceConditions) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(sourceConditions, "sourceConditions");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<ModelRelateInstance> modelRelateInstances = modelMapper.selectModelRelateInstanceBySourceInfo(sourceConditions);
        if (CollectionUtils.isEmpty(modelRelateInstances))
            return BaseResponse.ok();
        else {
            modelRelateInstances.stream().forEach(o -> o.setTargetJsonObject(commonUtils.getMrDetail(o.getTargetModelCode(),
                    o.getTargetFieldValue(), 0L)));
        }
        return BaseResponse.ok(modelRelateInstances);
    }

    @Override
    public BaseResponse saveModelRelateInstance(List<ModelRelateInstance> modelRelateInstances) {
        try {
            if (CollectionUtils.isEmpty(modelRelateInstances))
                return BaseResponse.error(ResponseCode.RELATEINSTANCE_IS_NULL);
            if (StringUtils.isEmpty(modelRelateInstances.get(0).getSourceFieldValue()))
                return BaseResponse.error(ResponseCode.SOURCEFIELDVALUE_IS_NULL);
            modelRelateInstances.stream().forEach(modelRelateInstance -> {
                if (modelRelateInstance.getId() <= 0) {
                    modelRelateInstance.setId(SnowFlake.getInstance().newId());
                    modelRelateInstance.setTargetFieldValue(Long.toString(SnowFlake.getInstance().newId()));
                    modelMapper.saveModelRelateInstance(modelRelateInstance);
                }
                commonUtils.saveMrDetail(modelRelateInstance.getTargetModelCode(), modelRelateInstance.getTargetFieldValue(),
                        modelRelateInstance.getTargetJsonObject());
            });
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("saveModelRelateInstance", e);
            return BaseResponse.error(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveModelRelateInstanceComplete(Boolean needSaveBigDataPlatform,
                                                        List<ModelRelateInstance> modelRelateInstances) {
        //region 参数检查

        needSaveBigDataPlatform = BooleanUtils.toBoolean(needSaveBigDataPlatform);

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelRelateInstances, "modelRelateInstances");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //20241119:代码漏洞 getSourceModelCode()重复了，第二个 getSourceModelCode()改为 getTargetModelCode（）
        Optional<ModelRelateInstance> optEmptyModelRelateInstance = modelRelateInstances.stream().filter(x ->
                StringUtils.isBlank(x.getSourceModelCode()) || StringUtils.isBlank(x.getSourceFieldCode()) ||
                        StringUtils.isBlank(x.getSourceFieldValue()) || StringUtils.isBlank(x.getTargetModelCode()) ||
                        StringUtils.isBlank(x.getTargetFieldCode()) || StringUtils.isBlank(x.getTargetFieldValue())).findAny();
        if (optEmptyModelRelateInstance.isPresent()) {
            return BaseResponse.error(ResponseCode.MODEL_RELATE_INSTANCE_SOME_FIELD_IS_NULL);
        }

        //endregion

        final Boolean finalNeedSaveBigDataPlatform = needSaveBigDataPlatform;
        modelRelateInstances.stream().forEach(x -> {
            if (LongUtil.isEmpty(x.getId())) {
                x.setId(SnowFlake.getInstance().newId());
            }
        });
        modelMapper.batchInsertOrUpdateModelRelateInstance(modelRelateInstances);
        if (finalNeedSaveBigDataPlatform) {
            modelRelateInstances.stream().forEach(x -> {
                commonUtils.saveMrDetail(x.getTargetModelCode(), x.getTargetFieldValue(),
                        x.getTargetJsonObject());
            });
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse removeModelRelateInstance(List<Long> mriIdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(mriIdList, "mriIdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(modelMapper.deleteModelRelateInstanceByIdList(mriIdList) > 0);
    }

    @Override
    public BaseResponse batchRemoveModelRelateInstanceByCondition(List<ModelRelateInstance> modelRelateInstanceList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelRelateInstanceList, "modelRelateInstanceList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(modelMapper.batchDeleteModelRelateInstanceByCondition(modelRelateInstanceList));
    }

    @Override
    public BaseResponse addKeyToModel(String modelCode, long mappingId) {
        modelMapper.addKeyToModel(mappingId);
        commonUtils.asyncClearModelCache(modelCode);
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse removeKeyFromModel(String modelCode, long mappingId) {
        modelMapper.removeKeyFromModel(mappingId);
        commonUtils.asyncClearModelCache(modelCode);
        return BaseResponse.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse removeFieldFromModel(long mappingId, boolean checked) {
        ModelFieldMapping modelFieldMappingById = modelMapper.getModelFieldMappingById(mappingId);
        boolean res = modelMapper.removeFieldFromModel(mappingId) > 0;
        if (res) {
            commonUtils.asyncClearModelDetailCache(modelFieldMappingById.getModelCode());
        }
        if (!checked) {
            return BaseResponse.ok();
        }
        if ("field".equals(modelFieldMappingById.getModelSettingType())) {
            List<String> fieldCodes = Stream.of(modelFieldMappingById.getTargetCode()).collect(Collectors.toList());
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelCode", modelFieldMappingById.getModelCode());
            map.put("fieldCodes", fieldCodes);
            List<FieldSet> fieldSetsByFieldCodes = fieldSetMapper.getFieldSetsByFieldCodes(map);
            List<Model> modelsByFieldCodes = fieldSetMapper.getModelsByFieldCodes(map);
            if (CollectionUtils.isEmpty(fieldSetsByFieldCodes) && CollectionUtils.isEmpty(modelsByFieldCodes)) {
                fieldMapper.deleteFieldByFieldCodes(map);
                return BaseResponse.ok();
            }
            HashMap<String, Object> checkMap = new HashMap<>();
            String fieldSetNames = fieldSetsByFieldCodes.stream().map(o -> o.getFieldSetName()).collect(Collectors.joining(","));
            String modelNames = modelsByFieldCodes.stream().map(o -> o.getModelName()).collect(Collectors.joining(","));
            if (!StringUtils.isEmpty(fieldSetNames))
                checkMap.put("fieldSets", fieldSetNames);
            if (!StringUtils.isEmpty(modelNames))
                checkMap.put("models", modelNames);
            return BaseResponse.ok(checkMap);
        } else {
            List<String> fieldSetCodes = Stream.of(modelFieldMappingById.getTargetCode()).collect(Collectors.toList());
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelCode", modelFieldMappingById.getModelCode());
            map.put("fieldSetCodes", fieldSetCodes);
            List<Model> modelsByFieldSetCodes = modelMapper.getModelsByFieldSetCodes(map);
            if (CollectionUtils.isEmpty(modelsByFieldSetCodes)) {
                fieldSetMapper.deleteFieldSetByFieldSetCodes(map);
                fieldSetMapper.deleteFieldSetMappingByFieldSetCodes(map);
                return BaseResponse.ok();
            }
            String modelNames = modelsByFieldSetCodes.stream().map(o -> o.getModelName()).collect(Collectors.joining(","));
            HashMap<String, Object> checkMap = new HashMap<>();
            checkMap.put("models", modelNames);
            return BaseResponse.ok(checkMap);
        }
    }

    @Override
    public ModelRelate getModelRelate(String sourceModelCode, String targetModelCode) {
        return modelMapper.getModelRelateByModel(sourceModelCode, targetModelCode);
    }

    @Override
    public BaseResponse getModelNameMapByCodeList(List<String> codeList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(codeList, "codeList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSid();
        if (LongUtil.isEmpty(sid)) {
            sid = defaultSid;
        }

        Map<String, String> codeNameMap;
        List<Map<String, String>> mapList = modelMapper.selectModelNameByCodeList(sid, codeList);
        if (CollectionUtils.isEmpty(mapList)) {
            codeNameMap = new HashMap<>(0);
        } else {
            codeNameMap = mapList.stream().filter(x -> x != null)
                    .collect(Collectors.toMap(x -> ObjectUtils.nullSafeToString(x.get("modelCode")),
                            x -> ObjectUtils.nullSafeToString(x.get("modelName")), (x, y) -> y));
        }
        return BaseResponse.ok(codeNameMap);
    }

    @Override
    public List<Field> getModelDmpField(String modelCode) {
        return modelMapper.getModelDmpField(modelCode);
    }

    @Override
    public BaseResponse getModelDetailFormulaList(List<String> modelCodeList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(modelCodeList, "modelCodeList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        return BaseResponse.ok(modelCodeList.stream().filter(StringUtils::isNotBlank).distinct().map(x -> {
            Map<String, Object> resultMap = getModelDetail(x);
            resultMap.put("modelCode", x);
            return resultMap;
        }).collect(Collectors.toMap(x -> Objects.toString(x.get("modelCode"), ""), x -> {
            x.remove("modelCode");
            ModelDetail md = MapUtil.<String, Object, ModelDetail>getTByMap(x, "field").orElse(null);
            if (Objects.isNull(md)) {
                return new HashMap<>(0);
            }
            List<ModelFieldGroup> mfgList = md.getModelFieldGroupList();
            if (CollectionUtils.isEmpty(mfgList)) {
                return new HashMap<>(0);
            }
            return mfgList.stream().collect(Collectors.toMap(y ->
                            Objects.toString(y.getModelFieldGroupCode(), ""),
                    y -> {
                        List<ModelFieldMapping> mfmList = y.getModelFieldMappingList();
                        if (CollectionUtils.isEmpty(mfmList)) {
                            return new HashMap<>(0);
                        }
                        Map<String, Object> resultMap = new HashMap<>();
                        mfmList.forEach(z -> {
                            //字段
                            setFieldFormulaByMfm(z, resultMap);
                            //字段集
                            setFieldSetFieldFormulaByMfm(z, resultMap);
                        });
                        return resultMap;
                    }));
        }, (x, y) -> y)));
    }

    private void setFieldFormulaByMfm(ModelFieldMapping mfm, Map<String, Object> resultMap) {
        if (Objects.isNull(mfm)) {
            return;
        }
        setFieldFormula(mfm.getField(), resultMap);
    }

    private void setFieldFormula(Field field, Map<String, Object> resultMap) {
        if (Objects.isNull(field)) {
            return;
        }
        String fieldCode = field.getFieldCode();
        if (StringUtils.isBlank(fieldCode)) {
            return;
        }
        getFormulaByField(field).ifPresent(x -> resultMap.put(fieldCode, x));
    }

    private Optional<String> getFormulaByField(Field field) {
        if (Objects.isNull(field)) {
            return Optional.empty();
        }
        return getFormulaByMff(field.getModelFieldFomula());
    }

    private Optional<String> getFormulaByMff(ModelFieldFomula mff) {
        if (Objects.isNull(mff)) {
            return Optional.empty();
        }
        String formula = mff.getFormula();
        if (StringUtils.isBlank(formula)) {
            return Optional.empty();
        }
        return Optional.of(formula);
    }

    private Optional<String> getFormulaByFieldSet(FieldSet fieldSet) {
        if (Objects.isNull(fieldSet)) {
            return Optional.empty();
        }
        return getFormulaByMff(fieldSet.getModelFieldFomula());
    }

    private void setFieldSetFieldFormulaByMfm(ModelFieldMapping mfm, Map<String, Object> resultMap) {
        if (Objects.isNull(mfm)) {
            return;
        }
        FieldSet fieldSet = mfm.getFieldSet();
        if (Objects.isNull(fieldSet)) {
            return;
        }
        //字段集本身的公式
        getFormulaByFieldSet(fieldSet).ifPresent(x -> resultMap.put("", x));
        String fieldSetCode = fieldSet.getFieldSetCode();
        if (StringUtils.isBlank(fieldSetCode)) {
            return;
        }
        List<Field> fieldList = fieldSet.getFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }
        Map<String, Object> fieldMap = new HashMap<>(fieldList.size());
        fieldList.forEach(x -> {
            //字段
            setFieldFormula(x, fieldMap);
        });
        if (CollectionUtil.isNotEmpty(fieldMap)) {
            resultMap.put(fieldSetCode, fieldMap);
        }
    }

    @Override
    public BaseResponse getModelCount() {
        Map<String, Object> map = new HashMap<>(1);
        map.put("sid", RequestUtil.getHeaderSidOrDefault(defaultSid));
        return BaseResponse.ok(modelMapper.selectModelCountByMap(map));
    }

    @Override
    public BaseResponse deleteModelGroupField(long id) {
        BaseResponse res = new BaseResponse();
        try {
            boolean result = modelMapper.deleteModelGroupField(id);
            if (result) {
                res.setCode(ResponseCode.SUCCESS.toString());
            } else {
                res.setCode(ResponseCode.DELETE_FAILD.toString());
                res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
            }
        } catch (Exception e) {
            log.error("deleteModelGroupField error", e);
            res.setCode(ResponseCode.DELETE_FAILD.toString());
            res.setErrMsg(ResponseCode.DELETE_FAILD.getMsg());
        }
        return res;
    }

    @Override
    public BaseResponse addModelGroupField(List<ModelGroupField> poList) {
        BaseResponse res = new BaseResponse();
        try {
            for (ModelGroupField po : poList) {
                po.setSid(RequestUtil.getHeaderSid());
                po.setAppCode(RequestUtil.getHeaderAppCode());
                po.setEid(RequestUtil.getHeaderEid());
                po.setId(SnowFlake.getInstance().newId());
            }
            int index = modelMapper.batchInsertModelGroupField(poList);
            return BaseResponse.ok(index);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }


    @Override
    public int updateGlobalModel(List<String> modelCodeList) {
        Map<String, Object> params = new HashMap<>();
        params.put("modelCodeList", modelCodeList);
        params.put("globalSid", 241199971893824L);
        params.put("globalEid", 99990000L);
        params.put("globalAppCode", GLOBAL_APP_CODE);
        return modelMapper.updateGlobalModel(params);
    }

    @Override
    public Map<String, ModelEtlInfo> getModelEtlInfoByCode(List<String> modelCodeList) {
        HashMap<String, ModelJson> modelJson = modelCache.getModelJson(modelCodeList);
        if (MapUtils.isNotEmpty(modelJson)) {
            Map<String, ModelEtlInfo> result = new HashMap<>(modelCodeList.size());
            List<EdgeEtlEngine> edgeEtlEngineList = edgeEtlService.getByModelCodeList(modelCodeList);
            Map<String, List<EdgeEtlEngine>> modelCodeEtlMap = edgeEtlEngineList.stream().collect(Collectors.groupingBy(EdgeEtlEngine::getModelCode));
            for (Map.Entry<String, ModelJson> entry : modelJson.entrySet()) {
                ModelEtlInfo modelEtlInfo = new ModelEtlInfo();
                modelEtlInfo.setModelJson(entry.getValue());
                List<EdgeEtlEngine> edgeEtlEngines = modelCodeEtlMap.get(entry.getKey());
                modelEtlInfo.setEdgeEtlEngineList(edgeEtlEngines);
                result.put(entry.getKey(), modelEtlInfo);
            }
            return result;
        }
        return new HashMap<>(0);
    }

    @Override
    public Map<String, Object> getModelVersionByModelCode(List<String> modelCodeList) {
        List<Map<String, Object>> mapList = modelMapper.getModelVersionByModelCode(modelCodeList);
        return mapList.stream().collect(Collectors.toMap(x -> x.get("modelCode").toString(),
                x -> x.get("modelVersion")
        ));
    }

    @Override
    public BaseResponse moveModelFieldSort(List<ModelFieldMapping> modelFieldMappingList) {
        if (CollectionUtils.isEmpty(modelFieldMappingList)) {
            return BaseResponse.error(ResponseCode.FIELD_IS_NULL);
        }

        Integer isSuccess = modelMapper.moveModelFieldSort(modelFieldMappingList);
        return BaseResponse.ok(isSuccess > 0);
    }

    public String autoCreate(DataSyncModelReq req, boolean isCreate) {
        Model model = req.buildModel();
        //校验，模型编码唯一性
        if (isCreate) {
            if (modelMapper.findModelCode(model.getModelCode()) != null) {
                throw new RuntimeException(ResponseCode.MODELCODE_IS_EXIST.getMsg());
            }
        }
        // 1. 创建cmdb_field
        List<String> columnNameList = req.buildColumnNameList();
        List<Field> fieldListExist = fieldMapper.getFieldByCodes(columnNameList);
        List<String> fieldNameListNeedAdd = columnNameList.stream().filter(x -> fieldListExist.stream().noneMatch(y -> y.getFieldCode().equals(x))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(fieldNameListNeedAdd)) {
            List<Field> fields = req.buildFieldList(fieldNameListNeedAdd);
            fieldMapper.batchAddField(fields);
            log.info("create cmdb_field success");
        }
        // 2. 创建cmdb_model
        addModel(model);
        log.info("create cmdb_model success");
        // 3. 关联cmdb_model和cmdb_field
        addModelFieldGroup(model.getModelCode(), DATA_CONTENT);
        log.info("create cmdb_model_field_group success");
        addField2Model(model.getModelCode(), columnNameList);
        log.info("create cmdb_model_field_mapping success");
        // 4. 保存edge_etl_engine
        EdgeEtlEngine edgeEtlEngine = req.buildEdgeEtlEngine();
        edgeEtlService.save(edgeEtlEngine);
        log.info("save edge_etl_engine success");
        // 5. 清除缓存
        commonUtils.asyncRun(() -> modelCache.clearCurrentModel(model.getModelCode()));
        return model.getModelCode();
    }

    public void addModelFieldGroup(String modelCode, String fieldGroupCode) {
        ModelFieldGroup modelFieldGroup = new ModelFieldGroup();
        modelFieldGroup.setId(SnowFlake.getInstance().newId());
        modelFieldGroup.setModelCode(modelCode);
        modelFieldGroup.setModelFieldGroupCode(fieldGroupCode);
        modelFieldGroup.setModelFieldGroupName(fieldGroupCode);
        modelFieldGroup.setFold(true);
        modelFieldGroup.setSid(RequestUtil.getHeaderSid());
        modelMapper.addModelFieldGroup(modelFieldGroup);
    }

    public void addField2Model(String modelCode, List<String> fieldCodeList) {
        long sid = RequestUtil.getHeaderSid();
        List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();
        //查该模型现有的最大sort
        int sort = modelMapper.selectMaxSort(sid, modelCode);
        for (String fieldCode : fieldCodeList) {
            ModelFieldMapping mapping = new ModelFieldMapping();
            mapping.setId(SnowFlake.getInstance().newId());
            mapping.setSid(sid);
            mapping.setEid(RequestUtil.getHeaderEid());
            mapping.setModelCode(modelCode);
            mapping.setModelFieldGroupCode(DATA_CONTENT);
            mapping.setModelSettingType(ModelSettingType.field.toString());
            mapping.setTargetCode(fieldCode);
            mapping.setHide(true);
            mapping.setSort(++sort);
            modelFieldMappingList.add(mapping);
        }
        modelMapper.batchAddModelFieldMapping(modelFieldMappingList);
    }


    /**
     * 10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
     * 檢索不與特定產品型號綁定的產品應用程式清單。
     *
     * @param spId 與產品應用程式關聯的服務提供者 ID。
     * @param modelCode 產品型號代碼。
     * @param filter 用於根據特定條件過濾結果的字串。
     * @param modelRelateCodes 用於細化搜尋條件的模型相關代碼清單。
     * @return 映射列表，其中每個映射代表一個非綁定產品應用程式及其相關詳細資訊。
     */
    public List<Map<String, Object>> getNotBindProductApps(
            long spId, String modelCode, String filter, List<String> modelRelateCodes) {
        Map<String, Object> map = new HashMap<>();
        map.put("spId", spId);
        map.put("modelCode", modelCode);
        map.put("filter", filter);
        map.put("modelRelateCodes", modelRelateCodes);
        return modelMapper.getNotBindProductApps(map);
    }


    @Override
    @Transactional
    public ModelAndEtlRespDTO saveModelAndEtl(ModelAndEtlReqDTO reqDTO) {
        ModelAndEtlRespDTO respDTO = new ModelAndEtlRespDTO();
        // 1. 新增字段
        insertModelField(reqDTO);

        ModelAndEtlReqDTO.ModelDTO modelDTO = reqDTO.getModel();
        IEtlStoreService etlStoreService = etlStoreServiceFactory.getEtlStoreService(reqDTO.getEtl().getSinkType());
        EtlEngineSaveReqDTO etl = reqDTO.getEtl();
        // 2. 新增模型
        // 更新模型时，需要更新其他etl的json字段
        boolean updateOtherEtlJson = false;
        List<ModelFieldGroup> modelFieldGroupList;
        if (modelDTO.getId() == null || modelDTO.getId() == 0) {
            checkModel(reqDTO);
            // 2.1 新增模型字段的分类和分类下字段
            modelFieldGroupList = buildModelFieldGroup(reqDTO);
            modelMapper.batchAddModelFieldGroup(modelFieldGroupList);
            // 2.2 新增模型字段分组下的字段mapping
            List<ModelFieldMapping> modelFieldMappingList = modelFieldGroupList.stream()
                    .map(ModelFieldGroup::getModelFieldMappingList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            modelMapper.batchAddModelFieldMapping(modelFieldMappingList);
            // 获取模型json
            ModelJson modelJson = modelCache.getModelJsonOne(modelFieldGroupList, modelFieldMappingList, false, false);
            String modelJsonStr = JSONObject.toJSONString(modelJson, SerializerFeature.WriteMapNullValue);
            String md5 = Md5Utils.getMd5(modelJsonStr);
            // 2.3 新增模型
            Model model = reqDTO.getModel().buildModel();
            model.setModelVersion(md5);
            modelMapper.addModel(model);
            respDTO.setModelId(model.getId());
            // 2.4新增etl
            etl.setEtlJson(modelJsonStr);
        // 3. 更新模型
        } else {
            // 3.1 查询已有的模型
            ModelDetail modelDetail = modelMapper.getModelDetail(reqDTO.getModel().getModelCode());
            if (modelDetail == null) {
                return null;
            }
            // 3.2 获取已有的字段分组和字段关系列表
            modelFieldGroupList = modelDetail.getModelFieldGroupList();
            ModelFieldGroup dataContentPoGroup = modelFieldGroupList.stream().filter(modelFieldGroup -> modelFieldGroup.getModelFieldGroupCode().equals(DATA_CONTENT)).findFirst().get();
            List<ModelFieldMapping> modelFieldMappingPoList = dataContentPoGroup.getModelFieldMappingList();
            // 3.3 查询是否有新增模型字段关系
            List<ModelAndEtlReqDTO.ModelFieldDTO> newFieldList = new ArrayList<>();
            List<ModelAndEtlReqDTO.ModelFieldDTO> modelFieldList = reqDTO.getModel().getModelFieldList();
            for (ModelAndEtlReqDTO.ModelFieldDTO modelFieldDTO : modelFieldList) {
                if (modelFieldMappingPoList.stream().noneMatch(mapping -> modelFieldDTO.getFieldCode().equals(mapping.getTargetCode()))) {
                    newFieldList.add(modelFieldDTO);
                }
            }
            // 3.4 新增模型和字段关系
            if (!CollectionUtils.isEmpty(newFieldList)) {
                List<ModelFieldMapping> newContentFieldMappingList = buildDataContentFieldMapping(reqDTO.getModel().getModelCode(), newFieldList);
                dataContentPoGroup.getModelFieldMappingList().addAll(newContentFieldMappingList);
                modelMapper.batchAddModelFieldMapping(newContentFieldMappingList);
            }
            // 3.5 获取模型json
            ModelJson modelJson = modelCache.getModelJsonOne(modelFieldGroupList, false, false);
            String modelJsonStr = JSONObject.toJSONString(modelJson, SerializerFeature.WriteMapNullValue);
            // 3.6 更新模型版本号
            if (!CollectionUtils.isEmpty(newFieldList)) {
                String md5 = Md5Utils.getMd5(modelJsonStr);
                Model newModel = new Model();
                newModel.setModelCode(reqDTO.getModel().getModelCode());
                newModel.setModelVersion(md5);
                modelMapper.updateModelByCode(newModel);
            }
            respDTO.setModelId(modelDTO.getId());
            // 3.7更新etl
            if (StringUtils.isBlank(etl.getEtlJson())) {
                etl.setEtlJson(modelJsonStr);
            }
            // 3.9 清空缓存模型
            modelCache.clearCurrentModel(reqDTO.getModel().getModelCode(), false);
            updateOtherEtlJson = true;
        }
        // etl 存储或者更新
        if (StringUtils.isBlank(etl.getAppCode())) {
            etl.setAppCode(RequestUtil.getHeaderAppCode());
        }
        if (SinkType.HBASE.getName().equalsIgnoreCase(etl.getSinkType()) || SinkType.ESHBASE.getName().equalsIgnoreCase(etl.getSinkType())) {
            etl.setSinkFieldsJson(HbaseStoreService.etlFieldJson);
            if (StringUtils.isNoneBlank(etl.getSinkTableTtlField()) && StringUtils.isBlank(etl.getSinkTableTtl())) {
                etl.setSinkTableTtl(HbaseStoreService.DEFAULT_TTL.toString());
            }
        } else if (SinkType.STARROCKS.getName().equalsIgnoreCase(etl.getSinkType())) {
            String newSinkFieldsJson = etlService.sortEtlField(etl);
            etl.setSinkFieldsJson(newSinkFieldsJson);
            if (StringUtils.isNoneBlank(etl.getSinkTableTtlField()) && StringUtils.isBlank(etl.getSinkTableTtl())) {
                etl.setSinkTableTtl(StarRocksStoreService.DEFAULT_TTL.toString());
                etl.setSinkTableTtlUnit(StarRocksStoreService.DEFAULT_TTL_UNIT);
            }
        }
        // 4. 开启etl状态
        boolean isCreateEtl = etl.getId() == 0;
        // 入库的数据
        List<EtlModelField> etlModelFieldsPo = null;
        if (!isCreateEtl) {
            EtlEngine etlPo = etlMapper.getById(etl.getId());
            if (etlPo != null) {
                String sinkFieldsJson = etlPo.getSinkFieldsJson();
                if (StringUtils.isNoneBlank(sinkFieldsJson)) {
                    etlModelFieldsPo = JSONArray.parseArray(sinkFieldsJson, EtlModelField.class);
                }
            }
        }
        etl.setSinkEnable(true);
        EtlEngineRespDTO tableInfo = etlService.getTableInfo(etl.getSinkType(), etl.getSchemaName(), etl.getSinkName(), etl.getStorageSettingId());
        // 4.1 不是hbase的时候。则把basicInfo中的字段加入存储定义中。
        if (!SinkType.HBASE.getName().equalsIgnoreCase(etl.getSinkType()) && !SinkType.ESHBASE.getName().equalsIgnoreCase(etl.getSinkType()) ) {
            if (tableInfo != null) {
                List<Map<String, Object>> tableFieldCodeMapList = tableInfo.getTableInfo();
                buildEtlSinkFieldsJson(modelFieldGroupList, etl, tableFieldCodeMapList, etlModelFieldsPo);
            } else {
                buildEtlSinkFieldsJson(modelFieldGroupList, etl, null, etlModelFieldsPo);
            }
        }

        BaseResponse baseResp = etlService.saveEtl(etl);
        if (!baseResp.checkIsSuccess()) {
            throw new BizException(baseResp);
        }
        // 如果需要更新其他etl的json，则更新etlJson。
        if (updateOtherEtlJson) {
            etlMapper.updateEtlJson(etl.getEtlJson(),null, modelDTO.getModelCode());
        }
        // 4.2 创建存储
        Long etlPoId = (Long) baseResp.getData();
        etl.setId(etlPoId);
        if (Objects.isNull(tableInfo)) {
            //创建存储
            BaseResponse createSinkResp = etlStoreService.createSink(etl);
            if (!createSinkResp.checkIsSuccess()) {
                throw new BizException(createSinkResp);
            }
        } else {
            // 更新存储
            etlStoreService.updateSink(etl);
        }
        respDTO.setEtlId(etlPoId);
        return respDTO;
    }

    /**
     * 设定etl的sinkFieldsJson字段，如果modelFieldGroupList中有基础信息分组，则将基础信息分组的字段加入到etl中。
     *
     * @param modelFieldGroupList
     * @param etl
     * @param tableFieldCodeMapList 表字段信息。如果存在表，则需要在modelFieldGroupList基础信息分组中过滤表中有的字段
     * @param etlModelFieldsPo 数据库中的旧的sinkFieldsJson的数据
     */
    private void buildEtlSinkFieldsJson(List<ModelFieldGroup> modelFieldGroupList, EtlEngineSaveReqDTO etl, List<Map<String, Object>> tableFieldCodeMapList, List<EtlModelField> etlModelFieldsPo) {
        Optional<ModelFieldGroup> first = modelFieldGroupList.stream().filter(modelFieldGroup -> modelFieldGroup.getModelFieldGroupCode().equals(BASE_GROUP_CODE)).findFirst();
        if (first.isPresent()) {
            ModelFieldGroup modelFieldGroup = first.get();
            List<ModelFieldMapping> modelFieldMappingList = modelFieldGroup.getModelFieldMappingList();
            if (Objects.isNull(modelFieldMappingList)) {
                return;
            }
            String sinkFieldsJson = etl.getSinkFieldsJson();
            if (Objects.isNull(modelFieldMappingList)) {
                return;
            }
            List<EtlModelField> etlModelFields = JSONArray.parseArray(sinkFieldsJson, EtlModelField.class);
            if (Objects.isNull(etlModelFields)) {
                return;
            }
            // 如果tableFieldCodeMapList有值，表示需要和表字段进行取交集
            if (!CollectionUtils.isEmpty(tableFieldCodeMapList)) {
                // modelFieldMappingList 中的字段要和目标表字段进行取交集tableFieldCodeMapList
                modelFieldMappingList = modelFieldMappingList.stream().filter(modelFieldMapping -> tableFieldCodeMapList.stream().anyMatch(tableFieldCodeMap -> tableFieldCodeMap.get("fieldCode").equals(modelFieldMapping.getTargetCode()))).collect(Collectors.toList());
            }
            // 如果etlModelFieldsPo有值，表示需要和数据库中的旧的sinkFieldsJson的数据进行取并集
            if (!CollectionUtils.isEmpty(etlModelFieldsPo)) {
                etlModelFieldsPo.stream().filter(etlModelFieldPo -> etlModelFields.stream().noneMatch(etlModelField -> etlModelFieldPo.getFieldCode().equals(etlModelField.getFieldCode()))).forEach(etlModelFieldPo -> {
                    etlModelFields.add(etlModelFieldPo);
                });
            }
            // 如果modelFieldMappingList中的字段不在etlModelFields中，则新增
            modelFieldMappingList.stream().filter(modelFieldMapping -> etlModelFields.stream().noneMatch(etlModelField -> modelFieldMapping.getTargetCode().equals(etlModelField.getFieldCode()))).forEach(modelFieldMapping -> {
                String targetCode = modelFieldMapping.getTargetCode();
                EtlModelField etlModelField = new EtlModelField();
                etlModelField.setFieldCode(targetCode);
                etlModelField.setFieldType("VARCHAR");
                etlModelField.setValuePath(BASE_GROUP_CODE + DOT + targetCode);
                etlModelFields.add(etlModelField);
            });
            String newSinkFieldsJson = JSONArray.toJSONString(etlModelFields);
            etl.setSinkFieldsJson(newSinkFieldsJson);
        }
    }

    /**
     * 构造基础数据分类和数据内容对应的字段分类
     * @param reqDTO
     * @return
     */
    private List<ModelFieldGroup> buildModelFieldGroup(ModelAndEtlReqDTO reqDTO) {
        List<ModelFieldGroup> list = new ArrayList<>();
        String modelCode = reqDTO.getModel().getModelCode();

        ModelFieldGroup basicInfoFieldGroup = buildBasicInfoGroup(modelCode);
        ModelFieldGroup dataContentFieldGroup = buildDataContentGroup(modelCode);
        // 新增模型字段分组下的字段
        List<ModelFieldMapping> basicInfoFieldMapping = buildBasicInfoFieldMapping(reqDTO);
        List<ModelFieldMapping> contentFieldMapping = buildDataContentFieldMapping(reqDTO);
        basicInfoFieldGroup.setModelFieldMappingList(basicInfoFieldMapping);
        dataContentFieldGroup.setModelFieldMappingList(contentFieldMapping);
        list.add(basicInfoFieldGroup);
        list.add(dataContentFieldGroup);
        return list;
    }

    private List<ModelFieldMapping> buildBasicInfoFieldMapping(ModelAndEtlReqDTO reqDTO) {
        ModelAndEtlReqDTO.ModelDTO modelDTO = reqDTO.getModel();
        long sid = RequestUtil.getHeaderSid();
        long eid = RequestUtil.getHeaderEid();
        String modelGroupCode = modelDTO.getModelGroupCode();
        String modelCode = modelDTO.getModelCode();
        // 获取分组下的默认字段
        List<ModelGroupField> modelGroupFields = modelMapper.getModelGroupField(modelGroupCode);
        // 新增字段分类下的字段
        List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();
        for (ModelGroupField modelGroupField : modelGroupFields) {
            ModelFieldMapping modelFieldMapping = new ModelFieldMapping();
            modelFieldMapping.setId(SnowFlake.getInstance().newId());
            modelFieldMapping.setModelCode(modelCode);
            modelFieldMapping.setModelFieldGroupCode(BASE_GROUP_CODE);
            modelFieldMapping.setModelSettingType(ModelSettingType.field.name());
            modelFieldMapping.setTargetCode(modelGroupField.getFieldCode());
            modelFieldMapping.setCollection(false);
            modelFieldMapping.setHide(false);
            modelFieldMapping.setShowTitle(true);
            modelFieldMapping.setSid(sid);
            modelFieldMapping.setEid(eid);
            modelFieldMapping.setSort(modelGroupField.getSort());
            modelFieldMapping.setKey(modelGroupField.isKey());
            modelFieldMapping.setField(modelGroupField.getField());
            modelFieldMappingList.add(modelFieldMapping);
        }
        return modelFieldMappingList;
    }

    private List<ModelFieldMapping> buildDataContentFieldMapping(ModelAndEtlReqDTO reqDTO) {
        List<ModelAndEtlReqDTO.ModelFieldDTO> modelFieldDTOS = reqDTO.getModel().getModelFieldList();
        return buildDataContentFieldMapping(reqDTO.getModel().getModelCode(), modelFieldDTOS);
    }

    private List<ModelFieldMapping> buildDataContentFieldMapping(String modelCode, List<ModelAndEtlReqDTO.ModelFieldDTO> modelFieldDTOS) {
        long sid = RequestUtil.getHeaderSid();
        long eid = RequestUtil.getHeaderEid();
        //查该模型现有的最大sort
        int sort = modelMapper.selectMaxSort(sid, modelCode);
        // 新增字段分类下的字段
        List<ModelFieldMapping> modelFieldMappingList = new ArrayList<>();
        for (ModelAndEtlReqDTO.ModelFieldDTO modelFieldDTO : modelFieldDTOS) {
            ModelFieldMapping modelFieldMapping = new ModelFieldMapping();
            modelFieldMapping.setId(SnowFlake.getInstance().newId());
            modelFieldMapping.setModelCode(modelCode);
            modelFieldMapping.setModelFieldGroupCode(DATA_CONTENT);
            modelFieldMapping.setModelSettingType(ModelSettingType.field.name());
            modelFieldMapping.setTargetCode(modelFieldDTO.getFieldCode());
            modelFieldMapping.setCollection(false);
            modelFieldMapping.setHide(false);
            modelFieldMapping.setShowTitle(true);
            modelFieldMapping.setSid(sid);
            modelFieldMapping.setEid(eid);
            modelFieldMapping.setSort(++sort);
            modelFieldMapping.setKey(false);
            Field field = new Field();
            modelFieldMapping.setField(field);
            modelFieldMappingList.add(modelFieldMapping);
        }
        return modelFieldMappingList;
    }


    private ModelFieldGroup buildBasicInfoGroup(String modelCode) {
        long sid = RequestUtil.getHeaderSid();
        ModelFieldGroup modelFieldGroup = new ModelFieldGroup();
        modelFieldGroup.setSid(sid);
        modelFieldGroup.setModelCode(modelCode);
        modelFieldGroup.setModelFieldGroupCode(BASE_GROUP_CODE);
        String groupName = messageUtils.get(BASE_GROUP_CODE, cmdbDefaultLanguage);
        modelFieldGroup.setModelFieldGroupName(groupName);
        modelFieldGroup.setFold(true);//默认基本资讯是展开的
        modelFieldGroup.setId(SnowFlake.getInstance().newId());
        return modelFieldGroup;
    }

    private ModelFieldGroup buildDataContentGroup(String modelCode) {
        long sid = RequestUtil.getHeaderSid();
        ModelFieldGroup modelFieldGroup = new ModelFieldGroup();
        modelFieldGroup.setSid(sid);
        modelFieldGroup.setModelCode(modelCode);
        modelFieldGroup.setModelFieldGroupCode(DATA_CONTENT);
        modelFieldGroup.setModelFieldGroupName(messageUtils.get(DATA_CONTENT, cmdbDefaultLanguage));
        modelFieldGroup.setFold(true);
        modelFieldGroup.setId(SnowFlake.getInstance().newId());
        return modelFieldGroup;
    }

    private void checkModel(ModelAndEtlReqDTO reqDTO) {
        String modelCode = reqDTO.getModel().getModelCode();
        if (modelMapper.findModelCode(modelCode) != null) {
            throw new BizException(ResponseCode.MODELCODE_IS_EXIST, modelCode);
        }
    }

    private int insertModelField(ModelAndEtlReqDTO reqDTO) {
        List<ModelAndEtlReqDTO.ModelFieldDTO> modelFieldDTOS = reqDTO.getModel().getModelFieldList();
        if (!CollectionUtils.isEmpty(modelFieldDTOS)) {
            List<String> fieldCodeStrList = modelFieldDTOS.stream().map(fieldDTO -> fieldDTO.getFieldCode()).collect(Collectors.toList());
            // 查询字段是否存在
            List<Field> fieldCodeList = fieldMapper.findFieldCodeList(fieldCodeStrList);
            // 过滤出新字段列表，即新增字段列表
            List<ModelAndEtlReqDTO.ModelFieldDTO> newFieldList = modelFieldDTOS.stream().filter(modelFieldDTO -> fieldCodeList.stream().noneMatch(field -> field.getFieldCode().equals(modelFieldDTO.getFieldCode()))).collect(Collectors.toList());
            // 构造Field对象列表，并批量插入字段表
            if (!CollectionUtils.isEmpty(newFieldList)) {
                List<Field> fieldList = reqDTO.getModel().buildFieldList(newFieldList);
                if (!CollectionUtils.isEmpty(fieldList)) {
                    return fieldMapper.batchAddField(fieldList);
                }
            }
        }
        return 0;
    }

    @Override
    public BaseResponse getModelCountByRelateApp(List<String> appCodeList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(appCodeList, "appCodeList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Map<String, Object> map = new HashMap<>(4);
        map.put("appCodeList", appCodeList);
        map.put("specialColumns", new String[]{"cm.appCode", "COUNT(*) AS total"});
        map.put("groupBy", "cm.appCode");
        return BaseResponse.ok(modelMapper.selectModelMapByMap(map));
    }

}



