package com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue;

import com.digiwin.escloud.issueservice.t.integration.fuguan.model.IssueKey;
import com.digiwin.escloud.issueservice.t.integration.fuguan.model.IssuePostRecord;
import com.digiwin.escloud.issueservice.t.integration.fuguan.model.constant.PostStatus;
import com.digiwin.escloud.issueservice.t.integration.fuguan.pipeline.IssueMqConsumePipeline;
import com.digiwin.escloud.issueservice.t.integration.fuguan.pipeline.PostRecorder;
import com.digiwin.escloud.issueservice.t.integration.fuguan.rule.issue.Prerequisite;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IssueFGProducer {
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    private IssueMqConsumePipeline fgPipeline;
    @Autowired
    private PostRecorder postRecorder;
    @Autowired
    private Prerequisite prerequisite;

    /**
     * 发送同步187服务器的MQ消息, 请确保案件处理逻辑的事务已经提交，否则mq消费者查不到变更的数据
     * 2023-09-12 调整：去掉这个入参isPersonalCase，因都是非个案了
     * @param issueId issueId
     * @param processSequenceNo 案件当前操作项次
     * @throws RuntimeException 任何运行时错误，包括调用其他微服务失败，发送MQ消息失败
     */
    public void send(long issueId, int processSequenceNo){
        try {
            IssueKey key = new IssueKey(issueId, processSequenceNo);
            if (prerequisite.needToSend(issueId)) {
                produceMsg(key);
            } else {
                log.info("抛转187:非需要同步的案件, issueId:{}", issueId);
            }
        } catch (DataAccessException e){
            log.error("抛转187异常,issueId:{}", issueId, e);
            postRecorder.record(new IssuePostRecord(issueId, processSequenceNo, PostStatus.QUERY_HISTORY_ERROR.getValue(), e));
        } catch (AmqpException k){
            log.error("抛转187异常,issueId:{}", issueId, k);
            postRecorder.record(new IssuePostRecord(issueId, processSequenceNo, PostStatus.SEND_MQ_FAIL.getValue(), k));
        }
    }

    private void produceMsg(IssueKey issueKey) throws AmqpException{
        try {
            amqpTemplate.convertAndSend(IssueFGMQConstant.exchange, IssueFGMQConstant.routingkey, issueKey);
            log.info("往187队列发起成功: issueId:{}", issueKey.getIssueId());
        } catch (AmqpException e){
            log.info("发送同步服管187案件消息失败:", e);
            throw new RuntimeException("发送MQ消息失败:同步案件信息到187系统.", e);
        } catch (IllegalArgumentException e) {
            log.info("发送同步服管187案件消息失败:", e);
        }
        log.info("FGProducer Send MQ msg: issueId:{}, sequenceNum:{}", issueKey.getIssueId(), issueKey.getProcessSequenceNo());
    }
}
