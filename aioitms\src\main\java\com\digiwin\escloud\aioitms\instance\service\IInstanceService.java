package com.digiwin.escloud.aioitms.instance.service;

import com.digiwin.escloud.aiobasic.edr.model.edr.EdrDevice;
import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.instance.model.*;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.aioitms.model.instance.AiopsEDRInstance;
import com.digiwin.escloud.aioitms.instance.model.AiopsEaiCloudInstance;
import com.digiwin.escloud.aioitms.model.instance.AiopsInstanceFixParam;
import com.digiwin.escloud.aioitms.model.instance.AiopsSmartMeterInstance;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 实例服务接口
 */
public interface IInstanceService {

    /**
     * 依据运维授权状态与运维项目上下文列表查询已授权的运维实例列表
     * @param aiopsAuthStatus 运维授权状态
     * @param aicList 运维项目上下文列表
     * @return 運維實例列表
     */
    List<AiopsInstance> getAiopsInstanceExistByAicList(AiopsAuthStatus aiopsAuthStatus,
                                                       Collection<AiopsItemContext> aicList);

    /**
     * 获取运维项目
     * @param aiopsItemCollection 运维项目代号列表
     * @return 回覆对象
     */
    BaseResponse getAiopsItemList(Collection<String> aiopsItemCollection);

    BaseResponse getAiopsItemList();

    /**
     * 依据运维项目上下文列表创建运维实例
     * @param eid 租户Id
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse createAiopsInstanceByAicList(Long eid, List<AiopsItemContext> aicList);

    /**
     * 依据运维项目上下文列表批量保存运维实例
     * @param eid 租户Id
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse batchSaveAiopsInstanceByAicList(Long eid, Collection<AiopsItemContext> aicList);

    /**
     * 批量保存运维实例
     * @param aiopsInstanceList 运维实例列表
     * @return 回覆对象
     */
    BaseResponse batchSaveAiopsInstance(Collection<AiopsInstance> aiopsInstanceList);

    /**
     * 依据运维项目上下文异动运维实例授权状态
     * @param aiopsAuthStatus 运维授权状态
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    BaseResponse modifyAiopsInstanceAuthStatusByAicList(AiopsAuthStatus aiopsAuthStatus,
                                                        Collection<AiopsItemContext> aicList);

    /**
     * 依据租户模组合约明细Id查询已授权的实例数量
     * @param tmcdId 租户模组合约明细Id
     * @param isContainHoldAuth 是否包含占用授权标的
     * @return 回覆对象
     */
    BaseResponse getAuthedCountByTmcdId(Long tmcdId, Boolean isContainHoldAuth);

    /**
     * 依据运维项目查询租户模组合约明细Id已授权的实例数量字典
     * @param eid 租户Id
     * @param aiopsItemList 运维项目列表
     * @return 回覆对象
     */
    BaseResponse getAuthedCountByAiopsItemList(Long eid, List<String> aiopsItemList);

    /**
     * 依据租户Id获取有效运维项目统计列表
     * @param eid 租户Id
     * @return 回覆对象
     */
    BaseResponse getStatisticsValidAiopsItemListByEid(Long eid);

    /**
     * 获取批量授權列表
     * @param batchAuthListRequest 批量授权列表请求
     * @return 回覆对象
     */
    BaseResponse getBatchAuthList(BatchAuthListRequest batchAuthListRequest);

    /**
     * 依据條件获取SNMP实例列表
     * @param snmpRequest SNMP请求条件
     * @return 回覆对象
     */
    BaseResponse getSNMPInstanceList(SNMPRequest snmpRequest);

    //region SNMP

    /**
     * 依据租户Id获取SNMP实例列表
     * @param eid 租户Id
     * @param snmpType snmp设备类型
     * @return 回覆对象
     */
    BaseResponse getSNMPInstanceListByEid(Long eid, String snmpType);

    /**
     * 依据设备Id获取SNMP信息列表
     * @param deviceId 设备Id
     * @param needPaging 是否分页
     * @param pageNum 页次
     * @param pageSize 页笔数
     * @return 回覆对象(SNMP实例列表)
     */
    BaseResponse getSNMPInstanceListByDeviceId(String deviceId, Boolean needPaging, Integer pageNum, Integer pageSize);

    /**
     * 保存SNMP实例
     * @param aiopsSNMPInstance 运维SNMP实例
     * @return 回覆对象
     */
    BaseResponse saveSNMPInstance(AiopsSNMPInstance aiopsSNMPInstance);

    /**
     * 批量保存SNMP实例
     * @param aiopsSNMPInstanceList 运维SNMP实例
     * @return 回覆对象
     */
    BaseResponse batchSaveSNMPInstance(Boolean autoOpenAuth, String sourceDeviceId, List<AiopsSNMPInstance> aiopsSNMPInstanceList);

    //endregion

    //region 備份軟件

    /**
     * 保存备份软件实例
     * @param aiopsBackupSoftwareInstance 运维备份软件实例
     * @return 回覆对象
     */
    BaseResponse saveBackupSoftwareInstance(AiopsBackupSoftwareInstance aiopsBackupSoftwareInstance);

    //endregion

    //region Http

    /**
     * 依据租户Id获取Http实例列表
     * @param eid 租户Id
     * @param apiCollectType api收集类型
     * @return 回覆对象
     */
    BaseResponse getHttpInstanceListByEid(Long eid, String apiCollectType);

    /**
     * 依据條件获取Http实例列表
     * @param httpRequest Http请求条件
     * @return 回覆对象
     */
    BaseResponse getHttpInstanceList(HttpRequest httpRequest);

    /**
     * 保存Http实例
     * @param aiopsHttpInstance 运维Http实例
     * @return 回覆对象
     */
    BaseResponse saveHttpInstance(AiopsHttpInstance aiopsHttpInstance);

    /**
     * 批量保存Http实例
     * @param aiopsHttpInstanceList 运维Http实例
     * @return 回覆对象
     */
    BaseResponse batchSaveHttpInstance(Boolean autoOpenAuth, String sourceDeviceId,
                                       List<AiopsHttpInstance> aiopsHttpInstanceList);

    //endregion

    //region EDR

    /**
     * 批量保存EDR實例
     * @param sourceDeviceId 来源设备Id
     * @param aiopsEDRInstanceList EDR实例列表
     * @return 回覆对象
     */
    BaseResponse<Map<Long, List<AiopsItemContext>>> batchSaveEDRInstance(String sourceDeviceId,
                                                                         List<AiopsEDRInstance> aiopsEDRInstanceList);

    //endregion

    //region 智能电表

    /**
     * 批量保存智能电表實例
     * @param sourceDeviceId 来源设备Id
     * @param aiopsSmartMeterInstanceList 智能电表实例列表
     * @return 回覆对象
     */
    BaseResponse<Map<Long, List<AiopsItemContext>>> batchSaveSmartMeterInstance(
            String sourceDeviceId,
            List<AiopsSmartMeterInstance> aiopsSmartMeterInstanceList);

    //endregion

    //region 云备援相关

    /**
     * 批量保存云备援实例
     * @param sourceDeviceId 来源设备Id
     * @param aiopsEaiCloudInstanceList 云备援实例列表
     * @return 回覆对象
     */
    BaseResponse<Map<Long, List<AiopsItemContext>>> batchSaveEaiCloudInstance(
            String sourceDeviceId,
            List<AiopsEaiCloudInstance> aiopsEaiCloudInstanceList);

    /**
     * 更新云备援预警状态
     * @param isWarningEnable 预警状态
     * @param serviceCode 客代
     * @param eaiType 云备援类型
     * @return 回覆对象
     */
    BaseResponse modifyAiopsEaiCloudInstance(
            Boolean isWarningEnable,
            String serviceCode,
            String eaiType);

    /**
     * 获取云备援预警状态
     * @param serviceCode 客代
     * @return 回覆对象
     */
    BaseResponse getSelectAiopsEaiCloudWarningStatus(
            String serviceCode, String eaiType);

    /**
     * 获取云备援收集项详细信息
     * @param serviceCode 客代
     * @param uploadDataModelCode 上传资料模型代码
     * @param eaiType 类型
     * @return 回覆对象
     */
    BaseResponse getAiopsEaiCloudInstanceCollectDetail(
            String serviceCode, String uploadDataModelCode, String aiopsItemType, String eaiType);
    /**
     * 移除云备援收集项详细信息
     * @param serviceCode 客代
     * @param eaiType 类型
     * @return 回覆对象
     */
    BaseResponse removeAiopsEaiCloudInstanceCollectDetail(String serviceCode, String eaiType);

    /**
     * 添加無設備實例的收集项及預警項
     * @param serviceCode 客代
     * @param eaiType 类型
     * @return 回覆对象
     */
    BaseResponse addNewInstanceCollectDetail(String serviceCode, String eaiType);

    //endregion


    /**
     * 依据运维项目Id获取运维实例
     * @param aiopsItemId 运维项目Id
     * @return 回覆对象
     */
    BaseResponse getAiopsInstanceByAiopsItemId(String aiopsItemId);

    /**
     * 依据运维项目Id获取运维实例Id
     * @param aiopsItemId 运维项目Id
     * @return 回覆对象
     */
    BaseResponse getAiIdByAiopsItemId(String aiopsItemId);

    /**
     * 异动运维实例设备映射
     * @param sourceDeviceId 来源设备Id
     * @param targetDeviceId 目标设备Id
     * @param copySourceDeviceAiopsItemCollect 拷贝目标设备运维项目收集项
     * @param transferRequestList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse modifyAiopsInstanceDeviceMapping(String sourceDeviceId, String targetDeviceId,
                                                  Boolean copySourceDeviceAiopsItemCollect,
                                                  List<String> transferRequestList);

    /**
     * 依据运维项目Id列表获取运维实例
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse getAiopsInstanceByAiopsItemIdList(List<String> aiopsItemIdList);

    /**
     * 授权到期异动运维实例授权状态
     * @param partTmcdList 租户模组合约明细部分信息列表
     * @return 回覆对象
     */
    BaseResponse modifyAiopsInstanceAuthStatusByContractExpire(List<Map<String, Object>> partTmcdList);

    /**
     * 依据运维项目Id列表获取授权状态字典
     * @param aiopsItemIdList 运维项目Id列表
     * @return 回覆对象
     */
    BaseResponse getAuthStatusMapByAiopsItemIdList(List<String> aiopsItemIdList);

    /**
     * 依据租户模组合约明细Id列表获取实例已授权数量字典
     * @param eid 租戶Id
     * @param isContainHoldAuth 是否包含占用授权标的
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 回覆对象
     */
    BaseResponse getAuthedCountMapByTmcdIdList(Long eid, Boolean isContainHoldAuth, List<Long> tmcdIdList);

    /**
     * 依据运维项目上下文列表获取设备Id列表
     * @param aicList 运维项目上下文列表
     * @return 回覆对象
     */
    List<String> getDeviceIdListByAicList(List<AiopsItemContext> aicList);

    /**
     * 作废设备实例授权
     * @param deviceId 设备Id
     * @return 回覆对象
     */
    BaseResponse invalidDeviceAiopsInstanceAuthStatus(String deviceId);

    /**
     * 依据运维实例Id列表获取运维实例
     * @param aiIdList 运维实例Id列表
     * @return 运维实例列表
     */
    List<AiopsInstance> getAiopsInstanceByAiIdList(List<Long> aiIdList);

    /**
     * 依据SNMPId获取SNMP实例
     * @param snmpId SNMPId
     * @return SNMP实例
     */
    AiopsSNMPInstance getSNMPInstanceBySNMPId(String snmpId);

    /**
     * 依据备份软件Id获取备份软件实例
     * @param backupSoftwareId 备份软件Id
     * @return 备份软件实例
     */
    AiopsBackupSoftwareInstance getBackupSoftwareInstanceByBackupSoftwareId(String backupSoftwareId);

    /**
     * 获取实例
     * @param content 内容
     * @param aiopsItem 运维项目
     * @param eid 租戶Sid
     * @param showInApp 是否显示在app上
     * @param pageNum 页次
     * @param pageSize 页笔数
     * @return 分页信息
     */
    PageInfo<AiopsInstance> getInstances(String content, String aiopsItem, Long eid, Boolean showInApp, int pageNum, int pageSize, String aiopsAuthStatus);

    /**
     * 依据关系获取运维实例项目Id
     * @param truestInstanceTableName 真实实例表名称
     * @param businessPKValueMap 业务主键值字典
     * @param aiopsItemIdColumnName 运维项目Id栏位名称
     * @return 运维实例项目Id
     */
    String getAiopsItemIdByRelate(String truestInstanceTableName, Map<String, Object> businessPKValueMap,
                                  String aiopsItemIdColumnName);

    /**
     * 依据条件字典获取运维实例项目Id列表
     * @param map 条件字典
     * @return 运维实例项目Id列表
     */
    List<String> getAiopsItemIdListByMap(Map<String, Object> map);

    /**
     * 依据条件字典获取上传大数据信息
     * @param map 条件字典
     * @return 字典列表
     */
    List<UploadBigDataContext> getUploadBigDataInfoByMap(Map<String, Object> map);

    /**
     * 依据运维项目Id获取运维真实表明细
     * @param aiopsItemType 运维项目类型
     * @param aiopsItemId 运维项目Id
     * @return 回覆对象
     */
    BaseResponse getTrustDetailByAiopsItemId(String aiopsItemType, String aiopsItemId);

    /**
     * 依据运维商运维模组类别类别代号获取运维实例列表
     * @param request 合并统计明细请求条件
     * @return 回覆对象
     */
    BaseResponse getAiopsInstanceByClassCode(MergeStatisticsDetailRequest request);

    BaseResponse getAiopsInstanceAssetList(MergeStatisticsDetailRequest request);

    /**
     * 依据运维实例Id获取运维实例真实表通用信息
     * @param aiId 运维实例Id
     * @return 回覆对象
     */
    BaseResponse getAiTrustCommonInfoByAiId(Long aiId);

    /**
     * 依据条件字典列表修正实例表租户模组明细Id
     * @param mapList 条件字典列表
     * @return 回覆对象
     */
    BaseResponse fixAiopsInstanceTmcdIdByMapList(List<Map<String, Object>> mapList);

    /**
     * 依条件字典获取运维实例关联的租户列表
     * @param map 条件字典
     * @return 回覆对象
     */
    BaseResponse getAiopsInstanceRelateTenantList(Map<String, Object> map);

    /**
     * 依據租戶模組合約明細Id列表獲取實例表已授權數量
     * @param tmcdIdList 租戶模組合約明細Id列表
     * @return 回覆对象
     */
    BaseResponse<List<Map<String, Object>>> getAiAuthedCountByTmcdIdList(Collection<Long> tmcdIdList);
    BaseResponse fixAiopsInstanceStatusByTmcdIdList(List<AiopsInstanceFixParam> aiParamList);

    BaseResponse tpInstanceSave(AiopsTpModuleInstance aiopsTpModuleInstance);

    String selectAiopsInstanceAiopsItemByDeviceId(String deviceId);

    BaseResponse<List<EdrDevice>> getEdrServerId(String deviceId);

    /**
     * 根据运维项目获取收集项信息
     *
     * @param aiopsItemList
     * @param request
     * @return
     */
    BaseResponse getAccByAiopsItemList(AccRequest request);

    BaseResponse<List<com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo>> getCommonAiopsInstanceAssetListNoPage
            (com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest request);

    /**
     * 根据运维实例Id列表查询StarRocks数据
     * 先根据aiIdList查询aiops_instance表获取aiopsItem列表，然后调用StarRocks查询接口
     *
     * @param aiIdList 运维实例Id列表
     * @return 查询结果
     */
    BaseResponse<List<Map<String, Object>>> queryStarRocksDataByAiIdList(List<Long> aiIdList);

    BaseResponse<Map<Long,AssetAiopsInstance>> buildAssetAiopsInstance(List<Long> aiIdList);
}
