<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aioitms.temprh.dao.TempRhInfoMapper">

    <!-- aiops_temp_rh_instance -->
    <insert id="insertTempRhAuthDetail" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhAuthDetail">
        INSERT INTO aiops_temp_rh_instance (
            id, sid, eid, customer_service_code, thiid,
            device_serial, device_name, device_status,
            authorize, tmp_rh_id, customer_use_device_time
        )
        VALUES (
            #{id}, #{sid}, #{eid}, #{customer_service_code}, #{thiid},
            #{device_serial}, #{device_name}, #{device_status},
            #{authorize}, #{tmp_rh_id}, #{customer_use_device_time}
        )
    </insert>


    <update id="updateTempRhAuthDetail" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhAuthDetail">
        update aiops_temp_rh_instance tad
        set
            tad.sid = #{sid},
            tad.eid = #{eid},
            tad.customer_service_code = #{customer_service_code},
            tad.thiid = #{thiid},
            tad.device_serial = #{device_serial},
            tad.device_name = #{device_name},
            tad.device_status = #{device_status},
            tad.authorize = #{authorize},
            tad.tmp_rh_id = #{tmp_rh_id},
            tad.customer_use_device_time = #{customer_use_device_time}
        where tad.id = #{id}
    </update>

    <select id="touchTempRhDeviceInfoIsExist" resultType="java.lang.String">
        select '7006' aa from aiops_temp_rh_instance where device_serial = #{device_serial}
        union all
        select '7007' aa from aiops_temp_rh_instance where device_name = #{device_name} and id != #{id}
        limit 1
    </select>

    <!-- th_auth_detail.查詢授權及授權資料 -->
    <select id="getThAuthDetailDataById" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhAuthDetail">
        select * from aiops_temp_rh_instance where id = #{id}
    </select>

    <select id="getDeviceData" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhAuthDetail">
        select * from aiops_temp_rh_instance where device_status = #{deviceStatus} and thiid = #{thiId}
    </select>

    <!-- th_auth_detail.取得 EID 的資料-->
    <select id="getSupplierInfo" resultType="Map">
        select * from supplier_tenant_map where eid in (
            SELECT sid FROM tenant where id = #{serviceCode}
        ) and sid > 0
        limit 1;
    </select>

    <select id="queryAllDeviceAndAuthInfo" resultType="com.digiwin.escloud.aioitms.temprh.web.dto.QueryTempRhAuthDetail">
        select
            ad.id as adId, /*查找預警項用*/
            ad.deviceId,  /*查找預警項用, 基本會等同於 aiops_temp_rh_instance.id */
            t.name      as customer_short_name,
            tmc.endDate as contract_end_date,
            IFNULL(ai.aiopsAuthStatus, 'NONE') aiopsAuthStatus,
            ait.name_CN as deviceTypeName_CN, ait.name_TW as deviceTypeName_TW,
            thi.host_name as tmp_rh_host_name,
        ai.id as aiId,
            atri.*
        from
        (
            select a.*, b.remark, b.customerNotes, b.device_location, 'main' as dataType
            from aiops_temp_rh_instance a
            left join temp_rh_customer_info b on a.id = b.tadid and a.eid = b.eid and a.sid = b.sid
            union all
            select atri.id,
            trhis.sid, trhis.eid, trhis.customer_service_code, atri.thiid,
            trhis.tmp_rh_id,atri.device_serial, atri.device_name, 'UNUSED' as device_status,
            false as authorize, null as scrapped_time, null as create_time, null as customer_use_device_time,
            trhis.remark, trhis.customerNotes,trhis.device_location, 'sub' as dataType
            from (select * from aiops_instance where aiopsItemType = 'TMP_RH') ai
            inner join temp_rh_customer_info trhis on trhis.tmp_rh_id = ai.aiopsItemId
            inner join aiops_temp_rh_instance atri on atri.id = trhis.tadid
            where 1=1
            /* and ai.aiopsAuthStatus = 'INVALID' /*只取己回收的資料*/
            and ifnull(atri.eid, -1) != trhis.eid /*己回收的資料, 還在設備主表的aiops_instance, 不需要*/
            and trhis.sid is not null
            and trhis.eid is not null
        ) atri
        left join (select * from aiops_instance where aiopsItemType = 'TMP_RH') ai on ai.aiopsItemId = atri.tmp_rh_id
        left join temp_rh_host_info thi on thi.id = atri.thiid
        left join tenant t on atri.eid = t.sid and atri.customer_service_code = t.id
        left join (
           select
               tmc.eid, endDate, tmcd.usedCount, tmcd.availableCount
           from tenant_module_contract tmc
                 inner join tenant_module_contract_detail tmcd on tmcd.tmcId = tmc.id
                 inner join supplier_aiops_module sam on sam.id = tmc.moduleId and sam.moduleCode = 'TMP_RH'
        ) tmc on tmc.eid = atri.eid
        left join aiops_item_type ait on ait.code = ai.aiopsItemType
        left join aiops_device ad on ad.deviceId = cast(atri.id as char(50))
        where 1=1
            <if test="thiid != null and thiid !='' ">
                AND ( atri.thiid like #{thiid} or thi.host_name like #{thiid})
            </if>
            <if test="device_serial != null and device_serial !='' ">
                AND (atri.device_serial like #{device_serial} or atri.device_name like #{device_serial})
            </if>
            <if test="customer_service_code != null and customer_service_code !='' ">
                AND atri.customer_service_code = #{customer_service_code}
            </if>
            <if test="device_status != null and device_status !='' ">
                and LOCATE(atri.device_status, #{device_status}) > 0
            </if>
            <if test="start_date != null and start_date !='' and end_date != null and end_date !='' ">
                and enddate between DATE_FORMAT(#{start_date}, '%Y-%m-%d') and  DATE_FORMAT(#{end_date}, '%Y-%m-%d')
            </if>
            <if test="device_location != null and device_location !='' ">
                and atri.device_location like #{device_location}
            </if>
            <if test="authStatus != null and authStatus !='' ">
                and LOCATE(ai.aiopsAuthStatus, #{authStatus}) > 0 /* MIS(客戶)/客服 端查看詳情用 */
            </if>

             <choose>
                 <when test="id != null and id !='' and isDetailQuery != null ">
                     AND atri.id = #{id} /* MIS(客戶)/客服 端查看詳情用 */
                     AND atri.eid = #{eid}
                 </when>
                 <otherwise>
                     <choose>
                         <when test="eid != null and eid !='' ">
                             /* MIS(客戶)/客服 端才會用到, 會以 aiops_instance 為主 */
                             AND ai.eid = #{eid}
                             and atri.sid is not null and atri.eid is not null /*aiops_temp_rh_instance  主表的未使用及作廢的, 不顯示*/
                         </when>
                         <otherwise>
                             and atri.dataType = 'main' /* 後台管理用, 會以 aiops_temp_rh_instance 為主 */
                         </otherwise>
                     </choose>
                 </otherwise>
             </choose>

        order by atri.thiid, atri.device_serial, IFNULL(ai.aiopsAuthStatus, 'NONE') asc
    </select>

    <select id="queryDeviceAndAuthInfo" resultType="com.digiwin.escloud.aioitms.temprh.web.dto.QueryTempRhAuthReportDetail">
        select
        atri.device_name,atri.eid, atri.device_serial, atri.device_location, thi.host_name as tmp_rh_host_name
        from
        (
        select
        a.*, b.device_location,
        'main' as dataType
        from aiops_temp_rh_instance a
        left join temp_rh_customer_info b on a.id = b.tadid and a.eid = b.eid and a.sid = b.sid
        union all
        select
        atri.id,
        trhis.sid,
        trhis.eid,
        trhis.customer_service_code,
        atri.thiid,
        trhis.tmp_rh_id,
        atri.device_serial,
        atri.device_name,
        'UNUSED' as device_status,
        false as authorize,
        null as scrapped_time,
        null as create_time,
        null as customer_use_device_time,
        trhis.device_location,
        'sub' as dataType
        from (
        select * from aiops_instance where aiopsItemType = 'TMP_RH') ai
        inner join temp_rh_customer_info trhis on trhis.tmp_rh_id = ai.aiopsItemId
        inner join aiops_temp_rh_instance atri on atri.id = trhis.tadid
        where 1=1
        and ai.aiopsAuthStatus = 'INVALID' /*只取己回收的資料*/
        and ifnull(atri.eid, -1) != trhis.eid /*己回收的資料, 還在設備主表的aiops_instance, 不需要*/
        and trhis.sid is not null
        and trhis.eid is not null
        ) atri
        left join temp_rh_host_info thi on thi.id = atri.thiid
        where atri.eid = #{eid}
        <if test="device_serial != null and device_serial !=''">
            <foreach collection="device_serial" item="item" open=" AND atri.device_serial IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="checkTmpRhContract" resultType="java.lang.String">
        select distinct error_code from (
            /*檢查有沒有合約*/
            select '7009' as error_code from (
                select count(*) tmpRhContractConunt
                from (
                    select tmc.*
                    from tenant_module_contract tmc
                    inner join tenant_module_contract_detail tmcd on tmcd.tmcId = tmc.id
                    inner join supplier_aiops_module sam on sam.id = tmc.moduleId and sam.moduleCode = 'TMP_RH'
                    where 1=1
                    AND tmc.eid = #{eid}
                ) a
            ) a where tmpRhContractConunt = 0
            union all
            /*檢查是不是在合約效期內*/
            select
            '7009' as error_code
            from tenant_module_contract tmc
            left join tenant_module_contract_detail tmcd on tmcd.tmcId = tmc.id
            inner join supplier_aiops_module sam on sam.id = tmc.moduleId and sam.moduleCode = 'TMP_RH'
            where 1=1
            and !(now() between startDate and endDate)  /* 檢查合約日期 */
            AND tmc.eid = #{eid}
            union all
            /*檢查授權數*/
            select
            '7014' as error_code
            from tenant_module_contract tmc
            inner join tenant_module_contract_detail tmcd on tmcd.tmcId = tmc.id
            inner join supplier_aiops_module sam on sam.id = tmc.moduleId and sam.moduleCode = 'TMP_RH'
            where 1=1
            and now() between startDate and endDate  /* 檢查合約日期 */
            and tmcd.usedCount >= tmcd.availableCount /* 檢查授權數 */
            AND tmc.eid = #{eid}
            limit 1
        ) a
    </select>


    <!-- th_host_info -->
    <select  id="getAllTempRhHostInfo" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhHostInfo">
        select * from temp_rh_host_info order by id asc
    </select>

    <select  id="queryTempRhHostInfoById" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhHostInfo">
       select * from temp_rh_host_info where id = #{id}
    </select>

    <select  id="touchTempRhHostInfoIsExist" resultType="java.lang.String">
        select '7001' aa from temp_rh_host_info where id = #{id}
        union all
        select '7002' aa from temp_rh_host_info where host_name = #{host_name}
        union all
        select '7003' aa from temp_rh_host_info where host_api_root_url = #{host_api_root_url}
        limit 1
    </select>

    <select  id="touchTempRhHostInfoIsUsing" resultType="java.lang.String">
        select distinct  errCode from (
            select '7005' as errCode
            from temp_rh_host_info thi
            inner join aiops_temp_rh_instance atri on thi.id = atri.thiid
            where thi.id = #{id}
            union all
            select '7012' as errCode
            from temp_rh_host_info thi
            inner join aiops_temp_rh_instance atri on thi.id = atri.thiid
            where thi.id = #{id}
              AND thi.host_api_root_url != #{host_api_root_url}
        ) a
        where errCode = #{errCode}
        limit 1

    </select>

    <insert id="insertTempRhHostInfo" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhHostInfo">
        INSERT INTO temp_rh_host_info (
            id, host_name, host_api_root_url, host_account, host_account_pwd, host_api_key
        )
        VALUES (
           #{id}, #{host_name}, #{host_api_root_url}, #{host_account}, #{host_account_pwd}, #{host_api_key}
        )
    </insert>

    <update id="updateTempRhHostInfoById" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhHostInfo">
        update temp_rh_host_info
        set host_name = #{host_name}, host_api_root_url = #{host_api_root_url},
            host_account = #{host_account}, host_account_pwd = #{host_account_pwd},
            host_api_key = #{host_api_key}
        where id = #{id}
    </update>

    <delete id="deleteTempRhHostInfoById">
        delete from temp_rh_host_info where id = #{id}
    </delete>



    <!-- th_device_upload_pic -->
    <insert id="insertTempRhDeviceUploadPic" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhDeviceUploadPic">
        INSERT INTO temp_rh_device_upload_pic (id, display_pic_url, tadid, customer_service_code, display_pic_name)
        VALUES (#{id}, #{display_pic_url}, #{tadid}, #{customer_service_code}, #{display_pic_name})
    </insert>

    <delete id="deleteTempRhDeviceUploadPic" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhDeviceUploadPic">
        delete from temp_rh_device_upload_pic
        where 1=1
            and id = #{id}
            and tadid = #{tadid}
            and customer_service_code = #{customer_service_code}
    </delete>

    <select id="getTempRhDeviceUploadPic" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhDeviceUploadPic">
        select * from temp_rh_device_upload_pic pic
        /* inner join aiops_temp_rh_instance atri on pic.tadid = atri.id and atri.device_status = 'IN_USED' */
        where pic.tadid = #{tadId} and pic.customer_service_code = #{customerServiceCode}
    </select>

    <select id="getThAuthDetailDataByIdForUploadPic" resultType="Map">
        select distinct *
        from  (
             select id as tadid, eid, customer_service_code from aiops_temp_rh_instance
             union all
             select tadid, eid, customer_service_code from temp_rh_customer_info
         ) aiops
        where 1=1
          and eid is not null
          and customer_service_code is not null
          and tadid = #{tadid}
          and eid = #{eid}
    </select>


    <!-- th_device_auth_history -->
    <insert id="insertTempRheDeviceAuthHistory" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRheDeviceAuthHistory">
        INSERT INTO temp_rh_device_auth_history (
            device_serial, device_status, sid, eid, customer_service_code, tmp_rh_id
            , op_user_id, op_user_name
            , device_location, add_device_timestamp, return_timestamp
        )
        VALUES (
            #{device_serial}, #{device_status}, #{sid}, #{eid}, #{customer_service_code}, #{tmp_rh_id}
            , #{op_user_id}, #{op_user_name}
            , #{device_location}, #{add_device_timestamp}, #{return_timestamp}
        )
    </insert>

    <select id="getHistoryBySerialAndStatus" resultType="com.digiwin.escloud.aioitms.temprh.web.dto.QueryTempRheDeviceAuthHistory">
        select
            a.device_serial,
            a.customer_service_code,
            b.name as customer_name,
            a.device_status,
            a.timestamp,
            a.op_user_id,
            a.op_user_name,
            a.device_location,
            a.add_device_timestamp,
            a.return_timestamp
        from temp_rh_device_auth_history a
        inner join tenant b on a.customer_service_code = b.id and a.device_serial = #{deviceSerial}
        where 1=1
        <if test="deviceStatus != null and deviceStatus !='' ">
            and a.device_status = #{deviceStatus}
        </if>
        order by a.timestamp desc
    </select>

    <select id="getMaxAddDeviceTimestamp" resultType="java.util.Date">
        select max(add_device_timestamp)
        from temp_rh_device_auth_history a
        inner join tenant b on a.customer_service_code = b.id
        where 1=1
          and a.device_status = 'IN_USED'
          and a.eid = #{eid}
          and a.device_serial = #{deviceSerial}
    </select>

    <select id="checkHasAddDeviceTimestamp" resultType="java.util.Date">
        select distinct add_device_timestamp
        from temp_rh_device_auth_history a
        inner join tenant b on a.customer_service_code = b.id
        where 1=1
          and a.device_status = 'IN_USED'
          and a.add_device_timestamp = #{add_device_timestamp}
          and a.device_serial = #{deviceSerial}
    </select>

    <!-- aiops_instance -->
    <!-- 取消關聯實例表-->
    <update id="cancelRelateAiopsInstance">
        UPDATE aiops_instance
        SET eid = -99999, tmcdId = -99999
        WHERE aiopsItemId = #{aiopsItemId}
    </update>

    <!-- temp_rh_customer_info -->
    <insert id="insertTempRhCustomerInfo" parameterType="com.digiwin.escloud.aioitms.temprh.model.TempRhCustomerInfo">
        INSERT INTO temp_rh_customer_info (
            id, sid, eid, customer_service_code, tmp_rh_id, tadid, device_location, remark, customerNotes
        )
        VALUES (
            #{id}, #{sid}, #{eid}, #{customer_service_code}, #{tmp_rh_id}, #{tadid}, #{device_location}, #{remark}, #{customerNotes}
        )
    </insert>

    <update id="updateTempRhCustomerInfo">
        UPDATE temp_rh_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="device_location != null">device_location = #{device_location},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="customerNotes != null">customerNotes = #{customerNotes},</if>
        </trim>
        where eid = #{eid} and tadid = #{tadid}
    </update>

    <select id="queryTempRhCustomerInfo" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhCustomerInfo">
        select * from temp_rh_customer_info where eid = #{eid} and tadid = #{tadid} and tmp_rh_id = #{tmp_rh_id}
    </select>

    <!-- 下放給  EAI 用的 api -->
    <select id="getEAIRunInfo" resultType="com.digiwin.escloud.aioitms.temprh.model.TempRhEAI">
        select
            atri.eid,
            ai.id as aiId,
            ai.aiopsItemId as instanceId,
            acc.uploadDataModelCode,
            adim.deviceId,   /* deviceId = instanceId = atri.id */
            adcd.accId as collectConfigId,  /* 收集項 ID*/
            adcd.id AS deviceCollectDetailId, /* 明細 ID, 新增的部份*/
            /*--TEMP_RH platform info --*/
            atri.device_serial     as tr_device_serial, /* 可視為 deviceId*/
            trhi.host_api_root_url as tr_host_api_root_url,
            trhi.host_account      as tr_host_account,
            trhi.host_account_pwd  as tr_host_account_pwd,
            trhi.host_api_key      as tr_host_api_key,
            trci.device_location   as tr_device_location /* 溫濕度設備放置點 */
        from
            aiops_instance ai
                inner join aiops_device_instance_mapping adim on adim.aiId = ai.id
                inner join aiops_device_collect_detail adcd on adcd.adimId = adim.id
                inner join aiops_collect_config acc on acc.id = adcd.accid
                inner join aiops_temp_rh_instance atri on atri.tmp_rh_id = ai.aiopsItemId and atri.authorize = 1
                inner join temp_rh_host_info trhi on trhi.id = atri.thiid
                left join temp_rh_customer_info trci on trci.tmp_rh_id = atri.tmp_rh_id
    </select>

    <select  id="getThresholdValue" resultType="java.lang.String">
        SELECT awc.abnormalValue AS abnormalValue
             FROM aiops_device ad
                 INNER JOIN aiops_device_collect adc ON ad.id = adc.adId
                 INNER JOIN aiops_device_collect_detail adcd ON adc.id = adcd.adcId AND adcd.isEnable = 1
                 INNER JOIN (
                     select ai.* from
                     aiops_instance ai
                     INNER join aiops_temp_rh_instance atri on atri.tmp_rh_id = ai.aiopsItemId
                     and ai.aiopsItemType = 'TMP_RH'
                     and atri.device_status = 'IN_USED'
                     union all

                     select ai.* from
                     aiops_instance ai
                     inner join temp_rh_customer_info trci on trci.tmp_rh_id = ai.aiopsItemId
                     inner join aiops_temp_rh_instance atri on trci.tadid = atri.id
                     and ai.aiopsItemType = 'TMP_RH'
                     and ifnull(atri.eid, -1) != trci.eid
                 ) ai ON adcd.aiId = ai.id
                 INNER join aiops_temp_rh_instance atri on atri.tmp_rh_id = ai.aiopsItemId
                 INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id and acc.collectDeviceType = 'TMP_RH'
                 INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
                 INNER JOIN aiops_collect_warning acw ON adcw.acwId = acw.id
                 INNER JOIN aiops_warning_setting aws ON aws.acwId = acw.id and aws.levelCode='FATAL'
                 INNER JOIN aiops_warning_condition awc ON awc.awsId = aws.id
             WHERE ad.id = #{adId} and acw.warningCode= #{warningCode}
        limit 1

    </select>

    <select id="getDeviceCollect" resultType="java.util.Map">
        select adcd.accId, adcd.id as adcdId, acc.scopeId
        from
        aiops_device_collect_detail adcd
        inner join aiops_collect_config acc on adcd.accId = acc.id
        where adcd.adcid in (
            select adc.id
            from aiops_device_collect adc inner join aiops_device ad on ad.deviceId = adc.deviceId
                and ad.deviceId = #{deviceId} and ad.eid != #{eid}
        )

    </select>
</mapper>