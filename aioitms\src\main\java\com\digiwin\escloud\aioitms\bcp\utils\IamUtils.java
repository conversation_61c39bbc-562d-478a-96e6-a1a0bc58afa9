package com.digiwin.escloud.aioitms.bcp.utils;

import com.digiwin.escloud.aioai.model.HeaderInfo;
import com.digiwin.escloud.common.util.StringUtil;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.common.WatchRxError;
import com.digiwin.escloud.integration.service.iam.AuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IamUtils {
    @Value("${digiwin.token.user.verifyuserid}")
    private String verifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String tenantId;
    @Value("${com.digwin.aioitms.defaultSid:241199971893824}")
    private Long defaultSid;

    @Autowired
    private AuthorizationService authorizationService;

    public HeaderInfo getChatAuthHeaders() {
        IamAuthoredUser iamAuthoredUser = getIamAuthoredUser();

        HeaderInfo chatHeaders = new HeaderInfo();
        chatHeaders.setEid(StringUtil.toString(iamAuthoredUser.getTenantSid()));
        chatHeaders.setSid(StringUtil.toString(defaultSid));
        chatHeaders.setToken(iamAuthoredUser.getToken());

        return chatHeaders;
    }

    public IamAuthoredUser getIamAuthoredUser() {
        WatchRxError watchRxError = new WatchRxError();
        IamAuthoredUser iamAuthoredUser = new IamAuthoredUser();
        authorizationService
                .getIamAuthoredUser(verifyUserId, tenantId)
                .subscribe(o ->
                        BeanUtils.copyProperties(o, iamAuthoredUser),
                        watchRxError
                );
        return iamAuthoredUser;
    }

}
