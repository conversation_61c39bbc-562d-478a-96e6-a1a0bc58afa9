<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper">

    <resultMap id="AssetLevelMap" type="com.digiwin.escloud.aiocmdb.asset.model.AssetLevel">
        <id property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="alcId" column="alcId"/>
        <result property="levelName" column="levelName"/>
        <result property="levelNameCN" column="levelNameCN"/>
        <result property="levelNameTW" column="levelNameTW"/>
        <result property="levelExplanation" column="levelExplanation"/>
        <result property="levelIcon" column="levelIcon"/>
        <result property="score" column="score"/>
        <result property="levelColor" column="levelColor"/>
    </resultMap>

    <resultMap id="AssetLevelCategoryMap" type="com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory">
        <id property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="categoryCode" column="categoryCode"/>
        <result property="categoryName" column="categoryName"/>
        <result property="categoryNameCN" column="categoryNameCN"/>
        <result property="categoryNameTW" column="categoryNameTW"/>
        <collection property="assetLevels" ofType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevel"
                    resultMap="AssetLevelMap" columnPrefix="al_"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aiocmdb.model.model.Model">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="appCode" property="appCode" jdbcType="VARCHAR"/>
        <result column="sid" property="sid" jdbcType="BIGINT"/>
        <result column="eid" property="eid" jdbcType="BIGINT"/>
        <result column="modelCode" property="modelCode" jdbcType="VARCHAR"/>
        <result column="modelName" property="modelName" jdbcType="VARCHAR"/>
        <result column="modelVersion" property="modelVersion" jdbcType="VARCHAR"/>
        <result column="modelGroupCode" property="modelGroupCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="imgUrl" property="imgUrl" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, appCode, sid, eid, modelCode, modelName, modelVersion, modelGroupCode, status,
        imgUrl, description, updateTime
    </sql>
    <!-- AssetCategoryClassification 相关SQL -->
    <insert id="insertAssetCategoryClassification" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        INSERT INTO asset_category_classification (id,categoryType,
            categoryName, categoryname_CN, categoryname_TW, code, iconUrl,
            parentId, categoryLevel, canDelete, canEdit
        ) VALUES (#{id},#{categoryType},
            #{categoryName}, #{categoryname_CN}, #{categoryname_TW}, #{code}, #{iconUrl},
            #{parentId}, #{categoryLevel}, #{canDelete}, #{canEdit}
        )
    </insert>

    <update id="updateAssetCategoryClassification" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        UPDATE asset_category_classification SET
                                                 categoryName = #{categoryName},
                                                 categoryname_CN = #{categoryname_CN},
                                                 categoryname_TW = #{categoryname_TW},
            code = #{code},
                                                 iconUrl = #{iconUrl},
                                                 parentId = #{parentId},
                                                 categoryLevel = #{categoryLevel},
                                                 canDelete = #{canDelete},
                                                 canEdit = #{canEdit}
        WHERE id = #{id}
    </update>

    <delete id="deleteAssetCategoryClassification">
        DELETE FROM asset_category_classification WHERE id = #{id}
    </delete>

    <select id="selectAssetCategoryClassificationList" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        SELECT * FROM asset_category_classification WHERE categoryType = #{categoryType} ORDER BY id
    </select>

    <select id="selectAssetCategoryClassificationById" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryClassification">
        SELECT * FROM asset_category_classification WHERE id = #{id}
    </select>

    <select id="countByCategoryName" resultType="int">
        SELECT COUNT(1) FROM asset_category_classification
        WHERE categoryName = #{categoryName} AND categoryType = #{categoryType}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="countAssetCategoryByClassificationId" resultType="int">
        SELECT COUNT(1) FROM asset_category WHERE classificationId = #{classificationId}
    </select>

    <!-- AssetCategory 相关SQL -->
    <insert id="insertAssetCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        INSERT INTO asset_category (id,categoryType,
            sid, scopeId, classificationId, categoryNumber, categoryName,
            categoryname_CN, categoryname_TW, iconUrl, modelCode, sinkName,
            status, creationMode, aiopsItem
        ) VALUES (#{id},#{categoryType},
            #{sid}, #{scopeId}, #{classificationId}, #{categoryNumber}, #{categoryName},
            #{categoryname_CN}, #{categoryname_TW}, #{iconUrl}, #{modelCode}, #{sinkName},
            #{status}, #{creationMode}, #{aiopsItem}
        )
    </insert>

    <update id="updateAssetCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        UPDATE asset_category SET
            classificationId = #{classificationId},
            categoryName = #{categoryName},
            categoryNumber = #{categoryNumber},
            modelCode = #{modelCode},
            sinkName = #{sinkName},
            categoryname_CN = #{categoryname_CN},
            categoryname_TW = #{categoryname_TW},
            iconUrl = #{iconUrl},
            status = #{status},
            creationMode = #{creationMode},
            aiopsItem = #{aiopsItem}
        WHERE id = #{id}
    </update>

    <update id="updateAssetCategoryProcessId" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        UPDATE asset_category SET  processId = #{processId}
        WHERE id = #{id}
    </update>

    <delete id="deleteAssetCategory">
        DELETE FROM asset_category WHERE id = #{id}
    </delete>

    <select id="selectAssetCategoryList" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT ac.*, acrs.ruleSettingValue rule_ruleSettingValue,accr.ruleNumber rule_ruleNumber
        FROM asset_category ac
        LEFT JOIN asset_category_coding_rule_setting_result acrs ON ac.id = acrs.objId
        LEFT JOIN asset_category_coding_rule accr ON accr.id = acrs.ruleId
        <where>
            <if test="classificationId != null">
                AND ac.classificationId = #{classificationId}
            </if>
        </where>
        ORDER BY ac.id
    </select>

    <!-- 分页查询资产类别，不包含编码规则数据 -->
    <select id="selectAssetCategoryListWithPaging" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT ac.*,ai.name as aiopsItemName,ai.name_CN as aiopsItemName_CN,ai.name_TW as aiopsItemName_TW
        FROM asset_category ac
        LEFT JOIN aiops_item ai ON ai.code = ac.aiopsItem
        LEFT JOIN cmdb_model cm ON cm.modelCode = ac.modelCode
        <where>
            <if test="sid != null">
                AND ac.sid = #{sid}
            </if>
            <if test="scopeId != null and scopeId != ''">
                AND ac.scopeId = #{scopeId}
            </if>
            <if test="classificationId != null">
                AND ac.classificationId = #{classificationId}
            </if>
            <if test="status != null">
                AND ac.status = #{status}
            </if>
            <if test="creationMode != null">
                AND ac.creationMode = #{creationMode}
            </if>
            <if test="categoryType != null">
                AND ac.categoryType = #{categoryType}
            </if>
            <if test="aiopsItem != null and aiopsItem != ''">
                AND ac.aiopsItem = #{aiopsItem}
            </if>
            <if test="categoryName != null and categoryName != ''">
                AND (ac.categoryName LIKE CONCAT('%', #{categoryName}, '%') OR ac.categoryNumber LIKE CONCAT('%', #{categoryName}, '%'))
            </if>
            <if test="categoryNumber != null and categoryNumber != ''">
                AND (ac.categoryNumber LIKE CONCAT('%', #{categoryNumber}, '%') OR cm.modelName LIKE CONCAT('%', #{categoryNumber}, '%'))
            </if>
        </where>
        ORDER BY ac.id
    </select>

    <!-- 不分页查询资产类别，不包含编码规则数据 -->
    <select id="selectAssetCategoryListWithoutPaging" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT ac.*,ai.name as aiopsItemName,ai.name_CN as aiopsItemName_CN,ai.name_TW as aiopsItemName_TW
        FROM asset_category ac
        LEFT JOIN aiops_item ai ON ai.code = ac.aiopsItem
        <where>
            <if test="classificationId != null">
                AND ac.classificationId = #{classificationId}
            </if>
            <if test="categoryType != null">
                AND ac.categoryType = #{categoryType}
            </if>
            <if test="categoryNumber != null">
                AND ac.categoryNumber = #{categoryNumber}
            </if>
        </where>
        ORDER BY ac.id
    </select>

    <select id="selectAssetCategoryById" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT * FROM asset_category WHERE id = #{id}
    </select>

    <select id="countBySidScopeIdCategoryNumber" resultType="int">
        SELECT COUNT(1) FROM asset_category
        WHERE sid = #{sid} AND scopeId = #{scopeId} AND categoryNumber = #{categoryNumber}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        <if test="categoryType != null">
            AND categoryType = #{categoryType}
        </if>
    </select>

    <select id="countBySidScopeIdCategoryName" resultType="int">
        SELECT COUNT(1) FROM asset_category
        WHERE sid = #{sid} AND scopeId = #{scopeId} AND categoryName = #{categoryName} AND categoryType = #{categoryType}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        <if test="categoryType != null">
            AND categoryType = #{categoryType}
        </if>
    </select>

    <!-- CmdbModelDataFieldRelationMapping 相关SQL -->
    <insert id="insertCmdbModelDataFieldRelationMapping" parameterType="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping">
        INSERT INTO cmdb_model_data_field_relation_mapping (id,
            targetModelCode, targetModelFieldName, targetModelFieldJsonPath,
            accId, sourceModelCode, sourceModelFieldName, sourceModelFieldJsonPath,
            transformRuleType, transformRule, description
        ) VALUES (#{id},
            #{targetModelCode}, #{targetModelFieldName}, #{targetModelFieldJsonPath},
            #{accId}, #{sourceModelCode}, #{sourceModelFieldName}, #{sourceModelFieldJsonPath},
            #{transformRuleType}, #{transformRule}, #{description}
        )
    </insert>

    <delete id="deleteCmdbModelDataFieldRelationMappingByTargetModelCode">
        DELETE FROM cmdb_model_data_field_relation_mapping WHERE targetModelCode = #{targetModelCode}
    </delete>

    <select id="selectCmdbModelDataFieldRelationMappingByTargetModelCode" resultType="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping">
        SELECT * FROM cmdb_model_data_field_relation_mapping
        WHERE targetModelCode = #{targetModelCode}
        ORDER BY id
    </select>

    <select id="countByTargetModelCodeAndFieldName" resultType="int">
        SELECT COUNT(1) FROM cmdb_model_data_field_relation_mapping
        WHERE targetModelCode = #{targetModelCode} AND targetModelFieldName = #{targetModelFieldName} AND targetModelFieldJsonPath = #{targetModelFieldJsonPath}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- AssetCategoryCodingRule 相关SQL -->
    <select id="selectAllAssetCategoryCodingRule" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRule">
        SELECT * FROM asset_category_coding_rule ORDER BY id
    </select>

    <!-- AssetCategoryCodingRuleSettingResult 相关SQL -->
    <select id="selectAssetCategoryCodingRuleSettingResultByObjId" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSettingResult">
        SELECT accrsr.*,accr.ruleNumber FROM asset_category_coding_rule_setting_result accrsr
                 LEFT JOIN asset_category_coding_rule accr ON accr.id = accrsr.ruleId
                 WHERE objId = #{objId}
        ORDER BY accrsr.id
    </select>

    <delete id="deleteAssetCategoryCodingRuleSettingResult">
        DELETE FROM asset_category_coding_rule_setting_result WHERE objId = #{objId}
    </delete>

    <insert id="insertAssetCategoryCodingRuleSettingResult" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSettingResult">
        INSERT INTO asset_category_coding_rule_setting_result (
            id, objType, objId,
            ruleId, ruleSettingValue
        ) VALUES (
                     #{id}, #{objType}, #{objId},
                     #{ruleId}, #{ruleSettingValue}
                 )
    </insert>

    <insert id="batchUpsertAiopsCollectSink">
        INSERT INTO aiops_collect_sink
        (id,modelCode, dataScene, sinkName)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.modelCode}, #{item.dataScene}, #{item.sinkName})
        </foreach>
        ON DUPLICATE KEY UPDATE
        sinkName = VALUES(sinkName)
    </insert>

    <select id="selectAssetCategorySinkNameByAiopsItemList" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetCategory">
        SELECT
        ac.sinkName,ac.aiopsItem
        FROM
        asset_category ac
        WHERE
        1=1
        <if test="aiopsItemList != null and !aiopsItemList.isEmpty()">
            AND ac.aiopsItem IN
            <foreach collection="aiopsItemList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="aiopsItemList == null or aiopsItemList.isEmpty()">
            AND 1 = 0
        </if>
        ORDER BY
        ac.id
    </select>

    <select id="selectAssetLevelCategory" resultMap="AssetLevelCategoryMap">
        SELECT
        alc.id, alc.sid, alc.categoryCode, alc.categoryName, alc.categoryNameCN, alc.categoryNameTW,
        al.id AS al_id, al.sid AS al_sid, al.alcId AS al_alcId, al.levelName as al_levelName, al.levelNameCN as al_levelNameCN,
        al.levelNameTW as al_levelNameTW, al.levelExplanation AS al_levelExplanation, al.levelIcon AS al_levelIcon, al.score AS al_score,
        al.levelColor AS al_levelColor
        FROM asset_level_category alc
        LEFT JOIN asset_level al ON al.alcId = alc.id
    </select>

    <select id="selectAssetRiskLevel" resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel">
        SELECT * FROM asset_risk_level
        WHERE 1=1
    </select>

    <insert id="insertAssetLevelCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory">
        INSERT INTO asset_level_category (id, sid, categoryCode, categoryName, categoryNameCN, categoryNameTW)
        VALUES (#{id}, #{sid}, #{categoryCode}, #{categoryName}, #{categoryNameCN}, #{categoryNameTW})
    </insert>

    <update id="updateAssetLevelCategory" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory">
        UPDATE asset_level_category
        SET sid = #{sid}, categoryCode = #{categoryCode}, categoryName = #{categoryName},
            categoryNameCN = #{categoryNameCN}, categoryNameTW = #{categoryNameTW}
        WHERE id = #{id}
    </update>

    <select id="selectAssetLevelByLevelName"  resultType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevel">
        SELECT *
        FROM asset_level
        WHERE levelName = #{levelName}
          AND alcId = #{alcId}
            LIMIT 1
    </select>

    <insert id="insertAssetLevel" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevel">
        INSERT INTO asset_level (id, sid, alcId, levelName, levelNameCN, levelNameTW, levelExplanation, levelIcon, score, levelColor)
        VALUES (#{id}, #{sid}, #{alcId}, #{levelName}, #{levelNameCN}, #{levelNameTW}, #{levelExplanation}, #{levelIcon}, #{score},
                #{levelColor})
    </insert>

    <update id="updateAssetLevel" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetLevel">
        UPDATE asset_level
        SET sid = #{sid}, alcId = #{alcId}, levelName = #{levelName}, levelNameCN = #{levelNameCN},
            levelNameTW = #{levelNameTW}, levelExplanation = #{levelExplanation}, levelIcon = #{levelIcon},
            score = #{score}, levelColor = #{levelColor}
        WHERE id = #{id}
    </update>

    <insert id="insertAssetRiskLevel" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel">
        INSERT INTO asset_risk_level (id, levelCode, levelName, levelNameCN, levelNameTW, levelLeftValue, levelRightValue, levelColor)
        VALUES (#{id}, #{levelCode}, #{levelName}, #{levelNameCN}, #{levelNameTW}, #{levelLeftValue}, #{levelRightValue}, #{levelColor})
    </insert>

    <update id="updateAssetRiskLevel" parameterType="com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel">
        UPDATE asset_risk_level
        SET levelCode = #{levelCode}, levelName = #{levelName}, levelNameCN = #{levelNameCN},
            levelNameTW = #{levelNameTW}, levelLeftValue = #{levelLeftValue}, levelRightValue = #{levelRightValue},
            levelColor = #{levelColor}
        WHERE id = #{id}
    </update>

    <select id="selectByModelCodeList" resultMap="BaseResultMap" parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        cmdb_model
        <if test="list != null and !list.isEmpty()">
            WHERE
            modelCode IN
            <foreach collection="list" item="modelCodeItem" open="(" separator="," close=")">
                #{modelCodeItem}
            </foreach>
        </if>
    </select>

    <select id="selectCategoryNumberBySidScopeIdCategoryNumber" resultType="String">
        SELECT categoryNumber FROM asset_category
        WHERE sid = #{sid} AND scopeId = #{scopeId}

    </select>

    <select id="selectModelCodeListByAiopsItemList" resultType="String">
        SELECT DISTINCT modelCode
        FROM asset_category
        WHERE aiopsItem IN
        <foreach collection="aiopsItemList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND modelCode IS NOT NULL
        ORDER BY modelCode
    </select>

</mapper>