package com.digiwin.escloud.aiocmdb.asset.utils;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import lombok.Getter;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;
import java.util.OptionalInt;
import java.util.function.Function;

public enum AssetNoProduceType {
    TEXT(textExecuteLogic(), null, 97001),
    DATE(dateExecuteLogic(), null, 97002),
    SERIAL_NUMBER(null, serialNumberExecuteLogic(), 97003);

    private Function<AssetCategoryCodingRuleSimple, String> singleParaExecuteLogic;
    private Function<AssetCategoryCodingRuleSimple[], String> multiParaExecuteLogic;
    @Getter
    private int errCode;
    AssetNoProduceType(
            Function<AssetCategoryCodingRuleSimple, String> singleParaExecuteLogic,
            Function<AssetCategoryCodingRuleSimple[], String> multiParaExecuteLogic,
            int errCode) {
        this.singleParaExecuteLogic = singleParaExecuteLogic;
        this.multiParaExecuteLogic = multiParaExecuteLogic;
        this.errCode = errCode;
    }

    public String execute(AssetCategoryCodingRuleSimple rule) {
        if (this.singleParaExecuteLogic == null) {
            throw new UnsupportedOperationException("Not support single parameter logic.");
        }
        return this.singleParaExecuteLogic.apply(rule);
    }
    public String execute(AssetCategoryCodingRuleSimple[] rule) {
        if (this.multiParaExecuteLogic == null) {
            throw new UnsupportedOperationException("Not support multi parameter logic.");
        }
        return this.multiParaExecuteLogic.apply(rule);
    }

    private static Function<AssetCategoryCodingRuleSimple, String> textExecuteLogic() {
        return rule -> Optional.ofNullable(rule.getRuleSettingValue()).orElse("");
    }

    private static Function<AssetCategoryCodingRuleSimple, String> dateExecuteLogic() {
        return rule -> {
            String datePatten = rule.getRuleSettingValue();
            SimpleDateFormat sdf = new SimpleDateFormat(datePatten);
            return sdf.format(new Date());
        };
    }

    private static Function<AssetCategoryCodingRuleSimple[], String> serialNumberExecuteLogic() {
        return ruleSimples -> {
            //TODO 目前的 logic 只處理, 流水號只出現一次的場景, 不支持設定多個流水號操作

            if (ruleSimples[0].getCurrentFlowNumber().isEmpty()) {
                OptionalInt opt = Arrays.stream(ruleSimples)
                        .filter(rule -> rule.getRuleNumber() == SERIAL_NUMBER)
                        .map(AssetCategoryCodingRuleSimple::getRuleSettingValue)
                        .mapToInt(Integer::parseInt)
                        .findFirst();
                if (!opt.isPresent()) {
                    throw new IllegalArgumentException("SERIAL_NUMBER rule setting value is empty.");
                }
                int snEncodingCount = opt.getAsInt();
                return String.format("%0" + snEncodingCount + "d", 1);
            }

            // 要再去掉 大類別的編碼
            int reduceMainWords = ruleSimples[0].getMainCode().length();
            String flowNumber = ruleSimples[0].getCurrentFlowNumber().substring(reduceMainWords);
            int snEncodingCount = 0;

            // 解析編碼規則
            for (AssetCategoryCodingRuleSimple rule : ruleSimples) {
                if (rule.getRuleNumber() != SERIAL_NUMBER) {
                    int reduceWords = rule.getRuleNumber().execute(rule).length();

                    if (reduceWords > flowNumber.length()) {
                        flowNumber = "0"; //TODO 表示編碼不合規範, 直接從零計算 [有可能發生嗎?]
                        continue;
                    }
                    flowNumber = flowNumber.substring(reduceWords);
                } else {
                    int getRuleSettingValue = Integer.parseInt(rule.getRuleSettingValue());
                    if (flowNumber.length() >= getRuleSettingValue) {
                        flowNumber = flowNumber.substring(0, getRuleSettingValue);
                    }
                    snEncodingCount = getRuleSettingValue;
                    break;
                }
            }
            int flowNumberInt = Integer.parseInt(flowNumber);
            int newFlowNumberInt = flowNumberInt + 1;
            if (String.valueOf(newFlowNumberInt).length() > snEncodingCount) {
                String errMsg = String.format(
                        "assetCode SERIAL_NUMBER Rule Err: Flow number digits exceeds the setting value. " +
                                "newFlowNumberInt:%s,curFlowNumber:%s, curNumberOfDigits:%s",
                        newFlowNumberInt, flowNumberInt, snEncodingCount
                );
                throw new AssetCodeRuleException(SERIAL_NUMBER, errMsg, SERIAL_NUMBER.getErrCode());
            }
            return String.format("%0" + snEncodingCount + "d", newFlowNumberInt);
        };
    }

}
