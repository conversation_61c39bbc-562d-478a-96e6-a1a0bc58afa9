package com.digiwin.escloud.aioitms.authorize.service;

import com.digiwin.escloud.aioitms.authorize.model.AiopsKitAuthToken;
import com.digiwin.escloud.aioitms.authorize.model.AuthorizeResponse;
import com.digiwin.escloud.aioitms.collectconfig.service.ICollectConfigService;
import com.digiwin.escloud.aioitms.device.service.IDeviceV2Service;
import com.digiwin.escloud.aioitms.instance.model.AiopsItem;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemContext;
import com.digiwin.escloud.aioitms.instance.service.IInstanceService;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.aioitms.ruleengine.service.IRuleEngineService;
import com.digiwin.escloud.aioitms.upgrade.enums.DeviceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.InstanceUpgradeEnum;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeMode;
import com.digiwin.escloud.aioitms.upgrade.enums.UpgradeType;
import com.digiwin.escloud.aioitms.upgrade.model.AiopsUpgradeDcdpDetail;
import com.digiwin.escloud.aioitms.upgrade.service.UpgradeService;
import com.digiwin.escloud.aioitms.util.RestItemsUtil;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleClassDetail;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioCmdbFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.Authentication.AuthenticationScope;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aioitms.common.Constants.Device.DEVICE_ID;
import static com.digiwin.escloud.common.constant.AioConstant.EID;

@Slf4j
@Service
public class AiopsAuthorizeV2Service implements IAiopsAuthorizeV2Service, ParamCheckHelp {

    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;

    @Autowired
    @Lazy
    IDeviceV2Service deviceService;
    @Autowired
    IInstanceService instanceService;
    @Autowired
    ICollectConfigService collectConfigService;
    @Autowired
    IRuleEngineService ruleEngineService;
    @Autowired
    UpgradeService upgradeService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    AioUserFeignClient aioUserFeignClient;

    @Resource
    private RestItemsUtil restItemsUtil;
    @Autowired
    private AioCmdbFeignClient aioCmdbFeignClient;

    @Override
    public AuthorizeResponse getAuthorizeToken(Long eid, String deviceId) {
        AuthorizeResponse response = new AuthorizeResponse();
        if (validate(eid, deviceId)) {
            AiopsKitAuthToken token = new AiopsKitAuthToken(UuidUtil.NewId(), AuthenticationScope.AIOPSKIT, new Date(),
                    DateUtil.GetDateAfterDays(1), eid, deviceId);
            storeDigiwinOAuthToken(token);
            response.setCode("0");
            response.setAccessToken(token.getAccessToken());
            return response;
        } else {
            response.setCode("1");
            response.setCode(ResponseCode.EID_DEVICEID_VERIFY_FAIL.getCode());
        }
        return response;
    }

    boolean validate(long eid, String deviceId) {
        if (LongUtil.isEmpty(eid) || StringUtils.isBlank(deviceId)) {
            return false;
        }
        if (validateDeviceId(eid, deviceId)) {
            return true;
        }
        return false;
    }

    boolean validateDeviceId(Long eid, String deviceId) {
        if (LongUtil.isEmpty(eid) || StringUtils.isBlank(deviceId)) {
            return false;
        }
        return deviceService.getDeviceInfoByEidAndDeviceId(eid, deviceId) != null;
    }

    void storeDigiwinOAuthToken(AiopsKitAuthToken token) {
        try {
            Gson gson = GsonUtil.getInstance();
            String tokenValString = gson.toJson(token);
            stringRedisTemplate.opsForValue().set(token.getAccessToken(), tokenValString, 1, TimeUnit.DAYS);
        } catch (Exception ex) {
            System.out.println("storeDigiwinOAuthToken error:");
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public BaseResponse checkAndCreateAiopsInstanceByAiopsItemContext(Long eid, AiopsAuthStatus aiopsAuthStatus,
                                                                      List<AiopsItemContext> aicList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(aicList, "aicList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        aicList = aicList.stream().filter(x -> x != null).collect(Collectors.toList());

        Optional<AiopsItemContext> invalidAii = aicList.stream()
                .filter(x -> StringUtils.isBlank(x.getAiopsItem()) ||
                        StringUtils.isBlank(x.getAiopsItemId())).findFirst();
        if (invalidAii.isPresent()) {
            return BaseResponse.error(ResponseCode.INVALID_PARAM, invalidAii);
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        //region 1.查询是否有可用的合约

        BaseResponse response = checkAndProcessAiopsItemContext(sid, eid, aiopsAuthStatus, aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }

        //endregion

        //region 2.实例授权调整(如果实例不存在的话新增，存在的话异动授权状态)

        //2-1.处理运维实例
        response = instanceService.createAiopsInstanceByAicList(eid, aicList); //预警及收集项
        if (!response.checkIsSuccess()) {
            return response;
        }

        //2-2.处理无须授权的已用数量
        response = processNoneAuthAiopsItemContext(eid, aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }

        //endregion

        return BaseResponse.ok(aicList.size());
    }

    @Override
    public BaseResponse checkAndProcessAiopsItemContext(Long sid, Long eid, AiopsAuthStatus aiopsAuthStatus,
                                                        List<AiopsItemContext> aicList) {
        //整理成要处理的运维项目Set
        Set<String> aiopsItemSet = aicList.stream()
                .map(AiopsItemContext::getAiopsItem)
                .filter(x -> !StringUtils.isBlank(x))
                .collect(Collectors.toSet());

        //region 1.查询是否有可用的合约

        BaseResponse<List<TenantModuleContractDetail>> response =
                aioUserFeignClient.getTenantModuleContractClassDetailByAiopsItemList(sid, eid, aiopsItemSet);
        if (!response.checkIsSuccess()) {
            return response;
        }
        List<TenantModuleContractDetail> tmcdList = response.getData();
        if (CollectionUtils.isEmpty(tmcdList)) {
            //若没有查到运维项目(认定运维项目是无效的)，直接返回失败
            String itemString = String.join(",", aiopsItemSet);
            return BaseResponse.dynamicError(aiopsItemSet, ResponseCode.AIOPS_ITEM_NOT_EXIST, itemString);
        }

        Map<String, TenantModuleContractDetail> tmcdMap = new HashMap<>();
        Map<String, SupplierAiopsModuleClassDetail> samcdMap = new HashMap<>();
        //检查合约中，是否包含要开启授权的运维项目
        tmcdList.stream().filter(Objects::nonNull).forEach(x -> {
            List<SupplierAiopsModuleClassDetail> samcdList = x.getSupplierAiopsModuleClassDetailList();
            if (CollectionUtils.isEmpty(samcdList)) {
                return;
            }
            Map<String, TenantModuleContractDetail> tempMap = samcdList.stream()
                    .filter(Objects::nonNull)
                    .map(y -> {
                        samcdMap.put(y.getAiopsItem(), y);
                        return y;
                    })
                    .collect(Collectors.toMap(SupplierAiopsModuleClassDetail::getAiopsItem, y -> x, (y, z) -> z));
            tmcdMap.putAll(tempMap);
        });

        //1-1.检查是否有找不到的运维项目
        Set<String> notExistAiopsItemList = aiopsItemSet.stream()
                .filter(x -> !tmcdMap.containsKey(x))
                .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(notExistAiopsItemList)) {
            //若有找不到的运维项目，直接返回失败
            String itemString = String.join(",", notExistAiopsItemList);
            return BaseResponse.dynamicError(processErrorAiopsItemData(notExistAiopsItemList),
                    ResponseCode.AIOPS_ITEM_NOT_EXIST, itemString);
        }

        //1-2.检查找到的运维项目是否有合约
        Set<String> notContractAiopsItem = aiopsItemSet.stream()
                .map(x -> {
                    TenantModuleContractDetail tmcd = tmcdMap.get(x);
                    //若没有取到对应的合约，tmcdId会是null
                    if (LongUtil.isEmpty(tmcd.getId())) {
                        SupplierAiopsModuleClassDetail samcd = samcdMap.get(x);
                        //如果是要异动授权状态为已授权，且运维项本身占用授权，就不允许无合约
                        if (AiopsAuthStatus.AUTHED.isSame(aiopsAuthStatus) && samcd.getHoldAuth()) {
                            return x;
                        }
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(notContractAiopsItem)) {
            //若有找不到的合约的运维项目，直接返为失败
            String itemString = String.join(",", notContractAiopsItem);
            return BaseResponse.dynamicError(processErrorAiopsItemData(notContractAiopsItem),
                    ResponseCode.TENANT_MODULE_CONTRACT_NOT_EXIST, eid, itemString);
        }

        //1-3.填充必要的信息
        aicList.forEach(x -> {
            String aiopsItem = x.getAiopsItem();
            x.setEid(eid);
            //因前面samcdMap是基于aiopsItem做出来的字典，因此这里samcd不可能取不到(为null)
            SupplierAiopsModuleClassDetail samcd = samcdMap.get(aiopsItem);
            x.setSamcdId(samcd.getId());
            x.setHoldAuth(BooleanUtils.toBoolean(samcd.getHoldAuth()));
            x.setAiopsItemType(samcd.getAiopsItemType());
            x.setAiopsItem(samcd.getAiopsItem());
            x.setExecParamsModelCode(samcd.getExecParamsModelCode());
            x.setAllowDuplicateMapping(samcd.getAllowDuplicateMapping());
            //修正规则引擎的授权状态，避免异步添加时规则引擎授权问题
            x.setReAuthStatus(aiopsAuthStatus);

            //这里的tmcd有可能是null
            TenantModuleContractDetail tmcd = tmcdMap.get(aiopsItem);
            x.setSourceTmcd(tmcd);
            if (tmcd == null) {
                //TODO:如果tmcd是空的，只能先以samcd是否占用授权来设置(但是这是不准的)
                x.setIsContainHoldAuth(x.getHoldAuth());
            } else {
                x.setIsContainHoldAuth(tmcd.getIsContainHoldAuth());
                // 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新
                x.setIndependentAuth(tmcd.getIndependentAuth());
            }
        });

        //endregion

        return BaseResponse.ok(aicList);
    }

    private List<AiopsItem> processErrorAiopsItemData(Set<String> aiopsItemSet) {
        if (CollectionUtils.isEmpty(aiopsItemSet)) {
            return new ArrayList<>(0);
        }
        //查询出已存在的运维项目列表，已获得对应的(多语言)名称
        BaseResponse response = instanceService.getAiopsItemList(aiopsItemSet);
        Optional<List<AiopsItem>> optAiopsItemList = response.checkAndGetCurrentData();
        List<AiopsItem> aiopsItemList;
        if (!optAiopsItemList.isPresent() || CollectionUtils.isEmpty(aiopsItemList = optAiopsItemList.get())) {
            //若没有查到任何内容，就返回假的运维项目列表
            return aiopsItemSet.stream().map(x -> {
                AiopsItem item = new AiopsItem();
                item.setCode(x);
                item.setName(x);
                item.setName_CN(x);
                item.setName_TW(x);
                return item;
            }).collect(Collectors.toList());
        }
        aiopsItemList.stream()
                .filter(x -> x != null)
                .forEach(x -> {
                    String code = x.getCode();
                    if (aiopsItemSet.contains(code)) {
                        aiopsItemSet.remove(code);
                    }
                });
        if (!CollectionUtils.isEmpty(aiopsItemSet)) {
            //若有没有查到内容，就返回假的运维项目列表
            aiopsItemList.addAll(aiopsItemSet.stream().map(x -> {
                AiopsItem item = new AiopsItem();
                item.setCode(x);
                item.setName(x);
                item.setName_CN(x);
                item.setName_TW(x);
                return item;
            }).collect(Collectors.toList()));
        }
        return aiopsItemList;
    }

    private BaseResponse processNoneAuthAiopsItemContext(Long eid, List<AiopsItemContext> aicList) {
        //排除掉任何包含授权标的的aic
        aicList = aicList.stream().filter(x -> !x.getIsContainHoldAuth()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aicList)) {
            return BaseResponse.ok(0);
        }

        //region 5.重新计算运维实例对应的授权数量(level2-重查检查)

        Set<TenantModuleContractDetail> checkedTmcdSet = new HashSet<>();
        BaseResponse response = checkAuthCount(eid, aicList, true, checkedTmcdSet);
        if (!response.checkIsSuccess()) {
            return response;
        }

        //endregion

        if (CollectionUtils.isEmpty(checkedTmcdSet)) {
            return BaseResponse.ok(0);
        }

        //region 6.更新对应租户模组合约明细的可用数量(level3-更新检查)

        response = aioUserFeignClient.modifyTenantModuleContractDetailUsedCountByTmcdList(eid, checkedTmcdSet);
        if (!response.checkIsSuccess()) {
            return response;
        }

        //endregion

        return BaseResponse.ok(aicList.size());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse modifyInstanceAuthStatus(Long eid, AiopsAuthStatus aiopsAuthStatus,
                                                 List<AiopsItemContext> aicList) {
        //TODO:当极特殊状况：已用授权数若已经远远大于购买授权数时(如：12/10，此段代码无法调整)，此方法会无法正常工作
        //但考虑到，若真发生此状况，应属于某个逻辑段有漏洞造成，只能人工介入处理，并尽速修正漏洞

        if (!AiopsAuthStatus.INVALID.isSame(aiopsAuthStatus) && !AiopsAuthStatus.UNAUTH.isSame(aiopsAuthStatus)) {
            //若是不是作废也不是取消作废，就排除掉任何不包含授权标的的aic
            aicList = aicList.stream()
                    .filter(row -> Objects.nonNull(row.getIsContainHoldAuth()))
                    .filter(AiopsItemContext::getIsContainHoldAuth)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(aicList)) {
                return BaseResponse.ok(0);
            }
        }

        //region 3.检查目前要开通的运维项目可用数量是否充足(level1-粗略检查)

        List<TenantModuleContractDetail> overflowTmcdAuthList;
        if (AiopsAuthStatus.AUTHED.isSame(aiopsAuthStatus)) {
            int aicListSize = aicList.size();
            List<TenantModuleContractDetail> contractInvalidList = new ArrayList<>(aicListSize);
            Map<Long, TenantModuleContractDetail> maybeOverflowTmcdAuthIdMap = new HashMap<>(aicListSize);
            //开启授权，在获取授权时就已经检查过因此sourceTmcd不可能为空
            overflowTmcdAuthList = aicList.stream()
                    .map(AiopsItemContext::getSourceTmcd)
                    .filter(x -> {
                        if (x == null) {
                            //如果不存在授权目标，直接认为超出授权
                            return true;
                        }
                        x.setUsedCount(IntegerUtil.objectToInteger(x.getUsedCount()) + 1);

                        if (x.getUsedCount() > IntegerUtil.objectToInteger(x.getAvailableCount())) {
                            //有可能因为购买合约异动造成虚增的状况，进行后面进行重查检查
                            // 若沒id, 表示 tenant_module_contract_detail 沒該筆數據
                            long key = Optional.ofNullable(x.getId()).orElse(-1L);
                            maybeOverflowTmcdAuthIdMap.put(key, x);
                            return false;
                        }

                        if (!BooleanUtils.toBoolean(x.getContractIsValid())) {
                            contractInvalidList.add(x);
                        }
                        return false;
                    }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(overflowTmcdAuthList)) {
                //超出授权数，返回失败(不需回滚)
                return BaseResponse.dynamicError(overflowTmcdAuthList, ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
            }
            //检查授权是否有效
            if (!CollectionUtils.isEmpty(contractInvalidList)) {
                //存在无效的授权，返回失败(不需回滚)
                return BaseResponse.dynamicError(contractInvalidList, ResponseCode.TENANT_MODULE_CONTRACT_INVALID, eid);
            }

            if (CollectionUtil.isNotEmpty(maybeOverflowTmcdAuthIdMap)) {
                BaseResponse<List<Map<String, Object>>> response = instanceService
                        .getAiAuthedCountByTmcdIdList(maybeOverflowTmcdAuthIdMap.keySet());
                if (!response.checkIsSuccess()) {
                    return response;
                }
                List<Map<String, Object>> checkOverflowList = response.getData();
                if (CollectionUtils.isEmpty(checkOverflowList)) {
                    return BaseResponse.dynamicError(maybeOverflowTmcdAuthIdMap, ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
                }
                checkOverflowList.forEach(x-> {
                    Long tmcdId = LongUtil.objectToLong(x.get("tmcdId"));
                    if (LongUtil.isEmpty(tmcdId)) {
                        //没取到tmcdId认为无效，离开
                        return;
                    }
                    TenantModuleContractDetail tmcd = maybeOverflowTmcdAuthIdMap.get(tmcdId);
                    if (Objects.isNull(tmcd)) {
                        //没取到认为无效，离开
                        return;
                    }
                    Integer authedCount = IntegerUtil.objectToInteger(x.get("authedCount"));
                    if (authedCount >= IntegerUtil.objectToInteger(tmcd.getAvailableCount())) {
                        //实际授权数量大于或等于已购数量，离开
                        return;
                    }
                    //检查后合法将其从字典移除
                    maybeOverflowTmcdAuthIdMap.remove(tmcdId);
                });
                if (CollectionUtil.isNotEmpty(maybeOverflowTmcdAuthIdMap)) {
                    //存在超出授权数，返回失败(不需回滚)
                    return BaseResponse.dynamicError(maybeOverflowTmcdAuthIdMap.values(),
                            ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
                }
            }
        }

        //endregion

        //region 4.改变实例授权状态

        BaseResponse response = instanceService.modifyAiopsInstanceAuthStatusByAicList(aiopsAuthStatus, aicList);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        // 如果实例要作废 需要将 对应得资产也报废
//        invalidCmdbAsset(aiopsAuthStatus, aicList);

            //异动设备已作废状态(如果有的话)
        response = deviceService.modifyDeviceDeletedStatusByAicList(aicList, aiopsAuthStatus);
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        //改变实例关联的规则引擎授权状态
        response = ruleEngineService.modifyRuleEngineAuthStatusByAicList(aicList,
                AiopsAuthStatus.getAuthStatus(aiopsAuthStatus));
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        //endregion

        //region 5.重新计算运维实例对应的授权数量(level2-重查检查)

        Set<TenantModuleContractDetail> checkedTmcdSet = new HashSet<>();
        response = checkAuthCount(eid, aicList, true, checkedTmcdSet);
        if (!response.checkIsSuccess()) {
            //超出授权数，返回失败(需回滚)
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        //endregion

        //若发现没有东西要处理，离开
        if (CollectionUtils.isEmpty(checkedTmcdSet)) {
            return BaseResponse.ok(0);
        }

        //region 6.更新对应租户模组合约明细的可用数量(level3-更新检查)

        response = aioUserFeignClient.modifyTenantModuleContractDetailUsedCountByTmcdList(eid, checkedTmcdSet);

        if (!response.checkIsSuccess()) {
            //若失败(通常表示超出授权数)，返回失败(需要回滚)
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        //endregion

        //region 7.同步到数采

        List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList = aicList.stream().map(x -> {
            Map<String, Object> upgradeParams = new HashMap<>(4);
            upgradeParams.put("id", x.getAiId());
            upgradeParams.put("aiopsItemId", x.getAiopsItemId());
            upgradeParams.put("aiopsAuthStatus", aiopsAuthStatus);
            AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                    InstanceUpgradeEnum.INSTANCE.getTableName(), upgradeParams);
            Map<String, Object> paramsMap = detail.getParamsMap();
            paramsMap.put("onlyUpdateAuthStatus", true);
            paramsMap.put(EID, x.getEid());
            paramsMap.put(DEVICE_ID, x.getSourceDeviceId());
            detail.setNeedSaveOperationLog(true);
            Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
            operationInfoMap.put("operationObjectClassification", "instance");
            operationInfoMap.put("operationObject", "instance");
            operationInfoMap.put("operationBehavior", "updateinstanceinfo");
            detail.setOperationInfoMap(operationInfoMap);
            return detail;
        }).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>(2);
        map.put("aicList", aicList);
        List<Map<String, Object>> mapList = deviceService.getAdcdTrueEnableByMap(map);
        if (CollectionUtil.isNotEmpty(mapList)) {
            upgradeDcdpDetailList.addAll(mapList.stream().map(x -> {
                AiopsUpgradeDcdpDetail detail = new AiopsUpgradeDcdpDetail(UpgradeType.UPDATE,
                        DeviceUpgradeEnum.DEVICE_COLLECT_DETAIL.getTableName(), x);
                Map<String, Object> paramsMap = detail.getParamsMap();
                paramsMap.put("onlyUpdateEnable", true);
                paramsMap.put(EID, x.get(EID));
                paramsMap.put(DEVICE_ID, x.get(DEVICE_ID));
                detail.setNeedSaveOperationLog(true);
                Map<String, Object> operationInfoMap = OperationUtil.getHeaderOperationInfo();
                operationInfoMap.put("operationObjectClassification", "device_collect_detail");
                operationInfoMap.put("operationObject", "device_collect_detail");
                if (BooleanUtil.objectToBoolean(x.get("isEnable"))) {
                    operationInfoMap.put("operationBehavior", "enabledevicecollectdetail");
                } else {
                    operationInfoMap.put("operationBehavior", "disabledevicecollectdetail");
                }
                detail.setOperationInfoMap(operationInfoMap);
                return detail;
            }).collect(Collectors.toList()));
        }
        BaseResponse upgradeRes = executeUpgradeToDcdp(upgradeDcdpDetailList);
        if (!upgradeRes.checkIsSuccess()) {
            //同步数采失败，回滚并返回失败
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return upgradeRes;
        }

        //endregion

        //region 8.刷新相关实例映射设备收集项缓存

        List<String> deviceIdList = instanceService.getDeviceIdListByAicList(aicList);
        if (!CollectionUtils.isEmpty(deviceIdList)) {
            deviceIdList.forEach(x -> collectConfigService.clearDeviceIdCollectConfigCache(x));
        }

        //endregion

        return BaseResponse.ok(aicList.size());
    }

    private void invalidCmdbAsset(AiopsAuthStatus aiopsAuthStatus, List<AiopsItemContext> aicList) {
        if (aiopsAuthStatus == AiopsAuthStatus.INVALID){
            List<AiopsItemContextDTO> dtoList = aicList
                    .stream()
                    .filter(aic -> BooleanUtil.objectToBoolean(aic.getModifyAsset()))
                    .map(i -> {
                        AiopsItemContextDTO aiopsItemContextDTO = new AiopsItemContextDTO();
                        aiopsItemContextDTO.setAiId(i.getAiId());
                        aiopsItemContextDTO.setAiopsItem(i.getAiopsItem());
                        return aiopsItemContextDTO;
                    }).collect(Collectors.toList());
            BaseResponse baseResponse = aioCmdbFeignClient.invalidCmdbAsset(dtoList);
            if (!baseResponse.checkIsSuccess()) {
                log.error("[invalidCmdbAsset] update cmdb asset status failed ,{}",baseResponse);
            }
        }
    }

    private BaseResponse executeUpgradeToDcdp(List<AiopsUpgradeDcdpDetail> upgradeDcdpDetailList) {
        return upgradeService.upgradeToDcdpByDetail(UpgradeMode.SYNC, upgradeDcdpDetailList);
    }

    @Override
    public BaseResponse checkAuthCount(Long eid, List<AiopsItemContext> aicList, Boolean ignoreNullTmcd,
                                       Set<TenantModuleContractDetail> checkedTmcdSet) {
        List<TenantModuleContractDetail> overflowTmcdAuthList = aicList.stream()
                .filter(x -> {
                    TenantModuleContractDetail tmcd = x.getSourceTmcd();
                    if (tmcd == null && BooleanUtils.toBoolean(ignoreNullTmcd)) {
                        return false;
                    }
                    return true;
                })
                .peek(x -> {
                    TenantModuleContractDetail tmcd = x.getSourceTmcd();
                    if (tmcd == null) {
                        tmcd = new TenantModuleContractDetail();
                        //默认没有取到的先认定包含占用授权标的
                        tmcd.setIsContainHoldAuth(true);
                        tmcd.setAvailableCount(0);
                        x.setSourceTmcd(tmcd);
                    }
                })
                .filter(x -> !checkedTmcdSet.contains(x.getSourceTmcd()))
                .filter(x -> {
                    TenantModuleContractDetail tmcd = x.getSourceTmcd();
                    checkedTmcdSet.add(tmcd);
                    Long tmcdId = tmcd.getId();
                    if (LongUtil.isEmpty(tmcdId)) {
                        //如果没有租户模组合约明细，那就不做检查
                        return false;
                    }
                    boolean isContainHoldAuth = BooleanUtils.toBoolean(tmcd.getIsContainHoldAuth());
                    BaseResponse<Integer> authedCountResponse = instanceService.getAuthedCountByTmcdId(tmcdId,
                            isContainHoldAuth);
                    if (!authedCountResponse.checkIsSuccess()) {
                        System.out.println("getAuthedCountByTmcdId error:" + authedCountResponse.getErrMsg());
                        return false;
                    }
                    int currentUsedCount = IntegerUtil.objectToInteger(authedCountResponse.getData());
                    tmcd.setUsedCount(currentUsedCount);

                    if (isContainHoldAuth && currentUsedCount > tmcd.getAvailableCount()) {
                        return true;
                    }

                    return false;
                }).map(AiopsItemContext::getSourceTmcd).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(overflowTmcdAuthList)) {
            return BaseResponse.dynamicError(overflowTmcdAuthList, ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
        }
        return BaseResponse.ok();
    }
}
