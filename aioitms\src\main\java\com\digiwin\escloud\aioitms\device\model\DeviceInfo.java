package com.digiwin.escloud.aioitms.device.model;

import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *设备
 */
@Data
public class DeviceInfo extends AiopsKitDevice {
    @ApiModelProperty("是否在線")
    private Boolean isOnLine;
    @ApiModelProperty("最后报到时间差异秒")
    private Long lastCheckInTimeDifferSecond;
    @ApiModelProperty(value = "租户名称")
    private String tenantName;
    @ApiModelProperty(value = "客服代号")
    private String serviceCode;

    @ApiModelProperty(value = "运维授权状态")
    private AiopsAuthStatus aiopsAuthStatus;

    @ApiModelProperty(value = "设备实例id")
    // 配合查询预警表 所以不驼峰
    private Long aiid;


    @ApiModelProperty("设备收集项详情")
    private List<DeviceCollectDetail> deviceCollectDetailList;

    private List<String> tmcList;
    // 因资产相关需要知道aiId
    private Long aiInstanceId;
    private AssetAiopsInstance assetAiopsInstance;
}
