package com.digiwin.escloud.aioitms.temprh.service;

import com.digiwin.escloud.aiocmdb.model.AssetAiopsInstance;
import com.digiwin.escloud.aioitms.collectwarning.model.CollectWarning;
import com.digiwin.escloud.aioitms.device.service.IDeviceV2Service;
import com.digiwin.escloud.aioitms.instance.model.SNMPInfo;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.authorize.service.IAiopsAuthorizeV2Service;
import com.digiwin.escloud.aioitms.device.dao.DeviceV2Mapper;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemContext;
import com.digiwin.escloud.aioitms.instance.service.IInstanceService;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping;
import com.digiwin.escloud.aioitms.model.device.DevicePlatform;
import com.digiwin.escloud.aioitms.temprh.dao.TempRhInfoMapper;
import com.digiwin.escloud.aioitms.temprh.model.*;
import com.digiwin.escloud.aioitms.temprh.utils.HiveEaiTempRhBizInfo;
import com.digiwin.escloud.aioitms.temprh.web.dto.*;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SerializeUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.iam.req.user.AuthoredUser;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class TempRhAuthDetailServiceImpl implements TempRhAuthDetailService {


    @Autowired
    private TempRhInfoMapper tempRhInfoMapper;

//    @Autowired
//    private CallBigDataApiService callBigDataApiService;

    @Autowired
    private CallStarRocksApiService callStarRocksApiService;

    @Autowired
    private IInstanceService instanceService;

    @Autowired
    private IAiopsAuthorizeV2Service authorizeService;

    @Autowired
    private TempRhHostInfoService tempRhHostInfoService;

    @Autowired
    private DeviceV2Mapper deviceMapper;

    @Autowired
    private AioUserFeignClient aioUserFeignClient;

    @Autowired
    private TempRhCustomerInfoService tempRhCustomerInfoService;

    @Autowired
    private IDeviceV2Service deviceService;

    @Autowired
    private DeviceV2Mapper deviceV2Mapper;

    private static final int DEFAULT_PAGE_NO = 1;
    private static final int DEFAULT_PAGE_SIZE = 20;

    public enum AuthHistoryAction {
        //授權時, 要 aiops_temp_rh_instance 表 (TempRhDeviceStatus.IN_USED)
        OP_AUTH_ACTION(),

        //設備在回收或做廢時, 需取消授權, 但不用再更新 aiops_temp_rh_instance 表 (TempRhDeviceStatus.UNUSED, TempRhDeviceStatus.SCRAPPED)
        CHANGE_DEVICE_STATUS(),

        ADD_NEW_DEVICE();

        AuthHistoryAction() {
        }
    }

    /**
     * 取得所有溫濕度設備的列表
     *
     * @param pageNum  頁碼
     * @param pageSize 顯示筆數
     * @param queryMap 查詢條件
     * @return
     */
    public PageInfo<QueryTempRhAuthDetail> queryAllDeviceAndAuthInfo(
            int pageNum, int pageSize, Map<String, Object> queryMap
    ) {
        //查询
        pageNum = pageNum == 0 ? DEFAULT_PAGE_NO : pageNum;
        pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;

        PageHelper.clearPage();
        Page page = PageHelper.startPage(pageNum, pageSize);

        List<QueryTempRhAuthDetail> data = tempRhInfoMapper.queryAllDeviceAndAuthInfo(queryMap); //query data
        PageInfo<QueryTempRhAuthDetail> pageInfo = new PageInfo<>(page);
        enrichDeviceListWithAssetInfo(data);
        //取得各設備最後更新時間
        Map<String, HiveEaiTempRhBizInfo> lastCollectMap = callStarRocksApiService.getAllLastCollectInfo();

        pageInfo.getList().stream()
                .forEach(row -> {
                    Optional.ofNullable(lastCollectMap.get(row.getKey()))
                            .ifPresent(htb -> {
                                row.setLast_device_sync_time(htb.getCollectedTime());
                                row.setLast_device_collect_time(htb.getDeviceCollectTime());
                                row.setDegrees(htb.getDegrees());
                                row.setWetness(htb.getWetness());
                            });
                    // 找出溫度閥值
                    row.setDegreesThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"tmp_warning"));
                    // 找出濕度閥值
                    row.setWetnessThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"rh_waring"));
                });
        return pageInfo;
    }

    private void enrichDeviceListWithAssetInfo(List<QueryTempRhAuthDetail> tempRhAuthDetailList) {
        if (CollectionUtils.isEmpty(tempRhAuthDetailList)) {
            return;
        }

        List<Long> aiIdList = tempRhAuthDetailList.stream()
                .map(QueryTempRhAuthDetail::getAiId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(aiIdList)) {
            return;
        }

        BaseResponse<Map<Long, AssetAiopsInstance>> mapBaseResponse = instanceService.buildAssetAiopsInstance(aiIdList);
        if (mapBaseResponse.checkIsSuccess() && mapBaseResponse.getData() != null) {
            Map<Long, AssetAiopsInstance> assetAiopsInstanceMap = mapBaseResponse.getData();
            tempRhAuthDetailList.forEach(temp -> {
                AssetAiopsInstance assetAiopsInstance = assetAiopsInstanceMap.get(temp.getAiopsInstanceId());
                temp.setAssetAiopsInstance(assetAiopsInstance);
            });
        }
    }

    /**
     * 取得單筆溫濕度設備資料
     *
     * @param id table: aiops_temp_rh_instance.id
     * @return
     */
    public QueryTempRhAuthDetail getTempRhDeviceDetailById(Long id, Long eid) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", id);
        queryMap.put("eid", eid);
        queryMap.put("isDetailQuery", true);

        List<QueryTempRhAuthDetail> data = tempRhInfoMapper.queryAllDeviceAndAuthInfo(queryMap); //query data

        if (!data.isEmpty()) {
            //取得各設備最後更新時間
            Map<String, HiveEaiTempRhBizInfo> lastCollectMap = callStarRocksApiService.getAllLastCollectInfo();
            data.stream()
                    .forEach(row -> Optional.ofNullable(lastCollectMap.get(row.getKey()))
                            .ifPresent(htb -> {
                                row.setLast_device_sync_time(htb.getCollectedTime());
                                row.setLast_device_collect_time(htb.getDeviceCollectTime());
                                row.setDegrees(htb.getDegrees());
                                row.setWetness(htb.getWetness());
                            }));
            System.out.println("getTempRhDeviceDetailById().result: get data");
            return data.get(0);
        }
        System.out.println("getTempRhDeviceDetailById().result: no data");
        return null;
    }

    /**
     * 查詢未使用的設備
     *
     * @param thiId
     * @return
     */
    public List<TempRhAuthDetail> getUnusedDevice(String thiId) {
        return tempRhInfoMapper.getDeviceData(thiId, TempRhDeviceStatus.UNUSED);
    }
    /**
     * 取得單筆溫濕度設備資料
     *
     * @param eid device_serial
     * @return
     */
    public List<QueryTempRhAuthReportDetail> getbyeidqueryDeviceDetails(String eid, List<String> device_serial) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("device_serial", device_serial);
        queryMap.put("eid", eid);

        return tempRhInfoMapper.queryDeviceAndAuthInfo(queryMap); //query data
    }

    /**
     * 授權設備
     *
     * @param authDevice
     * @return
     */
    private BaseResponse<TempRhAuthDetail> authDeviceToCustomer(
            AiopsAuthStatus aiopsAuthStatus, TempRhAuthDevice authDevice, TempRhAuthDetail tad
    ) {
        try {
            //己經授權的資料, 不要重復授權
            if (AiopsAuthStatus.AUTHED == aiopsAuthStatus
                    && tad.getDevice_status() == TempRhDeviceStatus.IN_USED
                    && Optional.ofNullable(tad.getAuthorize()).orElse(false)) {
                return BaseResponse.error(ResponseCode.TMP_RH_AUTH_FAIL_IS_DUPLICATE);
            }

            //己報廢不能操作
            if (TempRhDeviceStatus.SCRAPPED == tad.getDevice_status()) {
                return BaseResponse.error(ResponseCode.TMP_RH_AUTH_FAIL_IS_SCRAPPED);
            }

            // 取得 eid, sid
            Map<String, Object> supplierInfo = Optional.ofNullable(
                    tempRhInfoMapper.getSupplierInfo(authDevice.getCustomerServiceCode())
            ).orElseThrow(() -> new RuntimeException("CustomerServiceCode Not Found Data for " + authDevice.getCustomerServiceCode()));

            // 設定基本資料-1
            tad.setThiid(authDevice.getThiId());

            // 設定 eid, sid.
            tad.setEid((Long) supplierInfo.get("eid"));
            tad.setSid((Long) supplierInfo.get("sid"));
            tad.setCustomer_service_code(authDevice.getCustomerServiceCode());

            // 設定基本資料-2
            if (AiopsAuthStatus.AUTHED.isSame(aiopsAuthStatus)) {
                tad.setAuthorize(Boolean.TRUE);
                tad.setDevice_status(TempRhDeviceStatus.IN_USED); //援權, 一律視為己使用
            } else {
                tad.setAuthorize(Boolean.FALSE);
                tad.setDevice_status(TempRhDeviceStatus.IN_USED); //取消授權時, 不變更設備狀態 (己經分配到客戶的情況下)
            }

            //設定設備使用時間
            tad.setCustomer_use_device_time(Optional.ofNullable(tad.getCustomer_use_device_time()).orElse(new Date()));


            //產生溫濕度的業務主鍵 - 用來反查用
            tad.createTemRhId();

            //update tmpRh device data
            tempRhInfoMapper.updateTempRhAuthDetail(tad);

            //更新設備客戶放置点的位置
            tempRhCustomerInfoService.changeDeviceLocation(
                    tad, Optional.ofNullable(authDevice.getDevice_location()),
                    Optional.ofNullable(authDevice.getRemark()),Optional.ofNullable(authDevice.getCustomerNotes())
            );

            //檢查是不是己有記錄 "設備己使用的時間"
            boolean isNeedAddDeviceTimestamp = this.isNeedAddDeviceTimestamp(tad);

            //save history.
            if (isNeedAddDeviceTimestamp) {
                //記錄添加設備時間 for history.
                this.saveTempRheDeviceAuthHistory(tad, recAddDeviceTimestamp(tad));
            }

            //查找有沒有合約的資料
            if (AiopsAuthStatus.AUTHED == aiopsAuthStatus) {
                //2022-05-27 調整
                //陈莹莹  10:38:46 是的 如果不在这个时间段就是授权失败
                //陈莹莹  10:39:02 或者是这个租户下没有温湿度的授权  也是授权失败
                //陈莹莹  10:39:29 还有就是授权数量不够  也是授权失败
                List<String> contractErrData = tempRhInfoMapper.checkTmpRhContract(tad.getEid());
                if (contractErrData.size() > 0) {
                    String errCode = contractErrData.get(0);
                    if (ResponseCode.TMP_RH_CUSTMOER_NO_CONTRACT.isSameCode(errCode)) {
                        return BaseResponse.error(ResponseCode.TMP_RH_CUSTMOER_NO_CONTRACT);
                    }
                    if (ResponseCode.TMP_RH_CUSTMOER_NO_CONTRACT_AUTHORIZATION.isSameCode(errCode)) {
                        return BaseResponse.error(ResponseCode.TMP_RH_CUSTMOER_NO_CONTRACT_AUTHORIZATION);
                    }
                }
            }

            return BaseResponse.ok(tad);

        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.dynamicError(e, ResponseCode.INTERNAL_ERROR);
        }
    }

    /**
     * 建立溫濕專用的虛擬設備
     *
     * @param tad
     * @param sourceDeviceId
     * @return
     */
    private BaseResponse saveVirtualDeviceInfo(TempRhAuthDetail tad, String sourceDeviceId, AiopsAuthStatus aiopsAuthStatus) {
        try {
            long adId = Optional.ofNullable(deviceMapper.selectAdIdByDeviceId(sourceDeviceId))
                    .orElse(new Long(-1));
            if (adId < 0) {
                AiopsKitDevice device = new AiopsKitDevice();
                device.setId(SnowFlake.getInstance().newId());
                device.setEid(tad.getEid());
                device.setDeviceId(sourceDeviceId); //FIXME 溫濕度虛擬的 device id
//                device.setDeviceName("TMP_RH_FAKE_DEVICE");
                device.setDeviceName(tad.getDevice_name());
                device.setPlatform(DevicePlatform.TMP_RH);
                device.setPlacementPoint("EAI-CROSS-AZURE");
                device.setIpAddress("127.0.0.1");
                device.setRemark("透過ETL採集溫濕度資料");
                deviceMapper.insertDevice(device);

                adId = device.getId();
            } else {
                //如果為未授權, 則該虛擬設備, 不跟這家客戶關聯 (會影響授權數統計)
                AiopsKitDevice device = new AiopsKitDevice();
                device.setId(adId);

                if (AiopsAuthStatus.UNAUTH == aiopsAuthStatus) {
                    if (TempRhDeviceStatus.IN_USED == tad.getDevice_status()) {
                        device.setEid(tad.getEid());
                    } else {
                        device.setEid(-99999L); //表示無客戶使用
                    }

                } else if (AiopsAuthStatus.AUTHED == aiopsAuthStatus) {
                    device.setEid(tad.getEid());
                }
                device.setDeviceName(tad.getDevice_name());
                deviceMapper.updateDevice(device);
            }

            //產生對設備對應表資料
            List<AiopsKitDeviceTypeMapping> decTypeMappingData = deviceMapper.selectDeviceTypeMappingByDeviceId(sourceDeviceId);
            if (decTypeMappingData.isEmpty()) {
                AiopsKitDeviceTypeMapping decTypeMapping = new AiopsKitDeviceTypeMapping();
                decTypeMapping.setId(SnowFlake.getInstance().newId());
                decTypeMapping.setAdId(adId);
                decTypeMapping.setDeviceId(sourceDeviceId);
                decTypeMapping.setDeviceType(DevicePlatform.TMP_RH.name());
                decTypeMappingData.add(decTypeMapping);
                deviceMapper.batchInsertDeviceTypeMapping(decTypeMappingData);
            }
            return BaseResponse.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(ResponseCode.TMP_RH_ADD_VIRTUAL_DEVICE_FAIL);
        }
    }

    private BaseResponse<TempRhAuthDetail> authRelateToTmpRhDevice(
            AiopsAuthStatus aiopsAuthStatus, TempRhAuthDevice authDevice, TempRhAuthDetail tad
    ) {
        //建立設備授權關聯
        BaseResponse<TempRhAuthDetail> tadRes = this.authDeviceToCustomer(aiopsAuthStatus, authDevice, tad);
        if (!tadRes.checkIsSuccess()) {
            return tadRes;
        }

        return tadRes;
    }

//    @Deprecated
//    private boolean isUnAuthRelateToTmpRhDevice(TempRhAuthDetail tad) {
//        //己經是未授權的資料, 不要重復未授權
//        boolean rule1 = tad.getDevice_status() == TempRhDeviceStatus.UNUSED;
//        boolean rule2 = !Optional.ofNullable(tad.getAuthorize()).orElse(false);
//        return rule1 && rule2;
//    }


    /**
     * 保存溫濕度实例 及授權設備
     *
     * @param openAuthSupplier  授權類型
     * @param incSourceDeviceId 設備ID (aiops_device.deviceId) (目前不使用)
     * @param authDevice        溫濕度設備實例
     * @return
     */
    public BaseResponse authDeviceToCustomer(
            Supplier<AiopsAuthStatus> openAuthSupplier, String incSourceDeviceId,
            TempRhAuthDevice authDevice, Supplier<AuthHistoryAction> authActionSupplier
    ) {

        AiopsAuthStatus aiopsAuthStatus = openAuthSupplier.get();

        //取得設備資料 (這部份, 客戶資料有可能為空, 所以要透過授權動作, 更新到 aiops_temp_rh_instance
        TempRhAuthDetail tad = Optional.ofNullable(tempRhInfoMapper.getThAuthDetailDataById(authDevice.getTadId()))
                .orElseThrow(() -> new RuntimeException("ThAuthDetailData Not Found Data for " + authDevice.getTadId()));

        switch (authActionSupplier.get()) {
            case OP_AUTH_ACTION: //授權類的操作

                // 從智能運維畫面, 點重新使用時, 需檢查設備是不是己被其它人使用或己作廢.
                // 1. 要判 EID

                String deviceCustomerServiceCode = Optional.ofNullable(tad.getCustomer_service_code()).orElse("");
                String authCustomerServiceCode = authDevice.getCustomerServiceCode();

                boolean isDoAuth = AiopsAuthStatus.AUTHED == aiopsAuthStatus;

                //檢查客代不相符
                boolean isDiffServerCode = StringUtils.isNotBlank(deviceCustomerServiceCode)
                        ? !deviceCustomerServiceCode.equals(authCustomerServiceCode) : false;

                boolean isUsingDevice = TempRhDeviceStatus.IN_USED == tad.getDevice_status();
                boolean isScrappedDevice = TempRhDeviceStatus.SCRAPPED == tad.getDevice_status();
                if (isDoAuth) {

                    if (!isDiffServerCode) {
                        //客代相同, 只檢查該設備是否己作廢
                        if (isScrappedDevice) {
                            return BaseResponse.error(ResponseCode.TMP_RH_DEVICE_BE_USE_OR_INVALID);
                        }
                    } else if (isDiffServerCode || isUsingDevice || isScrappedDevice) {
                        return BaseResponse.error(ResponseCode.TMP_RH_DEVICE_BE_USE_OR_INVALID);
                    }
                }

                BaseResponse<TempRhAuthDetail> tadRes = this.authRelateToTmpRhDevice(aiopsAuthStatus, authDevice, tad);
                if (!tadRes.checkIsSuccess()) {
                    return tadRes;
                }
                tad = tadRes.getData(); //取回最新的設備狀態

                break;
            case CHANGE_DEVICE_STATUS: //設備回收或報廢的動作.
                aiopsAuthStatus = AiopsAuthStatus.INVALID;
                break;
            case ADD_NEW_DEVICE:
                // 新增設備時, 有指定客戶, 所以要產生 aiops_instace(實例) 的資料, 且只能是未授權的狀態
                aiopsAuthStatus = AiopsAuthStatus.UNAUTH;
                break;
        }

        String sourceDeviceId = tad.getId() + "";

        //setp0.1 先處理之前己配到的收集項
        //2023-05-09 修正預警產生的問題 for 同一個溫濕度設備, 給不同 eid 使用時
        if (AiopsAuthStatus.AUTHED == aiopsAuthStatus || AiopsAuthStatus.UNAUTH == aiopsAuthStatus) {
            //查找該設備下, eid 不相同的資料, 並刪除這個設備下的收集項. (因為溫顯度的設備是共用的)
            List<Map<String, Object>> deviceCollects = tempRhInfoMapper.getDeviceCollect(sourceDeviceId, tad.getEid());
            deviceCollects.forEach(row -> {

                long accId = Optional.ofNullable((Long)row.get("accId")).orElse(0L);
                long adcdId = Optional.ofNullable((Long)row.get("adcdId")).orElse(0L);
                String scopeId = Objects.toString(row.get("scopeId"), "");

                //取得之前配的預警項
                Map<String,Object> queryMap = new HashMap<>();
                queryMap.put("accId", accId);
                queryMap.put("adcdId", adcdId);
                List<CollectWarning> collectWarningList = deviceV2Mapper.getProductCollectWarningList(queryMap);

                //刪除這個設備有掛勾到的收集項
                deviceService.deleteCollectDetailForDevice(adcdId, accId, sourceDeviceId, scopeId, collectWarningList);
            });
        }

        //step0.2 更新 設備上的資訊
        // 2022-05-26 討論後, 先用 aiops_temp_rh_instance.id, 來當 deviceId, 這樣預警列表的部份, 可以走現有的流程
        // 要再自建 aiops_device 的資料, 再傳入 sourceDeviceId.
        BaseResponse addVirtualDeviceRes = this.saveVirtualDeviceInfo(tad, sourceDeviceId, aiopsAuthStatus);
        if (!addVirtualDeviceRes.checkIsSuccess()) {
            return addVirtualDeviceRes;
        }

        //step0.3 串接實例的相關流程
        //查找之前是不是己經建立過實例 ID.
        BaseResponse<Long> aiopsInstanceRes = instanceService.getAiIdByAiopsItemId(tad.getTmp_rh_id());
        if (!aiopsInstanceRes.checkIsSuccess()) {
            return aiopsInstanceRes;
        }

        //创建运维项目上下文
        AiopsItemContext aic = new AiopsItemContext("TMP_RH", tad.getTmp_rh_id(), sourceDeviceId); //aiopsItem 先暫時寫死.
        aic.setAiId(aiopsInstanceRes.getData());
        aic.setCreateAdcdBySync(Boolean.TRUE); //是通过同步线程创建集合
//        aic.setNeedFixExecParamsContent(aiopsSNMPInstance.getNeedFixExecParamsContent());
//        aic.setOriExecParamsContent(aiopsSNMPInstance.getOriExecParamsContent());

        Long eid = tad.getEid();
        List<AiopsItemContext> aicList = Stream.of(aic).collect(Collectors.toList());

        //创建运维实例(如果有的话)
        BaseResponse response = authorizeService.checkAndCreateAiopsInstanceByAiopsItemContext(eid, aiopsAuthStatus, aicList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        if (aiopsAuthStatus == null) {
            return response;
        }

//        BaseResponse modifyInstanceRes = authorizeService.modifyInstanceAuthStatus(eid, aiopsAuthStatus, aicList);
//        if (modifyInstanceRes.checkIsSuccess()) {
//            //如果取消授權, 則要關閉 這個溫濕度設備, 跟實例的關聯 (會影響授權數統計)
//            if (AiopsAuthStatus.UNAUTH == aiopsAuthStatus) {
//                tempRhInfoMapper.cancelRelateAiopsInstance(tad.getTmp_rh_id());
//            }
//        }
//        return modifyInstanceRes;
        return authorizeService.modifyInstanceAuthStatus(eid, aiopsAuthStatus, aicList);
    }

    /**
     * 新增-溫濕度設備
     *
     * @param deviceInfo
     * @return
     */
    @Transactional
    public BaseResponse addDeviceInfo(TempRhDeviceInfo deviceInfo) {
        TempRhAuthDetail tad = processTempRhAuthDeviceInfo(deviceInfo);
        tad.setId(SnowFlake.getInstance().newId());


        Optional<BaseResponse> errOpt = this.basicDataCheck(-999999, tad.getDevice_serial(), tad.getDevice_name());
        if (errOpt.isPresent()) {
            return errOpt.get();
        }

        boolean testConnect = tempRhHostInfoService.deviceTestConnect(tad);
        if (testConnect) {
            // add to table: aiops_temp_rh_instance (溫濕度實例)
            long result = tempRhInfoMapper.insertTempRhAuthDetail(tad);

            //建立跟客戶興設備的關聯, 只有在己使用時, 才能建立
            tempRhCustomerInfoService.changeDeviceLocation(tad, Optional.ofNullable(deviceInfo.getDevice_location()),
                    Optional.ofNullable(deviceInfo.getRemark()), Optional.ofNullable(deviceInfo.getCustomerNotes())
            );

            if (TempRhDeviceStatus.IN_USED == tad.getDevice_status()) {
                this.saveTempRheDeviceAuthHistory(tad, recAddDeviceTimestamp(tad));

                //建立實例
                return this.updateAiopsInstanceStatus(tad, AuthHistoryAction.ADD_NEW_DEVICE);
            }

            return BaseResponse.ok(result);
        } else {
            return BaseResponse.error(ResponseCode.TMP_RH_DEVICE_TEST_CONNECT_FAIL);
        }
    }

    /**
     * 設備基礎資料檢查.
     *
     * @param tdId
     * @param device_serial
     * @param device_name
     * @return
     */
    private Optional<BaseResponse> basicDataCheck(long tdId, String device_serial, String device_name) {
        List<String> existData = tempRhInfoMapper.touchTempRhDeviceInfoIsExist(tdId, device_serial, device_name);
        if (!existData.isEmpty()) {
            String errorCode = existData.get(0);
            if (ResponseCode.TMP_RH_DEVICE_SERIAL_DUPLICATE.isSameCode(errorCode)) {
                return Optional.ofNullable(BaseResponse.error(ResponseCode.TMP_RH_DEVICE_SERIAL_DUPLICATE));
            }
            if (ResponseCode.TMP_RH_DEVICE_NAME_DUPLICATE.isSameCode(errorCode)) {
                return Optional.ofNullable(BaseResponse.error(ResponseCode.TMP_RH_DEVICE_NAME_DUPLICATE));
            }
        }
        return Optional.empty();
    }

    /**
     * 更新-溫濕度設備
     *
     * @param tdId
     * @param deviceInfo
     * @return
     */
    public BaseResponse updateDeviceInfo(long tdId, TempRhDeviceUpdateAction updateAction, TempRhDeviceInfo deviceInfo) {

        if (updateAction.isCheckData()) {
            Optional<BaseResponse> errOpt = this.basicDataCheck(tdId, null, deviceInfo.getDevice_name());
            if (errOpt.isPresent()) {
                return errOpt.get();
            }
        }

        return this.updateDevice(
                tdId,
                tad -> BaseResponse.ok(),
                tad -> {
                    //變更放置點.
                    if (TempRhDeviceUpdateAction.MIS_ACTION == updateAction) {
                        tempRhCustomerInfoService.changeDeviceLocation(tdId, deviceInfo);
                    }
                    return processTempRhAuthDeviceInfo(tad, updateAction.filterData(deviceInfo, tad.getDevice_status()));
                },
                (tad, isDeviceStatusChange) -> {
                    /*
                     * 2022-06-10 開會決議.
                     *  1. 后台设备总表，编辑时不记录租用历程
                     *  2. 编辑目前只能编辑名称呢  所以不记录
                     *  3. 放置點改存 temp_rh_customer_info, 且也要記錄變更歷程
                     * 2011-06-13
                     *   改存 aiops_temp_rh_instance.customer_use_device_time 的時間, \
                     *   且如果之前沒存過歷程, 則才存到歷程表.
                     * */
                    if (TempRhDeviceUpdateAction.MIS_ACTION == updateAction) {
                        //檢查是不是己有記錄 "設備己使用的時間"
                        boolean isNeedAddDeviceTimestamp = this.isNeedAddDeviceTimestamp(tad);

                        //save history.
                        if (isNeedAddDeviceTimestamp) {
                            //記錄添加設備時間 for history.
                            this.saveTempRheDeviceAuthHistory(tad, recAddDeviceTimestamp(tad));
                        }
                    }
                    if (TempRhDeviceUpdateAction.BACKSTAGE_MANAGEMENT_ACTION == updateAction) {
                        /*
                         *  2022-06-15 慧娟提出的修改 logic.
                         * 1. aiops_device.deviceName = aiops_temp_rh_instance.device_name
                         * */
                        String sourceDeviceId = tad.getId() + "";
                        Optional.ofNullable(deviceMapper.selectAdIdByDeviceId(sourceDeviceId))
                                .ifPresent(adId -> {
                                    AiopsKitDevice device = new AiopsKitDevice();
                                    device.setId(adId);
                                    device.setDeviceName(tad.getDevice_name());
                                    deviceMapper.updateDevice(device);
                                });
                    }
                }
        );
    }

    /**
     * 添加设备时间
     * 1、如果添加设备时，未选择客代，这个时间置空
     * 2、如果这台设备已经添加了，未授权客户，那当授权客户时，就要记录这个时间了
     *
     * @param tad
     * @return
     */
    private Function<TempRheDeviceAuthHistory, TempRheDeviceAuthHistory> recAddDeviceTimestamp(TempRhAuthDetail tad) {
        return history -> {
            if (StringUtils.isNotBlank(tad.getCustomer_service_code())) {
                // 應該是未授權且被使用中, 就算添加设备时间, 有授權時, EAI 才進行採集.
                if (TempRhDeviceStatus.IN_USED == tad.getDevice_status()) {
                    history.setAdd_device_timestamp(tad.getCustomer_use_device_time());
                }
            }
            return history;
        };
    }

    /**
     * 檢查是不是要記錄-設備被使用的初始時間
     * @param tad
     * @return
     */
    private boolean isNeedAddDeviceTimestamp(TempRhAuthDetail tad) {
        //檢查是不是己有記錄 "設備己使用的時間"
        return tempRhInfoMapper.checkHasAddDeviceTimestamp(
                tad.getDevice_serial(), tad.getCustomer_use_device_time()
        ).isEmpty();

    }

    private TempRhAuthDetail processTempRhAuthDeviceInfo(TempRhDeviceInfo deviceInfo) {
        return processTempRhAuthDeviceInfo(null, deviceInfo);
    }

    private TempRhAuthDetail processTempRhAuthDeviceInfo(TempRhAuthDetail tad, TempRhDeviceInfo deviceInfo) {
        tad = Optional.ofNullable(tad).orElseGet(() -> {
            TempRhAuthDetail tempRhAuthDetail = new TempRhAuthDetail();
            tempRhAuthDetail.setId(SnowFlake.getInstance().newId());
            tempRhAuthDetail.setDevice_status(TempRhDeviceStatus.UNUSED);
            tempRhAuthDetail.setAuthorize(null);
            return tempRhAuthDetail;
        });

        if (StringUtils.isNotBlank(deviceInfo.getDevice_serial())) {
            tad.setDevice_serial(deviceInfo.getDevice_serial());
        }
        if (StringUtils.isNotBlank(deviceInfo.getDevice_name())) {
            tad.setDevice_name(deviceInfo.getDevice_name());
        }

        //主機平台id
        if (StringUtils.isNotBlank(deviceInfo.getThiId())) {
            tad.setThiid(deviceInfo.getThiId());
        }

        if (StringUtils.isNotBlank(deviceInfo.getCustomer_service_code())) {
            //FIXME 要檢查合約資料, 如果符合, 要直接授權 (only in add new device)

            Map<String, Object> supplierInfo = Optional.ofNullable(
                    tempRhInfoMapper.getSupplierInfo(deviceInfo.getCustomer_service_code())
            ).orElseThrow(() -> new RuntimeException("Not Found Data for " + deviceInfo.getCustomer_service_code()));

            //有選客代, 表示己使用, 但不表示己授權
            tad.setDevice_status(TempRhDeviceStatus.IN_USED);
            tad.setAuthorize(Boolean.FALSE);
            tad.setEid((Long) supplierInfo.get("eid"));
            tad.setSid((Long) supplierInfo.get("sid"));
            tad.setCustomer_service_code(deviceInfo.getCustomer_service_code());
            tad.setCustomer_use_device_time(new Date()); //客戶使用時間

            //產生溫濕度的業務主鍵 - 用來反查用
            tad.createTemRhId();
        }
        return tad;
    }


    /**
     * 更新設備狀態
     *
     * @param tdId
     * @return
     */
    public BaseResponse updateDeviceStatus(long tdId, TempRhDeviceStatus deviceStatus) {
        return updateDevice(
                tdId,
                tad -> {
                    if (TempRhDeviceStatus.UN_SCRAPPED == deviceStatus) {
                        //如果是取消作廢, 不需去變更實例的狀態.
                        return BaseResponse.ok();
                    } else {
                        if (TempRhDeviceStatus.SCRAPPED == deviceStatus
                                && tad.getDevice_status() == TempRhDeviceStatus.UNUSED) {
                            //如果是作廢, 且己經是未使用, 則不需去變更實例的狀態.
                            return BaseResponse.ok();
                        }
                        BaseResponse uasRes =  this.updateAiopsInstanceStatus(tad, AuthHistoryAction.CHANGE_DEVICE_STATUS); //unused 會用到
                        if (uasRes.checkIsSuccess()) {
                            /*
                             * 有可能為 TempRhDeviceStatus.SCRAPPED == deviceStatus && tad.getDevice_status() == TempRhDeviceStatus.IN_USED
                             * or TempRhDeviceStatus.UNUSED == deviceStatus
                             * 都是要記錄歷程
                             */
                            Boolean isDeviceStatusChange = (tad.getDevice_status() == TempRhDeviceStatus.IN_USED);
                            uasRes.setData(isDeviceStatusChange);
                        }
                        return uasRes;
                    }
                },
                tad -> {
                    tad.setDevice_status(deviceStatus);
                    tad.setCustomer_use_device_time(null); //清除設備被使用的時間
                    switch (deviceStatus) {
                        case SCRAPPED:
                            tad.setScrapped_time(new Date()); //作廢時間
                            tad.setAuthorize(null); //当设备的设备状态为未使用时，则授权状态的值应该为空
                            break;
                        case UNUSED:
                            tad.setAuthorize(null); //当设备的设备状态为未使用时，则授权状态的值应该为空
                            break;
                        case UN_SCRAPPED:
                            // 取消報廢變更成為未使用
                            tad.setDevice_status(TempRhDeviceStatus.UNUSED);
                            break;
                    }
                    return tad;
                },
                (tad, isDeviceStatusChange) -> {

                    if (TempRhDeviceStatus.UNUSED == deviceStatus || isDeviceStatusChange) {
                        saveTempRheDeviceAuthHistory(
                                tad,
                                history -> {
                                    if (TempRhDeviceStatus.UNUSED == deviceStatus
                                                    || TempRhDeviceStatus.SCRAPPED == deviceStatus) {
                                        history.setReturn_timestamp(new Date()); // 回收時間
                                    }
                                    //回填第一次設備綁定的時間
                                    Optional.ofNullable(
                                            tempRhInfoMapper.getMaxAddDeviceTimestamp(history.getEid(), history.getDevice_serial())
                                    ).ifPresent(addDeviceDate -> history.setAdd_device_timestamp(addDeviceDate));
                                    return history;
                                },
                                () -> this.cancelRelateToTmpRhDevice(tad) //新增歷程後, 如果未授權, 則直接取消關聯客戶
                        );
                    } else {
                        // 作廢跟取消報廢, 不記歷史
                        switch (deviceStatus) {
                            case SCRAPPED:
                                /*
                                 * 只有 TempRhDeviceStatus.SCRAPPED == deviceStatus && tad.getDevice_status() == TempRhDeviceStatus.IN_USED
                                 * 的情況, 才需要記歷程,
                                 * 會交由 isDeviceStatusChange = true 來決定
                                 * */
                            case UN_SCRAPPED:
                                this.cancelRelateToTmpRhDevice(tad); //如果未授權, 則直接取消關聯客戶
                                return;
                        }
                    }
                }
        );
    }

    private void cancelRelateToTmpRhDevice(TempRhAuthDetail tad) {
        //如果未授權, 則直接取消關聯客戶
        if (!Optional.ofNullable(tad.getAuthorize()).orElse(false)) {
            tad.setEid(null);
            tad.setSid(null);
            tad.setCustomer_service_code(null);
            tempRhInfoMapper.updateTempRhAuthDetail(tad);
        }
    }

    private BaseResponse updateAiopsInstanceStatus(TempRhAuthDetail tad, AuthHistoryAction authHistoryAction) {
        TempRhAuthDevice trad = new TempRhAuthDevice();
        trad.setThiId(tad.getThiid());
        trad.setDevice_location(
                tempRhCustomerInfoService.getUseCustomerInfo(tad).getDevice_location()
        );
        trad.setTadId(tad.getId());
        trad.setCustomerServiceCode(tad.getCustomer_service_code());

        boolean isAddNewDevice = AuthHistoryAction.ADD_NEW_DEVICE == authHistoryAction;
        if (isAddNewDevice) {
            // 新增設備時, 要直接新增實例, 且是未授權
            return this.authDeviceToCustomer(
                    () -> AiopsAuthStatus.UNAUTH, "", trad,
                    () -> AuthHistoryAction.ADD_NEW_DEVICE
            );
        }

        boolean isChangeDeviceStatus = AuthHistoryAction.CHANGE_DEVICE_STATUS == authHistoryAction;
        if (isChangeDeviceStatus) {
            //如果是回收/報廢 設備, 都是直接作廢實例授權, 不管之前的狀態
            return this.authDeviceToCustomer(
                    () -> AiopsAuthStatus.INVALID, "", trad,
                    () -> AuthHistoryAction.CHANGE_DEVICE_STATUS
            );
        }

//        boolean isDoUnAuth = deviceStatus == TempRhDeviceStatus.UNUSED;
//        isDoUnAuth = !isDoUnAuth ? deviceStatus == TempRhDeviceStatus.SCRAPPED : isDoUnAuth;
//        if (isDoUnAuth) {
//            //如果是回收/報廢 設備, 都是直接作廢實例授權, 不管之前的狀態
//            return this.authDeviceToCustomer(
//                    () -> AiopsAuthStatus.INVALID, "", trad,
//                    () -> AuthHistoryAction.CHANGE_DEVICE_STATUS
//            );
//        }
        return BaseResponse.ok();
    }

    /**
     * 更新設備狀態
     *
     * @param tadId
     * @param history
     * @return
     */
    private BaseResponse updateDevice(
            long tadId, Function<TempRhAuthDetail, BaseResponse> changeDeviceStatus,
            Function<TempRhAuthDetail, TempRhAuthDetail> changeAction,
            HistoryConsumer history) {

        TempRhAuthDetail tad = Optional.ofNullable(tempRhInfoMapper.getThAuthDetailDataById(tadId))
                .orElseThrow(() -> new RuntimeException("Not Found Data for " + tadId));

        BaseResponse upsRes = changeDeviceStatus.apply(tad);
        if (!upsRes.checkIsSuccess()) {
            return upsRes;
        }
        boolean isDeviceStatusChange = false;
        if (upsRes.getData() != null) {
            if (upsRes.getData() instanceof Boolean) {
                isDeviceStatusChange = (Boolean)upsRes.getData();
            }
        }


        //更新為新的值.
        tad = changeAction.apply(tad);

        long result = tempRhInfoMapper.updateTempRhAuthDetail(tad);
        if (result > 0) {
            history.accept(tad, isDeviceStatusChange);
        }
        return BaseResponse.ok(result);
    }

    private interface HistoryConsumer {
        void accept(TempRhAuthDetail tad, boolean isDeviceStatusChange);
    }

    //-------------------------------------

    /**
     * 記錄設備使用歷程
     *
     * @param mainRow
     * @return
     */
    private long saveTempRheDeviceAuthHistory(
            TempRhAuthDetail mainRow, Function<TempRheDeviceAuthHistory, TempRheDeviceAuthHistory> adjust
            ,Runnable ...afterRuns
    ) {
        TempRheDeviceAuthHistory history = new TempRheDeviceAuthHistory();
        history.setSid(mainRow.getSid());
        history.setEid(mainRow.getEid());
        history.setCustomer_service_code(mainRow.getCustomer_service_code());
        history.setDevice_serial(mainRow.getDevice_serial());
        history.setDevice_status(mainRow.getDevice_status());
        history.setTmp_rh_id(mainRow.getTmp_rh_id());

        history.setDevice_location(
                tempRhCustomerInfoService.getUseCustomerInfo(mainRow).getDevice_location()
        );

        Optional<AuthoredUser> opt = this.getLogInUser();
        if (opt.isPresent()) {
            AuthoredUser authoredUser = opt.get();
            history.setOp_user_id(authoredUser.getUserId());
            history.setOp_user_name(authoredUser.getUserName());
        } else {
            history.setOp_user_id("N/A");
            history.setOp_user_name("N/A");
        }

        //調整 添加设备时间 及 回收设备时间
        history = adjust.apply(history);
        Arrays.stream(afterRuns).forEach(r -> r.run());
        long result = tempRhInfoMapper.insertTempRheDeviceAuthHistory(history);
        return result;
    }

    /**
     * 取得設備使用歷程
     *
     * @param deviceSerial
     * @return
     */
    public List<QueryTempRheDeviceAuthHistory> getDeviceHistory(String deviceSerial) {
        return tempRhInfoMapper.getHistoryBySerialAndStatus(deviceSerial, null);
    }

    private Optional<AuthoredUser> getLogInUser() {
        try {
            String token = RequestUtil.getHeaderToken();

            if (StringUtils.isBlank(token)) {
                return Optional.empty();
            }

            String authoredUserStr = aioUserFeignClient.getUserInfoJsonByToken(token);
            if (StringUtils.isBlank(authoredUserStr)) {
                return Optional.empty();
            }

            AuthoredUser authoredUser = SerializeUtil.JsonDeserialize(authoredUserStr, AuthoredUser.class);
            return Optional.ofNullable(authoredUser);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    //-------------------------------------

    /**
     * save pic location.
     *
     * @param tadId
     * @param displayPics
     * @return
     */
    public List<TempRhDeviceUploadPic> saveTempRhDeviceUploadPic(long tadId, long eid, UploadPicInfo[] displayPics) {
        Map<String, Object> custInfoMap = Optional.ofNullable(
                        tempRhInfoMapper.getThAuthDetailDataByIdForUploadPic(tadId, eid)
                )
                .orElseThrow(() -> new RuntimeException("Not Found Data for " + tadId));

        return Arrays.asList(displayPics).parallelStream()
                .map(picInfo -> {
                    TempRhDeviceUploadPic picRow = new TempRhDeviceUploadPic();
                    picRow.setId(SnowFlake.getInstance().newId());
                    picRow.setTadid(tadId);
                    picRow.setDisplay_pic_name(picInfo.getDisplay_pic_name());
                    picRow.setDisplay_pic_url(picInfo.getDisplay_pic_url());
                    picRow.setCustomer_service_code(
                            Optional.ofNullable(custInfoMap.get("customer_service_code"))
                                    .orElse("-999999")
                                    .toString()
                    );
                    return picRow;
                })
                .peek(picRow -> tempRhInfoMapper.insertTempRhDeviceUploadPic(picRow))
                .collect(Collectors.toList());
    }

    public List<Long> deleteTempRhDeviceUploadPic(long tadId, String customerServiceCode, String[] picIds) {
        return Arrays.asList(picIds).parallelStream()
                .map(pidId -> StringUtils.isNotBlank(pidId) ? pidId : "-1")
                .map(pidId -> new Long(pidId))
                .map(pidId -> {
                    TempRhDeviceUploadPic picRow = new TempRhDeviceUploadPic();
                    picRow.setId(pidId);
                    picRow.setTadid(tadId);
                    picRow.setCustomer_service_code(customerServiceCode);
                    return picRow;
                })
                .map(picRow -> tempRhInfoMapper.deleteTempRhDeviceUploadPic(picRow))
                .collect(Collectors.toList());
    }

    /**
     * 取得己上傳的圖片列表
     *
     * @param tadId
     * @param customerServiceCode
     * @return
     */
    public List<TempRhDeviceUploadPic> getDeviceUploadPic(long tadId, String customerServiceCode) {
        return tempRhInfoMapper.getTempRhDeviceUploadPic(tadId, customerServiceCode);
    }

    /**
     * 取得溫濕度設備的數量
     *
     * @param queryMap 查詢條件
     * @return
     */
    public TempRhDeviceCountInfo queryAllDeviceCountInfo(Map<String, Object> queryMap ) {
        //查询
        List<QueryTempRhAuthDetail> data = tempRhInfoMapper.queryAllDeviceAndAuthInfo(queryMap); //query data
        //取得各設備最後更新時間
        TempRhDeviceCountInfo deviceCountInfo= new TempRhDeviceCountInfo();
        if (!data.isEmpty()) {
            long degreesDeviceCount = 0;
            long wetnessDeviceCount = 0;
            long degreesDeviceAbnormalCount = 0;
            long wetnessDeviceAbnormalCount = 0;
            //取得各設備最後更新時間
            Map<String, HiveEaiTempRhBizInfo> lastCollectMap = callStarRocksApiService.getAllLastCollectInfo();
            data.stream()
                .forEach(row -> {Optional.ofNullable(lastCollectMap.get(row.getKey()))
                        .ifPresent(htb -> {
                            row.setLast_device_sync_time(htb.getCollectedTime());
                            row.setLast_device_collect_time(htb.getDeviceCollectTime());
                            row.setDegrees(htb.getDegrees());
                            row.setWetness(htb.getWetness());
                        });
                    // 找出溫度閥值
                    row.setDegreesThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"tmp_warning"));
                    // 找出濕度閥值
                    row.setWetnessThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"rh_waring"));
                });

            degreesDeviceCount = data.stream().count();//.filter(row->!StringUtils.isEmpty(row.getDegrees())).count();
            wetnessDeviceCount = data.stream().count();//.filter(row->!StringUtils.isEmpty(row.getWetness())).count();
            degreesDeviceAbnormalCount = data.stream().filter(row->!StringUtils.isEmpty(row.getDegrees()) && !StringUtils.isEmpty(row.getDegreesThreshold()) && Float.parseFloat(row.getDegrees()) > Float.parseFloat(row.getDegreesThreshold()) ).count();
            wetnessDeviceAbnormalCount = data.stream().filter(row->!StringUtils.isEmpty(row.getWetness()) && !StringUtils.isEmpty(row.getWetnessThreshold()) && Float.parseFloat(row.getWetness()) > Float.parseFloat(row.getWetnessThreshold()) ).count();

            deviceCountInfo.setDegrees_device_count(degreesDeviceCount);
            deviceCountInfo.setWetness_device_count(wetnessDeviceCount);
            deviceCountInfo.setWetness_device_abnormal_count(degreesDeviceAbnormalCount);
            deviceCountInfo.setWetness_device_abnormal_count(wetnessDeviceAbnormalCount);
            deviceCountInfo.setEid(queryMap.get("eid").toString());

            System.out.println("getTempRhDeviceDetailById().result: get data");
            return deviceCountInfo;
        }
        return deviceCountInfo;
    }

    /**
     * 取得某客代下所有的溫濕度設備曲線資訊
     *
     * @param queryMap 查詢條件
     * @return
     */
    public List<QueryTempRhCureInfoList> queryAllDeviceCurveInfo(Map<String, Object> queryMap ) {
        //查询
        List<QueryTempRhAuthDetail> data = tempRhInfoMapper.queryAllDeviceAndAuthInfo(queryMap); //query data

        List<QueryTempRhCureInfoList> queryTempRhCureInfoList = new ArrayList<>();
        Map<String, String> queryCureMap = new HashMap<>();
        queryCureMap.put("eid",queryMap.get("eid").toString());
        queryCureMap.put("device_id"," ");
        queryCureMap.put("eai_trbi_device_serial"," ");
        queryCureMap.put("displayType",queryMap.get("displayType").toString());
        queryCureMap.put("s_date",queryMap.getOrDefault("s_date"," ").toString());
        queryCureMap.put("e_date",queryMap.getOrDefault("e_date"," ").toString());
        Map<String, QueryTempRhCureInfo> curveInfoMap = callStarRocksApiService.getTempRhCurveInfo(queryCureMap);
        List<QueryTempRhCureInfo> curveInfoList = new ArrayList<>(curveInfoMap.values());
        if (!data.isEmpty()) {
            data.stream()
                .forEach(row -> {
                    QueryTempRhCureInfoList tempQueryTempRhCureInfoList = new QueryTempRhCureInfoList();

                    tempQueryTempRhCureInfoList.setEid(row.getEid().toString());
                    tempQueryTempRhCureInfoList.setDeviceId(row.getDeviceId());
                    tempQueryTempRhCureInfoList.setDeviceSerial(row.getDevice_serial());
                    List<QueryTempRhCureInfo> rowCurveInfoList = curveInfoList.stream()
                            .filter(x -> x.getEid().equals(row.getEid().toString())
                                            && x.getDeviceId().equals(row.getDeviceId())
                                            && x.getDeviceSerial().equals(row.getDevice_serial())
                            ).collect(Collectors.toList());
                    rowCurveInfoList.sort((a, b) -> a.getCollectedTime().compareTo(b.getCollectedTime()));
                    tempQueryTempRhCureInfoList.setQueryTempRhCureInfo(rowCurveInfoList);
                    // 找出溫度閥值
                    tempQueryTempRhCureInfoList.setDegreesThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"tmp_warning"));
                    // 找出濕度閥值
                    tempQueryTempRhCureInfoList.setWetnessThreshold(tempRhInfoMapper.getThresholdValue(row.getAdId(),"rh_waring"));
                    queryTempRhCureInfoList.add(tempQueryTempRhCureInfoList);
                });
            System.out.println("getTempRhDeviceDetailById().result: get data");
            return queryTempRhCureInfoList;
        }
        System.out.println("getTempRhDeviceDetailById().result: no data");
        return queryTempRhCureInfoList;
    }

    /**
     * 取得某溫濕度設備的曲線資料
     *
     * @param queryMap 查詢條件
     * @return
     */
    public QueryTempRhCureInfoList queryDeviceCurveInfo(String adId, Map<String, String> queryMap ) {
        //查询
        QueryTempRhCureInfoList queryTempRhCureInfoList = new QueryTempRhCureInfoList();
        queryTempRhCureInfoList.setEid(queryMap.get("eid").toString());
        queryTempRhCureInfoList.setDeviceId(queryMap.get("device_id").toString());
        queryTempRhCureInfoList.setDeviceSerial(queryMap.get("eai_trbi_device_serial").toString());
        Map<String, QueryTempRhCureInfo> curveInfoMap = callStarRocksApiService.getTempRhCurveInfo(queryMap);
        List<QueryTempRhCureInfo> curveInfoList = new ArrayList<>(curveInfoMap.values());
        //List<QueryTempRhCureInfo> values = curveInfoMap.values().stream().collect(Collectors.toList());
        curveInfoList.sort((a,b)->a.getCollectedTime().compareTo(b.getCollectedTime()));
        queryTempRhCureInfoList.setQueryTempRhCureInfo(curveInfoList);
        // 找出溫度閥值
        queryTempRhCureInfoList.setDegreesThreshold(tempRhInfoMapper.getThresholdValue(adId,"tmp_warning"));
        // 找出濕度閥值
        queryTempRhCureInfoList.setWetnessThreshold(tempRhInfoMapper.getThresholdValue(adId,"rh_waring"));

        return queryTempRhCureInfoList;
    }
}
